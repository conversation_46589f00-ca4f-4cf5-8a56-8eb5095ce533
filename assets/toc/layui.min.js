!function(p){"use strict";function i(){this.v="2.9.18"}function h(e,t){t=t||"log",p.console&&console[t]&&console[t]("layui error hint: "+e)}var e,y=p.document,m={modules:{},status:{},timeout:10,event:{}},t=p.LAYUI_GLOBAL||{},g=(e=y.currentScript&&"SCRIPT"===y.currentScript.tagName.toUpperCase()?y.currentScript.src:function(){for(var e,t=y.getElementsByTagName("script"),n=t.length-1,i=n;0<i;i--)if("interactive"===t[i].readyState){e=t[i].src;break}return e||t[n].src}(),m.dir=t.dir||e.substring(0,e.lastIndexOf("/")+1)),v="undefined"!=typeof opera&&"[object Opera]"===opera.toString(),x=m.builtin={lay:"lay",layer:"layer",laydate:"laydate",laypage:"laypage",laytpl:"laytpl",form:"form",upload:"upload",dropdown:"dropdown",transfer:"transfer",tree:"tree",table:"table",treeTable:"treeTable",element:"element",rate:"rate",colorpicker:"colorpicker",slider:"slider",carousel:"carousel",flow:"flow",util:"util",code:"code",jquery:"jquery",all:"all","layui.all":"layui.all"},c=(i.prototype.cache=m,i.prototype.define=function(e,i){return"function"==typeof e&&(i=e,e=[]),this.use(e,function(){function n(e,t){layui[e]=t,m.status[e]=!0}return"function"==typeof i&&i(function(e,t){n(e,t),m.callback[e]=function(){i(n)}}),this},null,"define"),this},i.prototype.use=function(n,e,t,i){var a,o,r=this,l=m.dir=m.dir||g,s=y.getElementsByTagName("head")[0],c=(n="string"==typeof n?[n]:"function"==typeof n?(e=n,["all"]):n,p.jQuery&&jQuery.fn.on&&(r.each(n,function(e,t){"jquery"===t&&n.splice(e,1)}),layui.jquery=layui.$=jQuery),n[0]),d=0;function u(e,t){var n="PLaySTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/;"load"!==e.type&&!n.test((e.currentTarget||e.srcElement).readyState)||(m.modules[c]=t,s.removeChild(a),function e(){return++d>1e3*m.timeout/4?h(c+" is not a valid module","error"):void(m.status[c]?f():setTimeout(e,4))}())}function f(){t.push(layui[c]),1<n.length?r.use(n.slice(1),e,t,i):"function"==typeof e&&(layui.jquery&&"function"==typeof layui.jquery&&"define"!==i?layui.jquery(function(){e.apply(layui,t)}):e.apply(layui,t))}return t=t||[],m.host=m.host||(l.match(/\/\/([\s\S]+?)\//)||["//"+location.host+"/"])[0],0===n.length||layui["layui.all"]&&x[c]?f():(o=(o=(x[c]?l+"modules/":!/^\{\/\}/.test(r.modules[c])&&m.base||"")+(r.modules[c]||c)+".js").replace(/^\{\/\}/,""),!m.modules[c]&&layui[c]&&(m.modules[c]=o),m.modules[c]?function e(){return++d>1e3*m.timeout/4?h(c+" is not a valid module","error"):void("string"==typeof m.modules[c]&&m.status[c]?f():setTimeout(e,4))}():((a=y.createElement("script")).async=!0,a.charset="utf-8",a.src=o+((l=!0===m.version?m.v||(new Date).getTime():m.version||"")?"?v="+l:""),s.appendChild(a),!a.attachEvent||a.attachEvent.toString&&a.attachEvent.toString().indexOf("[native code")<0||v?a.addEventListener("load",function(e){u(e,o)},!1):a.attachEvent("onreadystatechange",function(e){u(e,o)}),m.modules[c]=o)),r},i.prototype.disuse=function(e){var n=this;return e=n.isArray(e)?e:[e],n.each(e,function(e,t){m.status[t],delete n[t],delete x[t],delete n.modules[t],delete m.status[t],delete m.modules[t]}),n},i.prototype.getStyle=function(e,t){return(e=e.currentStyle||p.getComputedStyle(e,null))[e.getPropertyValue?"getPropertyValue":"getAttribute"](t)},i.prototype.link=function(i,a,e){var o=this,t=y.getElementsByTagName("head")[0],n=y.createElement("link"),r="layuicss-"+((e="string"==typeof a?a:e)||i).replace(/\.|\//g,""),l="creating",s=0;return n.href=i+(m.debug?"?v="+(new Date).getTime():""),n.rel="stylesheet",n.id=r,n.media="all",y.getElementById(r)||t.appendChild(n),"function"==typeof a&&function e(t){var n=y.getElementById(r);return++s>1e3*m.timeout/100?h(i+" timeout"):void(1989===parseInt(o.getStyle(n,"width"))?(t===l&&n.removeAttribute("lay-status"),n.getAttribute("lay-status")===l?setTimeout(e,100):a()):(n.setAttribute("lay-status",l),setTimeout(function(){e(l)},100)))}(),o},i.prototype.addcss=function(e,t,n){return layui.link(m.dir+"css/"+e,t,n)},m.callback={},i.prototype.factory=function(e){if(layui[e])return"function"==typeof m.callback[e]?m.callback[e]:null},i.prototype.img=function(e,t,n){var i=new Image;if(i.src=e,i.complete)return t(i);i.onload=function(){i.onload=null,"function"==typeof t&&t(i)},i.onerror=function(e){i.onerror=null,"function"==typeof n&&n(e)}},i.prototype.config=function(e){for(var t in e=e||{})m[t]=e[t];return this},i.prototype.modules=function(){var e,t={};for(e in x)t[e]=x[e];return t}(),i.prototype.extend=function(e){for(var t in e=e||{})this[t]||this.modules[t]?h(t+" Module already exists","error"):this.modules[t]=e[t];return this},i.prototype.router=i.prototype.hash=function(e){var n={path:[],search:{},hash:((e=e||location.hash).match(/[^#](#.*$)/)||[])[1]||""};return/^#\//.test(e)&&(e=e.replace(/^#\//,""),n.href="/"+e,e=e.replace(/([^#])(#.*$)/,"$1").split("/")||[],this.each(e,function(e,t){/^\w+=/.test(t)?(t=t.split("="),n.search[t[0]]=t[1]):n.path.push(t)})),n},i.prototype.url=function(e){var a,t;return{pathname:(e?((e.match(/\.[^.]+?\/.+/)||[])[0]||"").replace(/^[^\/]+/,"").replace(/\?.+/,""):location.pathname).replace(/^\//,"").split("/"),search:(a={},t=(e?((e.match(/\?.+/)||[])[0]||"").replace(/\#.+/,""):location.search).replace(/^\?+/,"").split("&"),this.each(t,function(e,t){var n=t.indexOf("="),i=n<0?t.substr(0,t.length):0!==n&&t.substr(0,n);i&&(a[i]=0<n?t.substr(n+1):null)}),a),hash:this.router(e?(e.match(/#.+/)||[])[0]||"/":location.hash)}},i.prototype.data=function(e,t,n){if(e=e||"layui",n=n||localStorage,p.JSON&&p.JSON.parse){if(null===t)return delete n[e];t="object"==typeof t?t:{key:t};try{var i=JSON.parse(n[e])}catch(e){i={}}return"value"in t&&(i[t.key]=t.value),t.remove&&delete i[t.key],n[e]=JSON.stringify(i),t.key?i[t.key]:i}},i.prototype.sessionData=function(e,t){return this.data(e,t,sessionStorage)},i.prototype.device=function(e){function t(e){return e=new RegExp(e+"/([^\\s\\_\\-]+)"),(n.match(e)||[])[1]||!1}var n=navigator.userAgent.toLowerCase(),i={os:/windows/.test(n)?"windows":/linux/.test(n)?"linux":/iphone|ipod|ipad|ios/.test(n)?"ios":/mac/.test(n)?"mac":void 0,ie:!!(p.ActiveXObject||"ActiveXObject"in p)&&((n.match(/msie\s(\d+)/)||[])[1]||"11"),weixin:t("micromessenger")};return e&&!i[e]&&(i[e]=t(e)),i.android=/android/.test(n),i.ios="ios"===i.os,i.mobile=i.android||i.ios,i},i.prototype.hint=function(){return{error:h}},i.prototype._typeof=i.prototype.type=function(e){return null===e?String(e):"object"==typeof e||"function"==typeof e?(t=(t=Object.prototype.toString.call(e).match(/\s(.+)\]$/)||[])[1]||"Object",new RegExp("\\b(Function|Array|Date|RegExp|Object|Error|Symbol)\\b").test(t)?t.toLowerCase():"object"):typeof e;var t},i.prototype._isArray=i.prototype.isArray=function(e){var t,n=this.type(e);return!(!e||"object"!=typeof e||e===p)&&(t="length"in e&&e.length,"array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)},i.prototype.each=function(e,n){function t(e,t){return n.call(t[e],e,t[e])}if("function"==typeof n)if(this.isArray(e=e||[]))for(i=0;i<e.length&&!t(i,e);i++);else for(var i in e)if(t(i,e))break;return this},i.prototype.sort=function(e,a,t,n){if(n=n?e||[]:JSON.parse(JSON.stringify(e||[])),"object"!==this.type(e)||a){if("object"!=typeof e)return[n];n.sort(function(e,t){var n=e[a],i=t[a];if(!isNaN(e)&&!isNaN(t))return e-t;if(!isNaN(e)&&isNaN(t)){if(!a||"object"!=typeof t)return-1;n=e}else if(isNaN(e)&&!isNaN(t)){if(!a||"object"!=typeof e)return 1;i=t}return(e=[!isNaN(n),!isNaN(i)])[0]&&e[1]?n&&!i&&0!==i?1:!n&&0!==n&&i?-1:n-i:e[0]||e[1]?e[0]||!e[1]?-1:!e[0]||e[1]?1:void 0:i<n?1:n<i?-1:0}),t&&n.reverse()}return n},i.prototype.stope=function(t){t=t||p.event;try{t.stopPropagation()}catch(e){t.cancelBubble=!0}},"LAYUI-EVENT-REMOVE");i.prototype.onevent=function(e,t,n){return"string"!=typeof e||"function"!=typeof n?this:i.event(e,t,null,n)},i.prototype.event=i.event=function(e,t,n,i){function a(e,t){!1===(t&&t.call(o,n))&&null===r&&(r=!1)}var o=this,r=null,l=(t||"").match(/\((.*)\)$/)||[],e=(e+"."+t).replace(l[0],""),s=l[1]||"";return n===c?(delete(o.cache.event[e]||{})[s],o):i?(m.event[e]=m.event[e]||{},s?m.event[e][s]=[i]:(m.event[e][s]=m.event[e][s]||[],m.event[e][s].push(i)),this):(layui.each(m.event[e],function(e,t){"{*}"!==s&&(""===e&&layui.each(t,a),!s||e!==s)||layui.each(t,a)}),r)},i.prototype.on=function(e,t,n){return this.onevent.call(this,t,e,n)},i.prototype.off=function(e,t){return this.event.call(this,t,e,c)},i.prototype.debounce=function(n,i){var a;return function(){var e=this,t=arguments;clearTimeout(a),a=setTimeout(function(){n.apply(e,t)},i)}},i.prototype.throttle=function(e,t){var n=!1;return function(){n||(e.apply(this,arguments),n=!0,setTimeout(function(){n=!1},t))}},p.layui=new i}(window),layui.define(function(e){var t=layui.cache;layui.config({dir:t.dir.replace(/lay\/dest\/$/,"")}),e("layui.all",layui.v)}),function(u){"use strict";function f(e){return new a(e)}var e,p=u.document,a=function(e){var n=this,i="object"==typeof e?layui.isArray(e)?e:[e]:(this.selector=e,p.querySelectorAll(e||null));f.each(i,function(e,t){n.push(i[e])})};Array.prototype.indexOf=Array.prototype.indexOf||function(n,i){var a=-1;return i=i||0,layui.each(this,function(e,t){if(n===t&&i<=e)return a=e,!0}),a},a.fn=a.prototype=[],a.fn.constructor=a,f.extend=function(){function i(e,t){for(var n in e=e||("array"===layui.type(t)?[]:{}),t)e[n]=t[n]&&t[n].constructor===Object?i(e[n],t[n]):t[n];return e}var e,t=1,n=arguments;for(n[0]="object"==typeof n[0]?n[0]:{},e=n.length;t<e;t++)"object"==typeof n[t]&&i(n[0],n[t]);return n[0]},f.ie=(e=navigator.userAgent.toLowerCase(),!!(u.ActiveXObject||"ActiveXObject"in u)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")),f.layui=layui||{},f.getPath=layui.cache.dir,f.stope=layui.stope,f.each=function(){return layui.each.apply(layui,arguments),this},f.digit=function(e,t){if("string"!=typeof e&&"number"!=typeof e)return"";var n="";t=t||2;for(var i=(e=String(e)).length;i<t;i++)n+="0";return e<Math.pow(10,t)?n+e:e},f.elem=function(e,t){var n=p.createElement(e);return f.each(t||{},function(e,t){n.setAttribute(e,t)}),n},f.hasScrollbar=function(){return p.body.scrollHeight>(u.innerHeight||p.documentElement.clientHeight)},f.getStyleRules=function(e,n){if(e)return e=(e=e.sheet||e.styleSheet||{}).cssRules||e.rules,"function"==typeof n&&layui.each(e,function(e,t){if(n(t,e))return!0}),e},f.style=function(e){e=e||{};var t=f.elem("style"),n=e.text||"",i=e.target;if(n)return"styleSheet"in t?(t.setAttribute("type","text/css"),t.styleSheet.cssText=n):t.innerHTML=n,t.id="LAY-STYLE-"+(e.id||(n=f.style.index||0,f.style.index++,"DF-"+n)),i&&((e=f(i).find("#"+t.id))[0]&&e.remove(),f(i).append(t)),t},f.position=function(e,t,n){var i,a,o,r,l,s,c,d;t&&(n=n||{},e!==p&&e!==f("body")[0]||(n.clickType="right"),c="right"===n.clickType?{left:(c=n.e||u.event||{}).clientX,top:c.clientY,right:c.clientX,bottom:c.clientY}:e.getBoundingClientRect(),l=t.offsetWidth,s=t.offsetHeight,i=function(e){return p.body[e=e?"scrollLeft":"scrollTop"]|p.documentElement[e]},a=function(e){return p.documentElement[e?"clientWidth":"clientHeight"]},o="margin"in n?n.margin:5,d=c.left,r=c.bottom,"center"===n.align?d-=(l-e.offsetWidth)/2:"right"===n.align&&(d=d-l+e.offsetWidth),(d=d+l+o>a("width")?a("width")-l-o:d)<o&&(d=o),c.bottom+s+o>a()&&(c.top>s+o&&c.top<=a()?r=c.top-s-2*o:n.allowBottomOut||(r=a()-s-2*o)<0&&(r=0)),(l=n.position)&&(t.style.position=l),s=n.offset?n.offset[0]:0,c=n.offset?n.offset[1]:0,t.style.left=d+("fixed"===l?0:i(1))+s+"px",t.style.top=r+("fixed"===l?0:i())+c+"px",f.hasScrollbar()||(d=t.getBoundingClientRect(),!n.SYSTEM_RELOAD&&d.bottom+o>a()&&(n.SYSTEM_RELOAD=!0,setTimeout(function(){f.position(e,t,n)},50))))},f.options=function(t,n){if(n="object"==typeof n?n:{attr:n},t===p)return{};var t=f(t),i=n.attr||"lay-options",t=t.attr(i);try{return new Function("return "+(t||"{}"))()}catch(e){return layui.hint().error(n.errorText||[i+'="'+t+'"',"\n parseerror: "+e].join("\n"),"error"),{}}},f.isTopElem=function(n){var e=[p,f("body")[0]],i=!1;return f.each(e,function(e,t){if(t===n)return i=!0}),i},f.clipboard={writeText:function(t){var n=String(t.text);function e(){var e=p.createElement("textarea");e.value=n,e.style.position="fixed",e.style.opacity="0",e.style.top="0px",e.style.left="0px",p.body.appendChild(e),e.select();try{p.execCommand("copy"),"function"==typeof t.done&&t.done()}catch(e){"function"==typeof t.error&&t.error(e)}finally{e.remove?e.remove():p.body.removeChild(e)}}navigator&&"clipboard"in navigator?navigator.clipboard.writeText(n).then(t.done,function(){e()}):e()}},f.passiveSupported=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});u.addEventListener("test",null,t),u.removeEventListener("test",null,t)}catch(e){}return e}(),f.touchEventsSupported=function(){return"ontouchstart"in u},f.touchSwipe=function(e,t){var n,i,a,o=t,r=f(e)[0];r&&f.touchEventsSupported()&&(n={pointerStart:{x:0,y:0},pointerEnd:{x:0,y:0},distanceX:0,distanceY:0,direction:"none",timeStart:null},t=function(e){1===e.touches.length&&(r.addEventListener("touchmove",i,!!f.passiveSupported&&{passive:!1}),r.addEventListener("touchend",a),r.addEventListener("touchcancel",a),n.timeStart=Date.now(),n.pointerStart.x=n.pointerEnd.x=e.touches[0].clientX,n.pointerStart.y=n.pointerEnd.y=e.touches[0].clientY,n.distanceX=n.distanceY=0,n.direction="none",o.onTouchStart)&&o.onTouchStart(e,n)},i=function(e){e.preventDefault(),n.pointerEnd.x=e.touches[0].clientX,n.pointerEnd.y=e.touches[0].clientY,n.distanceX=n.pointerStart.x-n.pointerEnd.x,n.distanceY=n.pointerStart.y-n.pointerEnd.y,Math.abs(n.distanceX)>Math.abs(n.distanceY)?n.direction=0<n.distanceX?"left":"right":n.direction=0<n.distanceY?"up":"down",o.onTouchMove&&o.onTouchMove(e,n)},a=function(e){o.onTouchEnd&&o.onTouchEnd(e,n),r.removeEventListener("touchmove",i),r.removeEventListener("touchend",a,!!f.passiveSupported&&{passive:!1}),r.removeEventListener("touchcancel",a)},r.__lay_touchswipe_cb_&&r.removeEventListener("touchstart",r.__lay_touchswipe_cb_),r.__lay_touchswipe_cb_=t,r.addEventListener("touchstart",t))},f.addEvent=p.addEventListener?function(e,t,n,i){e.addEventListener(t,n,i)}:function(t,e,n){function i(e){e.target=e.srcElement,n.call(t,e)}var a="_lay_on_"+e,o=(i._rawFn=n,t[a]||(t[a]=[]),!1);f.each(t[a],function(e,t){if(t._rawFn===n)return o=!0}),o||(t[a].push(i),t.attachEvent("on"+e,i))},f.removeEvent=p.removeEventListener?function(e,t,n,i){e.removeEventListener(t,n,i)}:function(n,i,a){var o,e="_lay_on_"+i,t=n[e];layui.isArray(t)&&(o=[],f.each(t,function(e,t){t._rawFn===a?n.detachEvent("on"+i,t):o.push(t)}),n[e]=o)},f.onClickOutside=function(a,o,e){var t=(e=e||{}).event||("onpointerdown"in u?"pointerdown":"mousedown"),n=e.scope||p,s=e.ignore||[],e=!("capture"in e)||e.capture,i=n,r=t,l=function(l){var e,t=a,n=l.target||l.srcElement,i=(i=(e=l).composedPath&&e.composedPath()||e.path,e=e.target||e.srcElement,null!=i?i:[e].concat(function e(t,n){return n=n||[],(t=t.parentNode)?e(t,n.concat([t])):n}(e)));t&&t!==n&&-1===i.indexOf(t)&&!function(e){for(var t=l.target||l.srcElement,n=0;n<s.length;n++){var i=s[n];if("string"==typeof i)for(var a=p.querySelectorAll(i),o=0;o<a.length;o++){var r=a[n];if(r===t||-1!==e.indexOf(r))return 1}else if(i&&(i===t||-1!==e.indexOf(i)))return 1}}(i)&&o(l)},c=f.passiveSupported?{passive:!0,capture:e}:e;return i.addEventListener?i.addEventListener(r,l,c):i.attachEvent("on"+r,l),function(){i.removeEventListener?i.removeEventListener(r,l,c):i.detachEvent("on"+r,l)}},a.addStr=function(n,e){return n=n.replace(/\s+/," "),e=e.replace(/\s+/," ").split(" "),f.each(e,function(e,t){new RegExp("\\b"+t+"\\b").test(n)||(n=n+" "+t)}),n.replace(/^\s|\s$/,"")},a.removeStr=function(n,e){return n=n.replace(/\s+/," "),e=e.replace(/\s+/," ").split(" "),f.each(e,function(e,t){(t=new RegExp("\\b"+t+"\\b")).test(n)&&(n=n.replace(t,""))}),n.replace(/\s+/," ").replace(/^\s|\s$/,"")},a.fn.find=function(n){var i=[],a="object"==typeof n;return this.each(function(e,t){t=a&&t.contains(n)?n:t.querySelectorAll(n||null),f.each(t,function(e,t){i.push(t)})}),f(i)},a.fn.each=function(e){return f.each.call(this,this,e)},a.fn.addClass=function(n,i){return this.each(function(e,t){t.className=a[i?"removeStr":"addStr"](t.className,n)})},a.fn.removeClass=function(e){return this.addClass(e,!0)},a.fn.hasClass=function(n){var i=!1;return this.each(function(e,t){new RegExp("\\b"+n+"\\b").test(t.className)&&(i=!0)}),i},a.fn.css=function(t,i){function a(e){return isNaN(e)?e:e+"px"}return"string"!=typeof t||void 0!==i?this.each(function(e,n){"object"==typeof t?f.each(t,function(e,t){n.style[e]=a(t)}):n.style[t]=a(i)}):0<this.length?this[0].style[t]:void 0},a.fn.width=function(n){var i=this;return void 0!==n?i.each(function(e,t){i.css("width",n)}):0<i.length?i[0].offsetWidth:void 0},a.fn.height=function(n){var i=this;return void 0!==n?i.each(function(e,t){i.css("height",n)}):0<i.length?i[0].offsetHeight:void 0},a.fn.attr=function(n,i){return void 0!==i?this.each(function(e,t){t.setAttribute(n,i)}):0<this.length?this[0].getAttribute(n):void 0},a.fn.removeAttr=function(n){return this.each(function(e,t){t.removeAttribute(n)})},a.fn.html=function(n){return void 0!==n?this.each(function(e,t){t.innerHTML=n}):0<this.length?this[0].innerHTML:void 0},a.fn.val=function(n){return void 0!==n?this.each(function(e,t){t.value=n}):0<this.length?this[0].value:void 0},a.fn.append=function(n){return this.each(function(e,t){"object"==typeof n?t.appendChild(n):t.innerHTML=t.innerHTML+n})},a.fn.remove=function(n){return this.each(function(e,t){n?t.removeChild(n):t.parentNode.removeChild(t)})},a.fn.on=function(n,i,a){return this.each(function(e,t){f.addEvent(t,n,i,a)})},a.fn.off=function(n,i,a){return this.each(function(e,t){f.removeEvent(t,n,i,a)})},u.lay=f,u.layui&&layui.define&&layui.define(function(e){e("lay",f)})}(window,window.document),layui.define(function(e){"use strict";function s(e){return new RegExp(e,"g")}function n(e,t){var n=this;n.config=n.config||{},n.template=e,(e=function(e){for(var t in e)n.config[t]=e[t]})(i),e(t)}function t(e,t){return new n(e,t)}var i={open:"{{",close:"}}"},c={escape:function(e){return null==e?"":/[<"'>]|&(?=#[a-zA-Z0-9]+)/g.test(e+="")?e.replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;"):e}};n.prototype.tagExp=function(e,t,n){var i=this.config;return s((t||"")+i.open+["#([\\s\\S])+?","([^{#}])*?"][e||0]+i.close+(n||""))},n.prototype.parse=function(t,n){var i=this,a=i.config,o=t,r=s("^"+a.open+"#"),l=s(a.close+"$");if("string"!=typeof t)return t;t='"use strict";var view = "'+t.replace(/\s+|\r|\t|\n/g," ").replace(s(a.open+"#"),a.open+"# ").replace(s(a.close+"}"),"} "+a.close).replace(/\\/g,"\\\\").replace(s(a.open+"!(.+?)!"+a.close),function(e){return e.replace(s("^"+a.open+"!"),"").replace(s("!"+a.close),"").replace(s(a.open+"|"+a.close),function(e){return e.replace(/(.)/g,"\\$1")})}).replace(/(?="|')/g,"\\").replace(i.tagExp(),function(e){return'";'+(e=e.replace(r,"").replace(l,"")).replace(/\\(.)/g,"$1")+';view+="'}).replace(i.tagExp(1),function(e){var t='"+laytpl.escape(';return e.replace(/\s/g,"")===a.open+a.close?"":(e=e.replace(s(a.open+"|"+a.close),""),/^=/.test(e)?e=e.replace(/^=/,""):/^-/.test(e)&&(e=e.replace(/^-/,""),t='"+('),t+e.replace(/\\(.)/g,"$1")+')+"')})+'";return view;';try{return i.cache=t=new Function("d, laytpl",t),t(n,c)}catch(e){return delete i.cache,t=e,n=o,i="Laytpl Error: ","object"==typeof console&&console.error(i+t+"\n"+(n||"")),i+t}},n.prototype.render=function(e,t){e=e||{};e=this.cache?this.cache(e,c):this.parse(this.template,e);return"function"==typeof t&&t(e),e};t.config=function(e){for(var t in e=e||{})i[t]=e[t]},t.v="2.0.0",e("laytpl",t)}),layui.define(function(e){"use strict";function t(e){this.config=e||{},this.config.index=++u.index,this.render(!0)}var o=document,r="getElementById",d="getElementsByTagName",s="layui-disabled",u=(t.prototype.type=function(){var e=this.config;if("object"==typeof e.elem)return void 0===e.elem.length?2:3},t.prototype.view=function(){var i,e,n,a=this.config,o=a.groups="groups"in a?Number(a.groups)||0:5,r=(a.layout="object"==typeof a.layout?a.layout:["prev","page","next"],a.count=Number(a.count)||0,a.curr=Number(a.curr)||1,a.limits="object"==typeof a.limits?a.limits:[10,20,30,40,50],a.limit=Number(a.limit)||10,a.pages=Math.ceil(a.count/a.limit)||1,a.curr>a.pages?a.curr=a.pages:a.curr<1&&(a.curr=1),o<0?o=1:o>a.pages&&(o=a.pages),a.prev="prev"in a?a.prev:"上一页",a.next="next"in a?a.next:"下一页",a.pages>o?Math.ceil((a.curr+(1<o?1:0))/(0<o?o:1)):1),l={prev:a.prev?'<a class="layui-laypage-prev'+(1==a.curr?" "+s:"")+'" data-page="'+(a.curr-1)+'">'+a.prev+"</a>":"",page:function(){var e=[];if(a.count<1)return"";1<r&&!1!==a.first&&0!==o&&e.push('<a class="layui-laypage-first" data-page="1"  title="首页">'+(a.first||1)+"</a>");var t=Math.floor((o-1)/2),n=1<r?a.curr-t:1,i=1<r?(t=a.curr+(o-t-1))>a.pages?a.pages:t:o;for(i-n<o-1&&(n=i-o+1),!1!==a.first&&2<n&&e.push('<span class="layui-laypage-spr">...</span>');n<=i;n++)n===a.curr?e.push('<span class="layui-laypage-curr"><em class="layui-laypage-em" '+(/^#/.test(a.theme)?'style="background-color:'+a.theme+';"':"")+"></em><em>"+n+"</em></span>"):e.push('<a data-page="'+n+'">'+n+"</a>");return a.pages>o&&a.pages>i&&!1!==a.last&&(i+1<a.pages&&e.push('<span class="layui-laypage-spr">...</span>'),0!==o)&&e.push('<a class="layui-laypage-last" title="尾页"  data-page="'+a.pages+'">'+(a.last||a.pages)+"</a>"),e.join("")}(),next:a.next?'<a class="layui-laypage-next'+(a.curr==a.pages?" "+s:"")+'" data-page="'+(a.curr+1)+'">'+a.next+"</a>":"",count:'<span class="layui-laypage-count">'+(e="object"==typeof a.countText?a.countText:["共 "," 条"])[0]+a.count+e[1]+"</span>",limit:(i=['<span class="layui-laypage-limits"><select lay-ignore>'],layui.each(a.limits,function(e,t){var n;i.push('<option value="'+t+'"'+(t===a.limit?" selected":"")+">"+(n=t+" 条/页","function"==typeof a.limitTemplet&&a.limitTemplet(t)||n)+"</option>")}),i.join("")+"</select></span>"),refresh:['<a data-page="'+a.curr+'" class="layui-laypage-refresh">','<i class="layui-icon layui-icon-refresh"></i>',"</a>"].join(""),skip:['<span class="layui-laypage-skip">'+(e="object"==typeof a.skipText?a.skipText:["到第","页","确定"])[0],'<input type="text" min="1" value="'+a.curr+'" class="layui-input">',e[1]+'<button type="button" class="layui-laypage-btn">'+e[2]+"</button>","</span>"].join("")};return['<div class="layui-box layui-laypage layui-laypage-'+(a.theme?/^#/.test(a.theme)?"molv":a.theme:"default")+'" id="layui-laypage-'+a.index+'">',(n=[],layui.each(a.layout,function(e,t){l[t]&&n.push(l[t])}),n.join("")),"</div>"].join("")},t.prototype.jump=function(e,t){if(e){var n=this,i=n.config,a=e.children,o=e[d]("button")[0],r=e[d]("input")[0],e=e[d]("select")[0],l=function(){var e=Number(r.value.replace(/\s|\D/g,""));e&&(i.curr=e,n.render())};if(t)return l();for(var s=0,c=a.length;s<c;s++)"a"===a[s].nodeName.toLowerCase()&&u.on(a[s],"click",function(){var e=Number(this.getAttribute("data-page"));e<1||e>i.pages||(i.curr=e,n.render())});e&&u.on(e,"change",function(){var e=this.value;i.curr*e>i.count&&(i.curr=Math.ceil(i.count/e)),i.limit=e,n.render()}),o&&u.on(o,"click",function(){l()})}},t.prototype.skip=function(n){var i,e;n&&(i=this,e=n[d]("input")[0])&&u.on(e,"keyup",function(e){var t=this.value,e=e.keyCode;/^(37|38|39|40)$/.test(e)||(/\D/.test(t)&&(this.value=t.replace(/\D/,"")),13===e&&i.jump(n,!0))})},t.prototype.render=function(e){var t=this,n=t.config,i=t.type(),a=t.view(),i=(2===i?n.elem&&(n.elem.innerHTML=a):3===i?n.elem.html(a):o[r](n.elem)&&(o[r](n.elem).innerHTML=a),n.jump&&n.jump(n,e),o[r]("layui-laypage-"+n.index));t.jump(i),n.hash&&!e&&(location.hash="!"+n.hash+"="+n.curr),t.skip(i)},{render:function(e){return new t(e).index},index:layui.laypage?layui.laypage.index+1e4:0,on:function(t,e,n){return t.attachEvent?t.attachEvent("on"+e,function(e){e.target=e.srcElement,n.call(t,e)}):t.addEventListener(e,n,!1),this}});e("laypage",u)}),function(a,v){"use strict";function o(){var t=this,e=t.config.id;return(o.that[e]=t).inst={hint:function(e){t.hint.call(t,e)},reload:function(e){t.reload.call(t,e)},config:t.config}}function x(e){var t,n=this,i=(n.index=++b.index,n.config=lay.extend({},n.config,b.config,e),lay(e.elem||n.config.elem));return 1<i.length?(lay.each(i,function(){b.render(lay.extend({},n.config,{elem:this}))}),n):(e=lay.extend(n.config,lay.options(i[0])),i[0]&&i.attr(s)?(t=o.getThis(i.attr(s)))?t.reload(e):void 0:(e.id="id"in e?e.id:i.attr("id")||n.index,e.index=n.index,void b.ready(function(){n.init()})))}var i=a.layui&&layui.define,r={getPath:a.lay&&lay.getPath?lay.getPath:"",link:function(e,t,n){b.path&&a.lay&&lay.layui&&lay.layui.link(b.path+e,t,n)}},e=a.LAYUI_GLOBAL||{},n="laydate",s="lay-"+n+"-id",b={v:"5.6.0",config:{weekStart:0},index:a.laydate&&a.laydate.v?1e5:0,path:e.laydate_dir||r.getPath,set:function(e){return this.config=lay.extend({},this.config,e),this},ready:function(e){var t="laydate",n=(i?"modules/":"")+"laydate.css?v="+b.v;return i?layui["layui.all"]?"function"==typeof e&&e():layui.addcss(n,e,t):r.link(n,e,t),this}},T="layui-this",E="laydate-disabled",h=[100,2e5],w="layui-laydate-static",D="layui-laydate-list",l="laydate-selected",c="layui-laydate-hint",d="laydate-day-prev",u="laydate-day-next",S=".laydate-btns-confirm",N="laydate-time-text",L="laydate-btns-time",k="layui-laydate-preview",C="layui-laydate-shade",f="yyyy|y|MM|M|dd|d|HH|H|mm|m|ss|s";o.formatArr=function(e){return(e||"").match(new RegExp(f+"|.","g"))||[]},x.isLeapYear=function(e){return e%4==0&&e%100!=0||e%400==0},x.prototype.config={type:"date",range:!1,format:"yyyy-MM-dd",value:null,isInitValue:!0,min:"1900-1-1",max:"2099-12-31",trigger:"click",show:!1,showBottom:!0,isPreview:!0,btns:["clear","now","confirm"],lang:"cn",theme:"default",position:null,calendar:!1,mark:{},holidays:null,zIndex:null,done:null,change:null,autoConfirm:!0,shade:0},x.prototype.lang=function(){var e={cn:{weeks:["日","一","二","三","四","五","六"],time:["时","分","秒"],timeTips:"选择时间",startTime:"开始时间",endTime:"结束时间",dateTips:"返回日期",month:["一","二","三","四","五","六","七","八","九","十","十一","十二"],tools:{confirm:"确定",clear:"清空",now:"现在"},timeout:"结束时间不能早于开始时间<br>请重新选择",invalidDate:"不在有效日期或时间范围内",formatError:["日期格式不合法<br>必须遵循下述格式：<br>","<br>已为你重置"],preview:"当前选中的结果"},en:{weeks:["Su","Mo","Tu","We","Th","Fr","Sa"],time:["Hours","Minutes","Seconds"],timeTips:"Select Time",startTime:"Start Time",endTime:"End Time",dateTips:"Select Date",month:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tools:{confirm:"Confirm",clear:"Clear",now:"Now"},timeout:"End time cannot be less than start Time<br>Please re-select",invalidDate:"Invalid date",formatError:["The date format error<br>Must be followed：<br>","<br>It has been reset"],preview:"The selected result"}};return e[this.config.lang]||e.cn},x.prototype.markerOfChineseFestivals={"0-1-1":"元旦","0-2-14":"情人","0-3-8":"妇女","0-3-12":"植树","0-4-1":"愚人","0-5-1":"劳动","0-5-4":"青年","0-6-1":"儿童","0-9-10":"教师","0-10-1":"国庆","0-12-25":"圣诞"},x.prototype.reload=function(e){this.config=lay.extend({},this.config,e),this.init()},x.prototype.init=function(){var r=this,l=r.config,e="static"===l.position,t={year:"yyyy",month:"yyyy-MM",date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss"};l.elem=lay(l.elem),l.eventElem=lay(l.eventElem),l.elem[0]&&("array"!==layui.type(l.theme)&&(l.theme=[l.theme]),l.fullPanel&&("datetime"!==l.type||l.range)&&delete l.fullPanel,r.rangeStr=l.range?"string"==typeof l.range?l.range:"-":"",r.rangeLinked=!(!l.range||!l.rangeLinked||"date"!==l.type&&"datetime"!==l.type),r.autoCalendarModel=function(){var e=r.rangeLinked;return r.rangeLinked=l.range&&("date"===l.type||"datetime"===l.type)&&(!r.startDate||!r.endDate||r.startDate&&r.endDate&&r.startDate.year===r.endDate.year&&r.startDate.month===r.endDate.month),lay(r.elem)[r.rangeLinked?"addClass":"removeClass"]("layui-laydate-linkage"),r.rangeLinked!=e},r.autoCalendarModel.auto=r.rangeLinked&&"auto"===l.rangeLinked,"array"===layui.type(l.range)&&(r.rangeElem=[lay(l.range[0]),lay(l.range[1])]),t[l.type]||(a.console&&console.error&&console.error("laydate type error:'"+l.type+"' is not supported"),l.type="date"),l.format===t.date&&(l.format=t[l.type]||t.date),r.format=o.formatArr(l.format),l.weekStart&&!/^[0-6]$/.test(l.weekStart)&&(t=r.lang(),l.weekStart=t.weeks.indexOf(l.weekStart),-1===l.weekStart)&&(l.weekStart=0),r.EXP_IF="",r.EXP_SPLIT="",lay.each(r.format,function(e,t){e=new RegExp(f).test(t)?"\\d{"+(new RegExp(f).test(r.format[0===e?e+1:e-1]||"")?/^yyyy|y$/.test(t)?4:t.length:/^yyyy$/.test(t)?"1,4":/^y$/.test(t)?"1,308":"1,2")+"}":"\\"+t,r.EXP_IF=r.EXP_IF+e,r.EXP_SPLIT=r.EXP_SPLIT+"("+e+")"}),r.EXP_IF_ONE=new RegExp("^"+r.EXP_IF+"$"),r.EXP_IF=new RegExp("^"+(l.range?r.EXP_IF+"\\s\\"+r.rangeStr+"\\s"+r.EXP_IF:r.EXP_IF)+"$"),r.EXP_SPLIT=new RegExp("^"+r.EXP_SPLIT+"$",""),r.isInput(l.elem[0])||"focus"===l.trigger&&(l.trigger="click"),l.elem.attr("lay-key",r.index),l.eventElem.attr("lay-key",r.index),l.elem.attr(s,l.id),lay.each(["min","max"],function(e,t){var n=[],i=[];if("number"==typeof l[t])var a=l[t],o=new Date,o=r.newDate({year:o.getFullYear(),month:o.getMonth(),date:o.getDate(),hours:e?23:0,minutes:e?59:0,seconds:e?59:0}).getTime(),n=[(e=new Date(a?a<864e5?o+864e5*a:a:o)).getFullYear(),e.getMonth()+1,e.getDate()],i=[e.getHours(),e.getMinutes(),e.getSeconds()];else if("string"==typeof l[t])n=(l[t].match(/\d+-\d+-\d+/)||[""])[0].split("-"),i=(l[t].match(/\d+:\d+:\d+/)||[""])[0].split(":");else if("object"==typeof l[t])return l[t];l[t]={year:0|n[0]||(new Date).getFullYear(),month:n[1]?(0|n[1])-1:(new Date).getMonth(),date:0|n[2]||(new Date).getDate(),hours:0|i[0],minutes:0|i[1],seconds:0|i[2]}}),r.elemID="layui-laydate"+l.elem.attr("lay-key"),(l.show||e)&&r.render(),e||r.events(),"function"==typeof l.formatToDisplay&&(r.isInput(l.elem[0])?r.formatToDisplay(l.elem[0],l.formatToDisplay):(t=r.rangeElem)&&(r.formatToDisplay(t[0][0],l.formatToDisplay),r.formatToDisplay(t[1][0],l.formatToDisplay))),l.value)&&l.isInitValue&&("date"===layui.type(l.value)?r.setValue(r.parse(0,r.systemDate(l.value))):r.setValue(l.value))},x.prototype.render=function(){var n,i,a,o,r=this,l=r.config,s=r.lang(),c="static"===l.position,d=r.elem=lay.elem("div",{id:r.elemID,class:["layui-laydate",l.range?" layui-laydate-range":"",r.rangeLinked?" layui-laydate-linkage":"",c?" "+w:"",l.fullPanel?" laydate-theme-fullpanel":"",(n="",lay.each(l.theme,function(e,t){"default"===t||/^#/.test(t)||(n+=" laydate-theme-"+t)}),n)].join("")}),u=r.elemMain=[],f=r.elemHeader=[],p=r.elemCont=[],h=r.table=[],e=r.footer=lay.elem("div",{class:"layui-laydate-footer"}),t=r.shortcut=lay.elem("ul",{class:"layui-laydate-shortcut"}),y=(l.zIndex&&(d.style.zIndex=l.zIndex),lay.each(new Array(2),function(e){if(!l.range&&0<e)return!0;var n=lay.elem("div",{class:"layui-laydate-header"}),t=[((t=lay.elem("i",{class:"layui-icon laydate-icon laydate-prev-y"})).innerHTML="&#xe65a;",t),((t=lay.elem("i",{class:"layui-icon laydate-icon laydate-prev-m"})).innerHTML="&#xe603;",t),((t=lay.elem("div",{class:"laydate-set-ym"})).appendChild(lay.elem("span")),t.appendChild(lay.elem("span")),t),((t=lay.elem("i",{class:"layui-icon laydate-icon laydate-next-m"})).innerHTML="&#xe602;",t),((t=lay.elem("i",{class:"layui-icon laydate-icon laydate-next-y"})).innerHTML="&#xe65b;",t)],i=lay.elem("div",{class:"layui-laydate-content"}),a=lay.elem("table"),o=lay.elem("thead"),r=lay.elem("tr");lay.each(t,function(e,t){n.appendChild(t)}),o.appendChild(r),lay.each(new Array(6),function(n){var i=a.insertRow(0);lay.each(new Array(7),function(e){var t;0===n&&((t=lay.elem("th")).innerHTML=s.weeks[(e+l.weekStart)%7],r.appendChild(t)),i.insertCell(e)})}),a.insertBefore(o,a.children[0]),i.appendChild(a),u[e]=lay.elem("div",{class:"layui-laydate-main laydate-main-list-"+e}),u[e].appendChild(n),u[e].appendChild(i),f.push(t),p.push(i),h.push(a)}),lay(e).html((y=[],i=[],"datetime"===l.type&&y.push('<span lay-type="datetime" class="'+L+'">'+s.timeTips+"</span>"),(l.range||"datetime"!==l.type||l.fullPanel)&&y.push('<span class="'+k+'" title="'+s.preview+'"></span>'),lay.each(l.btns,function(e,t){var n=s.tools[t]||"btn";l.range&&"now"===t||(c&&"clear"===t&&(n="cn"===l.lang?"重置":"Reset"),i.push('<span lay-type="'+t+'" class="laydate-btns-'+t+'">'+n+"</span>"))}),y.push('<div class="laydate-footer-btns">'+i.join("")+"</div>"),y.join(""))),l.shortcuts&&(d.appendChild(t),lay(t).html((a=[],lay.each(l.shortcuts,function(e,t){a.push('<li data-index="'+e+'">'+t.text+"</li>")}),a.join(""))).find("li").on("click",function(e){var t=("function"==typeof(t=l.shortcuts[this.dataset.index]||{}).value?t.value():t.value)||[],i=(layui.isArray(t)||(t=[t]),l.type);lay.each(t,function(e,t){var n=[l.dateTime,r.endDate][e];"time"===i&&"date"!==layui.type(t)?r.EXP_IF.test(t)&&(t=(t.match(r.EXP_SPLIT)||[]).slice(1),lay.extend(n,{hours:0|t[0],minutes:0|t[2],seconds:0|t[4]})):lay.extend(n,r.systemDate("date"===layui.type(t)?t:new Date(t))),"time"!==i&&"datetime"!==i||(r[["startTime","endTime"][e]]={hours:n.hours,minutes:n.minutes,seconds:n.seconds}),0===e?r.startDate=lay.extend({},n):r.endState=!0,"year"===i||"month"===i||"time"===i?r.listYM[e]=[n.year,n.month+1]:e&&r.autoCalendarModel.auto&&r.autoCalendarModel()}),r.checkDate("limit").calendar(null,null,"init"),(t=lay(r.footer).find("."+L).removeClass(E))&&"date"===t.attr("lay-type")&&t[0].click(),r.done(null,"change"),lay(this).addClass(T),"static"!==l.position&&r.setValue(r.parse()).done().remove()})),lay.each(u,function(e,t){d.appendChild(t)}),l.showBottom&&d.appendChild(e),lay.elem("style")),m=[],g=!0,t=(lay.each(l.theme,function(e,t){g&&/^#/.test(t)?(g=!(o=!0),m.push(["#{{id}} .layui-laydate-header{background-color:{{theme}};}","#{{id}} li.layui-this,#{{id}} td.layui-this>div{background-color:{{theme}} !important;}",-1!==l.theme.indexOf("circle")?"":"#{{id}} .layui-this{background-color:{{theme}} !important;}","#{{id}} .laydate-day-now{color:{{theme}} !important;}","#{{id}} .laydate-day-now:after{border-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,r.elemID).replace(/{{theme}}/g,t))):!g&&/^#/.test(t)&&m.push(["#{{id}} .laydate-selected>div{background-color:{{theme}} !important;}","#{{id}} .laydate-selected:hover>div{background-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,r.elemID).replace(/{{theme}}/g,t))}),l.shortcuts&&l.range&&m.push("#{{id}}.layui-laydate-range{width: 628px;}".replace(/{{id}}/g,r.elemID)),m.length&&(m=m.join(""),"styleSheet"in y?(y.setAttribute("type","text/css"),y.styleSheet.cssText=m):y.innerHTML=m,o&&lay(d).addClass("laydate-theme-molv"),d.appendChild(y)),r.remove(x.thisElemDate),b.thisId=l.id,c?l.elem.append(d):(v.body.appendChild(d),r.position()),l.shade?'<div class="'+C+'" style="z-index:'+(parseInt(layui.getStyle(d,"z-index"))-1)+"; background-color: "+(l.shade[1]||"#000")+"; opacity: "+(l.shade[0]||l.shade)+'"></div>':"");d.insertAdjacentHTML("beforebegin",t),r.checkDate().calendar(null,0,"init"),r.changeEvent(),x.thisElemDate=r.elemID,r.renderAdditional(),"function"==typeof l.ready&&l.ready(lay.extend({},l.dateTime,{month:l.dateTime.month+1})),r.preview()},x.prototype.remove=function(e){var t=this,n=t.config,i=lay("#"+(e||t.elemID));return i[0]&&(i.hasClass(w)||t.checkDate(function(){i.remove(),delete t.startDate,delete t.endDate,delete t.endState,delete t.startTime,delete t.endTime,delete b.thisId,"function"==typeof n.close&&n.close(t)}),lay("."+C).remove()),t},x.prototype.position=function(){var e=this.config;return lay.position(e.elem[0],this.elem,{position:e.position}),this},x.prototype.hint=function(e){var t=this,n=(t.config,lay.elem("div",{class:c}));t.elem&&(n.innerHTML=(e="object"==typeof e?e||{}:{content:e}).content||"",lay(t.elem).find("."+c).remove(),t.elem.appendChild(n),clearTimeout(t.hinTimer),t.hinTimer=setTimeout(function(){lay(t.elem).find("."+c).remove()},"ms"in e?e.ms:3e3))},x.prototype.getAsYM=function(e,t,n){return n?t--:t++,t<0&&(t=11,e--),11<t&&(t=0,e++),[e,t]},x.prototype.systemDate=function(e){var t=e||new Date;return{year:t.getFullYear(),month:t.getMonth(),date:t.getDate(),hours:e?e.getHours():0,minutes:e?e.getMinutes():0,seconds:e?e.getSeconds():0}},x.prototype.checkDate=function(e){function n(i,a,o){var r=["startTime","endTime"];a=(a.match(s.EXP_SPLIT)||[]).slice(1),o=o||0,c.range&&(s[r[o]]=s[r[o]]||{}),lay.each(s.format,function(e,t){var n=parseFloat(a[e]);a[e].length<t.length&&(l=!0),/yyyy|y/.test(t)?(n<h[0]&&(n=h[0],l=!0),i.year=n):/MM|M/.test(t)?(n<1&&(n=1,l=!0),i.month=n-1):/dd|d/.test(t)?(n<1&&(n=1,l=!0),i.date=n):/HH|H/.test(t)?(n<0&&(l=!(n=0)),23<n&&(n=23,l=!0),i.hours=n,c.range&&(s[r[o]].hours=n)):/mm|m/.test(t)?(n<0&&(l=!(n=0)),59<n&&(n=59,l=!0),i.minutes=n,c.range&&(s[r[o]].minutes=n)):/ss|s/.test(t)&&(n<0&&(l=!(n=0)),59<n&&(n=59,l=!0),i.seconds=n,c.range)&&(s[r[o]].seconds=n)}),p(i)}var t,l,i,a,o,s=this,c=(new Date,s.config),r=s.lang(),d=c.dateTime=c.dateTime||s.systemDate(),u=c.elem[0],f=(s.isInput(u),function(){if(s.rangeElem){var e=[s.rangeElem[0].val(),s.rangeElem[1].val()];if(e[0]&&e[1])return e.join(" "+s.rangeStr+" ")}return s.isInput(u)?u.value:"static"===c.position?"":lay(u).attr("lay-date")}()),p=function(e){e&&(e.year>h[1]&&(e.year=h[1],l=!0),11<e.month&&(e.month=11,l=!0),59<e.seconds&&(e.seconds=0,e.minutes++,l=!0),59<e.minutes&&(e.minutes=0,e.hours++,l=!0),23<e.hours&&(e.hours=0,l=!0),t=b.getEndDate(e.month+1,e.year),e.date>t)&&(e.date=t,l=!0)};return"limit"===e?c.range?(p(s.rangeLinked?s.startDate:d),s.endDate&&p(s.endDate)):p(d):("string"==typeof(f=f||c.value)&&(f=f.replace(/\s+/g," ").replace(/^\s|\s$/g,"")),(i=function(){var e,t,n;c.range&&(s.endDate=s.endDate||lay.extend({},c.dateTime,(e={},t=c.dateTime,n=s.getAsYM(t.year,t.month),"year"===c.type?e.year=t.year+1:"time"!==c.type&&(e.year=n[0],e.month=n[1]),"datetime"!==c.type&&"time"!==c.type||(e.hours=23,e.minutes=e.seconds=59),e)))})(),"string"==typeof f&&f?s.EXP_IF.test(f)?c.range?(f=f.split(" "+s.rangeStr+" "),lay.each([c.dateTime,s.endDate],function(e,t){n(t,f[e],e)})):n(d,f):(s.hint(r.formatError[0]+(c.range?c.format+" "+s.rangeStr+" "+c.format:c.format)+r.formatError[1]),l=!0):f&&"date"===layui.type(f)?c.dateTime=s.systemDate(f):(c.dateTime=s.systemDate(),delete s.startTime,delete s.endDate,i(),delete s.endTime),s.rangeElem&&(i=[s.rangeElem[0].val(),s.rangeElem[1].val()],a=[c.dateTime,s.endDate],lay.each(i,function(e,t){s.EXP_IF_ONE.test(t)&&n(a[e],t,e)})),p(d),c.range&&p(s.endDate),l&&f&&s.setValue(!c.range||s.endDate?s.parse():""),s.getDateTime(d)>s.getDateTime(c.max)?(d=c.dateTime=lay.extend({},c.max),o=!0):s.getDateTime(d)<s.getDateTime(c.min)&&(d=c.dateTime=lay.extend({},c.min),o=!0),c.range&&((s.getDateTime(s.endDate)<s.getDateTime(c.min)||s.getDateTime(s.endDate)>s.getDateTime(c.max))&&(s.endDate=lay.extend({},c.max),o=!0),s.startTime={hours:c.dateTime.hours,minutes:c.dateTime.minutes,seconds:c.dateTime.seconds},s.endTime={hours:s.endDate.hours,minutes:s.endDate.minutes,seconds:s.endDate.seconds},"month"===c.type)&&(c.dateTime.date=1,s.endDate.date=1),o&&f&&(s.setValue(s.parse()),s.hint("value "+r.invalidDate+r.formatError[1])),s.startDate=s.startDate||f&&lay.extend({},c.dateTime),s.autoCalendarModel.auto&&s.autoCalendarModel(),s.endState=!c.range||!s.rangeLinked||!(!s.startDate||!s.endDate),e&&e()),s},x.prototype.markRender=function(e,n,t){var i;"object"==typeof t?lay.each(t||{},function(e,t){(e=e.split("-"))[0]!=n[0]&&0!=e[0]||e[1]!=n[1]&&0!=e[1]||e[2]!=n[2]||(i=t||n[2])}):"string"==typeof t&&(i=t||n[2]),i&&e.find("div").html('<span class="laydate-day-mark">'+i+"</span>")},x.prototype.mark=function(t,n){function e(e){i.markRender(t,n,e)}var i=this,a=i.config;return a.calendar&&"cn"===a.lang&&e(i.markerOfChineseFestivals),"function"==typeof a.mark?a.mark({year:n[0],month:n[1],date:n[2]},e):"object"==typeof a.mark&&e(a.mark),i},x.prototype.holidaysRender=function(r,l,e){function s(e,t,n){e.find("div").html(["<span",' class="laydate-day-holidays"',' type="'+t+'"',">",n,"</span>"].join(""))}var c=["holidays","workdays"];"array"===layui.type(e)?lay.each(e,function(o,e){lay.each(e,function(e,t){var n=r.attr("lay-ymd"),i=t.split("-"),a=n.split("-");lay.each(i,function(e,t){i[e]=parseInt(t,10)}),lay.each(a,function(e,t){a[e]=parseInt(t,10)}),i.join("-")===a.join("-")&&s(r,c[o],l[2])})}):"string"==typeof e&&-1!==c.indexOf(e)&&s(r,e,l[2])},x.prototype.holidays=function(t,n){function e(e){i.holidaysRender(t,n,e)}var i=this,a=i.config;return"function"==typeof a.holidays?a.holidays({year:n[0],month:n[1],date:n[2]},e):"array"===layui.type(a.holidays)&&e(a.holidays),i},x.prototype.cellRender=function(t,e,n){var i=this.config;return"function"==typeof i.cellRender&&i.cellRender(e,function(e){"string"==typeof e?lay(t).html(e):"object"==typeof e&&lay(t).html("").append(lay(e)[0])},{originElem:t,type:n}),this},x.prototype.startOfYear=function(e){return(e=new Date(e)).setFullYear(e.getFullYear(),0,1),e.setHours(0,0,0,0),e},x.prototype.endOfYear=function(e){var t=(e=new Date(e)).getFullYear();return e.setFullYear(t+1,0,0),e.setHours(23,59,59,999),e},x.prototype.startOfMonth=function(e){return(e=new Date(e)).setDate(1),e.setHours(0,0,0,0),e},x.prototype.endOfMonth=function(e){var t=(e=new Date(e)).getMonth();return e.setFullYear(e.getFullYear(),t+1,0),e.setHours(23,59,59,999),e},x.prototype.addDays=function(e,t){return e=new Date(e),t&&e.setDate(e.getDate()+t),e},x.prototype.isDisabledYearOrMonth=function(e,t,n){for(var i=this,a=i.config,o="year"===t?i.startOfYear(e):i.startOfMonth(e),t="year"===t?i.endOfYear(e):i.endOfMonth(e),r=Math.floor((t.getTime()-o.getTime())/864e5)+1,l=0,s=0;s<r;s++){var c=i.addDays(o,s);a.disabledDate.call(a,c,n)&&l++}return l===r},x.prototype.isDisabledDate=function(e,t){t=t||{};var n=this.config,i=n.range&&0!==t.rangeType?"end":"start";return!!n.disabledDate&&"time"!==n.type&&("date"===t.disabledType||"datetime"===t.disabledType)&&((e=new Date(e)).setHours(0,0,0,0),"year"===t.type||"month"===t.type?this.isDisabledYearOrMonth(e,t.type,i):n.disabledDate.call(n,e,i))},x.prototype.isDisabledTime=function(e,t){t=t||{};var n,i=this.config,a=i.range&&0!==t.rangeType?"end":"start";return!!i.disabledTime&&!("time"!==i.type&&"datetime"!==i.type||"time"!==t.disabledType&&"datetime"!==t.disabledType)&&(n=function(e,t,n){return function(){return-1!==("function"==typeof t&&t.apply(i,n)||[]).indexOf(e)}},e=this.systemDate(new Date(e)),a=i.disabledTime.call(i,this.newDate(e),a)||{},"datetime"===t.disabledType?n(e.hours,a.hours)()||n(e.minutes,a.minutes,[e.hours])()||n(e.seconds,a.seconds,[e.hours,e.minutes])():[n(e.hours,a.hours),n(e.minutes,a.minutes,[e.hours]),n(e.seconds,a.seconds,[e.hours,e.minutes])][t.time.length-1]())},x.prototype.isDisabledDateTime=function(e,t){return this.config,this.isDisabledDate(e,t=t||{})||this.isDisabledTime(e,t)},x.prototype.limit=function(t){t=t||{};var a=this,e=a.config,o={},n=t.index>(t.time?0:41)?a.endDate:e.dateTime;return lay.each({now:lay.extend({},n,t.date||{}),min:e.min,max:e.max},function(e,n){var i;o[e]=a.newDate(lay.extend({year:n.year,month:"year"===t.type?0:n.month,date:"year"===t.type||"month"===t.type?1:n.date},(i={},lay.each(t.time,function(e,t){i[t]=n[t]}),i))).getTime()}),n=o.now<o.min||o.max<o.now||a.isDisabledDateTime(o.now,t),t.elem&&t.elem[n?"addClass":"removeClass"](E),n},x.prototype.thisDateTime=function(e){var t=this.config;return e?this.endDate:t.dateTime},x.prototype.calendar=function(e,a,t){a=a?1:0;var o,r,l,s=this,n=s.config,c=e||s.thisDateTime(a),i=new Date,d=s.lang(),u="date"!==n.type&&"datetime"!==n.type,f=lay(s.table[a]).find("td"),p=lay(s.elemHeader[a][2]).find("span");return c.year<h[0]&&(c.year=h[0],s.hint(d.invalidDate)),c.year>h[1]&&(c.year=h[1],s.hint(d.invalidDate)),s.firstDate||(s.firstDate=lay.extend({},c)),i.setFullYear(c.year,c.month,1),o=(i.getDay()+(7-n.weekStart))%7,r=b.getEndDate(c.month||12,c.year),l=b.getEndDate(c.month+1,c.year),lay.each(f,function(e,t){var n,i=[c.year,c.month];(t=lay(t)).removeAttr("class"),e<o?(n=r-o+e,t.addClass("laydate-day-prev"),i=s.getAsYM(c.year,c.month,"sub")):o<=e&&e<l+o?(n=e-o,s.rangeLinked||n+1===c.date&&t.addClass(T)):(n=e-l-o,t.addClass("laydate-day-next"),i=s.getAsYM(c.year,c.month)),i[1]++,i[2]=n+1,t.attr("lay-ymd",i.join("-")).html("<div>"+i[2]+"</div>"),s.mark(t,i).holidays(t,i).limit({elem:t,date:{year:i[0],month:i[1]-1,date:i[2]},index:e,rangeType:a,disabledType:"date"}),s.cellRender(t,{year:i[0],month:i[1],date:i[2]},"date")}),lay(p[0]).attr("lay-ym",c.year+"-"+(c.month+1)),lay(p[1]).attr("lay-ym",c.year+"-"+(c.month+1)),s.panelYM||(s.panelYM={}),s.panelYM[a]={year:c.year,month:c.month},"cn"===n.lang?(lay(p[0]).attr("lay-type","year").html(c.year+" 年"),lay(p[1]).attr("lay-type","month").html(c.month+1+" 月")):(lay(p[0]).attr("lay-type","month").html(d.month[c.month]),lay(p[1]).attr("lay-type","year").html(c.year)),u&&(n.range?!e&&"init"===t||(s.listYM=[[(s.startDate||n.dateTime).year,(s.startDate||n.dateTime).month+1],[s.endDate.year,s.endDate.month+1]],s.list(n.type,0).list(n.type,1),"time"===n.type?s.setBtnStatus("时间",lay.extend({},s.systemDate(),s.startTime),lay.extend({},s.systemDate(),s.endTime)):s.setBtnStatus(!0)):(s.listYM=[[c.year,c.month+1]],s.list(n.type,0))),n.range&&"init"===t&&(s.rangeLinked?(i=s.getAsYM(c.year,c.month,a?"sub":null),s.calendar(lay.extend({},c,{year:i[0],month:i[1]}),1-a)):s.calendar(null,1-a)),n.range||(f=["hours","minutes","seconds"],s.limit({elem:lay(s.footer).find(".laydate-btns-now"),date:s.systemDate(/^(datetime|time)$/.test(n.type)?new Date:null),index:0,time:f,disabledType:"datetime"}),s.limit({elem:lay(s.footer).find(S),index:0,time:f,disabledType:"datetime"})),s.setBtnStatus(),lay(s.shortcut).find("li."+T).removeClass(T),n.range&&!u&&"init"!==t&&s.stampRange(),s},x.prototype.list=function(i,a){var o,r,e,n,l,s,t,c=this,d=c.config,u=c.rangeLinked?d.dateTime:[d.dateTime,c.endDate][a],f=c.lang(),p=d.range&&"date"!==d.type&&"datetime"!==d.type,h=lay.elem("ul",{class:D+" "+{year:"laydate-year-list",month:"laydate-month-list",time:"laydate-time-list"}[i]}),y=c.elemHeader[a],m=lay(y[2]).find("span"),g=c.elemCont[a||0],v=lay(g).find("."+D)[0],x="cn"===d.lang,b=x?"年":"",w=c.listYM[a]||{},k=["hours","minutes","seconds"],C=["startTime","endTime"][a];return w[0]<1&&(w[0]=1),"year"===i?(e=o=w[0]-7,o<1&&(e=o=1),lay.each(new Array(15),function(e){var t=lay.elem("li",{"lay-ym":o}),n={year:o,month:0,date:1};o==w[0]&&lay(t).addClass(T),t.innerHTML=o+b,h.appendChild(t),c.limit({elem:lay(t),date:n,index:a,type:i,rangeType:a,disabledType:"date"}),c.cellRender(t,{year:o,month:1,date:1},"year"),o++}),lay(m[x?0:1]).attr("lay-ym",o-8+"-"+w[1]).html(e+b+" - "+(o-1)+b)):"month"===i?(lay.each(new Array(12),function(e){var t=lay.elem("li",{"lay-ym":e}),n={year:w[0],month:e,date:1};e+1==w[1]&&lay(t).addClass(T),t.innerHTML=f.month[e]+(x?"月":""),h.appendChild(t),c.limit({elem:lay(t),date:n,index:a,type:i,rangeType:a,disabledType:"date"}),c.cellRender(t,{year:w[0],month:e+1,date:1},"month")}),lay(m[x?0:1]).attr("lay-ym",w[0]+"-"+w[1]).html(w[0]+b)):"time"===i&&(r=function(){lay(h).find("ol").each(function(n,e){lay(e).find("li").each(function(e,t){c.limit({elem:lay(t),date:[{hours:e},{hours:c[C].hours,minutes:e},{hours:c[C].hours,minutes:c[C].minutes,seconds:e}][n],index:a,rangeType:a,disabledType:"time",time:[["hours"],["hours","minutes"],["hours","minutes","seconds"]][n]})})}),d.range||c.limit({elem:lay(c.footer).find(S),date:c[C],index:0,time:["hours","minutes","seconds"],disabledType:"datetime"})},d.range?c[C]||(c[C]="startTime"===C?u:c.endDate):c[C]=u,lay.each([24,60,60],function(t,e){var n=lay.elem("li"),i=["<p>"+f.time[t]+"</p><ol>"];lay.each(new Array(e),function(e){i.push("<li"+(c[C][k[t]]===e?' class="'+T+'"':"")+">"+lay.digit(e,2)+"</li>")}),n.innerHTML=i.join("")+"</ol>",h.appendChild(n)}),r(),e=-1!==d.format.indexOf("H"),m=-1!==d.format.indexOf("m"),t=-1!==d.format.indexOf("s"),n=h.children,l=0,lay.each([e,m,t],function(e,t){t||(n[e].className+=" layui-hide",l++)}),h.className+=" laydate-time-list-hide-"+l),v&&g.removeChild(v),g.appendChild(h),"year"===i||"month"===i?(lay(c.elemMain[a]).addClass("laydate-ym-show"),lay(h).find("li").on("click",function(){var e,t,n=0|lay(this).attr("lay-ym");lay(this).hasClass(E)||(c.rangeLinked?lay.extend(u,{year:"year"===i?n:w[0],month:"year"===i?w[1]-1:n}):u[i]=n,e=-1!==["year","month"].indexOf(d.type),t="year"===i&&-1!==["date","datetime"].indexOf(d.type),e||t?(lay(h).find("."+T).removeClass(T),lay(this).addClass(T),("month"===d.type&&"year"===i||t)&&(c.listYM[a][0]=n,p&&((a?c.endDate:u).year=n),c.list("month",a))):(c.checkDate("limit").calendar(u,a,"init"),c.closeList()),c.setBtnStatus(),!d.range&&d.autoConfirm&&("month"===d.type&&"month"===i||"year"===d.type&&"year"===i)&&c.setValue(c.parse()).done().remove(),c.autoCalendarModel.auto&&!c.rangeLinked?c.choose(lay(g).find("td.layui-this"),a):c.endState&&c.done(null,"change"),lay(c.footer).find("."+L).removeClass(E))})):(m=lay.elem("span",{class:N}),s=function(){lay(h).find("ol").each(function(e){var n=this,t=lay(n).find("li");n.scrollTop=30*(c[C][k[e]]-2),n.scrollTop<=0&&t.each(function(e,t){if(!lay(this).hasClass(E))return n.scrollTop=30*(e-2),!0})})},t=lay(y[2]).find("."+N),s(),m.innerHTML=d.range?[f.startTime,f.endTime][a]:f.timeTips,lay(c.elemMain[a]).addClass("laydate-time-show"),t[0]&&t.remove(),y[2].appendChild(m),(v=lay(h).find("ol")).each(function(t){var n=this;lay(n).find("li").on("click",function(){var e=0|this.innerHTML;lay(this).hasClass(E)||(d.range?c[C][k[t]]=e:u[k[t]]=e,lay(n).find("."+T).removeClass(T),lay(this).addClass(T),r(),s(),!c.endDate&&"time"!==d.type&&"datetime"!==d.type||c.done(null,"change"),c.setBtnStatus())})}),layui.device().mobile&&v.css({overflowY:"auto",touchAction:"pan-y"})),c},x.prototype.listYM=[],x.prototype.closeList=function(){var n=this;n.config,lay.each(n.elemCont,function(e,t){lay(this).find("."+D).remove(),lay(n.elemMain[e]).removeClass("laydate-ym-show laydate-time-show")}),lay(n.elem).find("."+N).remove()},x.prototype.setBtnStatus=function(e,t,n){var i=this,a=i.config,o=i.lang(),r=lay(i.footer).find(S),l="datetime"===a.type||"time"===a.type?["hours","minutes","seconds"]:void 0;a.range&&(t=t||(i.rangeLinked?i.startDate:a.dateTime),n=n||i.endDate,a=!i.endState||i.newDate(t).getTime()>i.newDate(n).getTime(),i.limit({date:t,disabledType:"datetime",time:l,rangeType:0})||i.limit({date:n,disabledType:"datetime",time:l,rangeType:1})?r.addClass(E):r[a?"addClass":"removeClass"](E),e)&&a&&i.hint("string"==typeof e?o.timeout.replace(/\u65e5\u671f/g,e):o.timeout)},x.prototype.parse=function(e,t){var n=this,i=n.config,a=n.rangeLinked?n.startDate:i.dateTime,t=t||("end"==e?lay.extend({},n.endDate,n.endTime):i.range?lay.extend({},a||i.dateTime,n.startTime):i.dateTime),a=b.parse(t,n.format,1);return i.range&&void 0===e?a+" "+n.rangeStr+" "+n.parse("end"):a},x.prototype.newDate=function(e){return e=e||{},new Date(e.year||1,e.month||0,e.date||1,e.hours||0,e.minutes||0,e.seconds||0)},x.prototype.getDateTime=function(e){return this.newDate(e).getTime()},x.prototype.formatToDisplay=function(e,t){var n=this,i=Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,"value");Object.defineProperty(e,"value",lay.extend({},i,{get:function(){return this.getAttribute("lay-date")},set:function(e){i.set.call(this,t.call(n,e)),this.setAttribute("lay-date",e)}}))},x.prototype.setValue=function(e){var t,n=this,i=n.config,a=i.elem[0];return"static"!==i.position&&(e=e||"",n.isInput(a)?lay(a).val(e):(t=n.rangeElem)?("array"!==layui.type(e)&&(e=e.split(" "+n.rangeStr+" ")),t[0].val(e[0]||""),t[1].val(e[1]||"")):(0===lay(a).find("*").length&&(t="function"==typeof i.formatToDisplay?i.formatToDisplay(e):e,lay(a).html(t)),lay(a).attr("lay-date",e))),n},x.prototype.preview=function(){var e,t=this,n=t.config;n.isPreview&&(e=lay(t.elem).find("."+k),n=!n.range||(t.rangeLinked?t.endState:t.endDate)?t.parse():"",e.html(n),e.html())&&(e.css({color:"#16b777"}),setTimeout(function(){e.css({color:"#777"})},300))},x.prototype.renderAdditional=function(){this.config.fullPanel&&this.list("time",0)},x.prototype.stampRange=function(){var i,a=this,o=a.config,r=a.rangeLinked?a.startDate:o.dateTime,e=lay(a.elem).find("td");o.range&&!a.endState&&lay(a.footer).find(S).addClass(E),r=r&&a.newDate({year:r.year,month:r.month,date:r.date}).getTime(),i=a.endState&&a.endDate&&a.newDate({year:a.endDate.year,month:a.endDate.month,date:a.endDate.date}).getTime(),lay.each(e,function(e,t){var n=lay(t).attr("lay-ymd").split("-"),n=a.newDate({year:n[0],month:n[1]-1,date:n[2]}).getTime();o.rangeLinked&&!a.startDate&&n===a.newDate(a.systemDate()).getTime()&&lay(t).addClass(lay(t).hasClass(d)||lay(t).hasClass(u)?"":"laydate-day-now"),lay(t).removeClass(l+" "+T),n!==r&&n!==i||(a.rangeLinked||!a.rangeLinked&&(e<42?n===r:n===i))&&lay(t).addClass(lay(t).hasClass(d)||lay(t).hasClass(u)?l:T),r<n&&n<i&&lay(t).addClass(l)})},x.prototype.done=function(e,t){var n=this,i=n.config,a=lay.extend({},lay.extend(n.rangeLinked?n.startDate:i.dateTime,n.startTime)),o=lay.extend({},lay.extend(n.endDate,n.endTime));return lay.each([a,o],function(e,t){"month"in t&&lay.extend(t,{month:t.month+1})}),n.preview(),e=e||[n.parse(),a,o],"change"===t&&n.renderAdditional(),"function"==typeof i[t||"done"]&&i[t||"done"].apply(i,e),n},x.prototype.checkPanelDate=function(e,t){var n,i=this.config;if("date"===i.type||"datetime"===i.type)return i=0===t,{needFullRender:(e=e.month+1)!==(n=this.panelYM[t].month+1),index:i=this.endState&&(i&&n<e||!i&&e<n)?1-t:t}},x.prototype.choose=function(e,n){var i,a,t,o,r,l,s;e.hasClass(E)||(a=(i=this).config,t=n,i.rangeLinked&&(i.endState||!i.startDate?(n=0,i.endState=!1):(n=1,i.endState=!0)),o=i.thisDateTime(n),lay(i.elem).find("td"),e={year:0|(e=e.attr("lay-ymd").split("-"))[0],month:(0|e[1])-1,date:0|e[2]},lay.extend(o,e),a.range?(lay.each(["startTime","endTime"],function(e,t){i[t]=i[t]||{hours:e?23:0,minutes:e?59:0,seconds:e?59:0},n===e&&(i.getDateTime(lay.extend({},o,i[t]))<i.getDateTime(a.min)?(i[t]={hours:a.min.hours,minutes:a.min.minutes,seconds:a.min.seconds},lay.extend(o,i[t])):i.getDateTime(lay.extend({},o,i[t]))>i.getDateTime(a.max)&&(i[t]={hours:a.max.hours,minutes:a.max.minutes,seconds:a.max.seconds},lay.extend(o,i[t])))}),n||(i.startDate=lay.extend({},o)),i.endState&&!i.limit({date:i.rangeLinked?i.startDate:i.thisDateTime(1-n),disabledType:"date"})&&(((r=i.endState&&i.autoCalendarModel.auto?i.autoCalendarModel():r)||i.rangeLinked&&i.endState)&&i.newDate(i.startDate)>i.newDate(i.endDate)&&(i.startDate.year===i.endDate.year&&i.startDate.month===i.endDate.month&&i.startDate.date===i.endDate.date&&(l=i.startTime,i.startTime=i.endTime,i.endTime=l),l=i.startDate,i.startDate=lay.extend({},i.endDate,i.startTime),a.dateTime=lay.extend({},i.startDate),i.endDate=lay.extend({},l,i.endTime)),r)&&(a.dateTime=lay.extend({},i.startDate)),i.rangeLinked?(e=i.checkPanelDate(o,t),l=lay.extend({},o),s=r||e&&e.needFullRender?"init":null,e=e?e.index:t,i.calendar(l,e,s)):i.calendar(null,n,r?"init":null),i.endState&&i.done(null,"change")):"static"===a.position?i.calendar().done().done(null,"change"):"date"===a.type?a.autoConfirm?i.setValue(i.parse()).done().remove():i.calendar().done(null,"change"):"datetime"===a.type&&i.calendar().done(null,"change"))},x.prototype.tool=function(t,e){var n=this,i=n.config,a=n.lang(),o=i.dateTime,r="static"===i.position,l={datetime:function(){lay(t).hasClass(E)||(n.list("time",0),i.range&&n.list("time",1),lay(t).attr("lay-type","date").html(n.lang().dateTips))},date:function(){n.closeList(),lay(t).attr("lay-type","datetime").html(n.lang().timeTips)},clear:function(){r&&(lay.extend(o,n.firstDate),n.calendar()),i.range&&(delete i.dateTime,delete n.endDate,delete n.startTime,delete n.endTime),n.setValue(""),n.done(null,"onClear").done(["",{},{}]).remove()},now:function(){var e=new Date;if(lay(t).hasClass(E))return n.hint(a.tools.now+", "+a.invalidDate);lay.extend(o,n.systemDate(),{hours:e.getHours(),minutes:e.getMinutes(),seconds:e.getSeconds()}),n.setValue(n.parse()),r&&n.calendar(),n.done(null,"onNow").done().remove()},confirm:function(){if(i.range){if(lay(t).hasClass(E))return("time"===i.type?n.startTime&&n.endTime&&n.newDate(n.startTime)>n.newDate(n.endTime):n.startDate&&n.endDate&&n.newDate(lay.extend({},n.startDate,n.startTime||{}))>n.newDate(lay.extend({},n.endDate,n.endTime||{})))?n.hint("time"===i.type?a.timeout.replace(/\u65e5\u671f/g,"时间"):a.timeout):n.hint(a.invalidDate)}else if(lay(t).hasClass(E))return n.hint(a.invalidDate);n.setValue(n.parse()),n.done(null,"onConfirm").done().remove()}};l[e]&&l[e]()},x.prototype.change=function(i){function e(e){var t=lay(s).find(".laydate-year-list")[0],n=lay(s).find(".laydate-month-list")[0];return t&&(c[0]=e?c[0]-15:c[0]+15,a.list("year",i)),n&&(e?c[0]--:c[0]++,a.list("month",i)),(t||n)&&(lay.extend(r,{year:c[0]}),l&&(r.year=c[0]),o.range||a.done(null,"change"),o.range||a.limit({elem:lay(a.footer).find(S),date:{year:c[0]},disabledType:"datetime"})),a.setBtnStatus(),t||n}var a=this,o=a.config,r=a.thisDateTime(i),l=o.range&&("year"===o.type||"month"===o.type),s=a.elemCont[i||0],c=a.listYM[i];return{prevYear:function(){e("sub")||(a.rangeLinked?(o.dateTime.year--,a.checkDate("limit").calendar(null,null,"init")):(r.year--,a.checkDate("limit").calendar(null,i),a.autoCalendarModel.auto?a.choose(lay(s).find("td.layui-this"),i):a.done(null,"change")))},prevMonth:function(){var e,t;a.rangeLinked?(t=a.panelYM[0],t=a.getAsYM(t.year,t.month,"sub"),e=lay.extend({},o.dateTime,a.panelYM[0],{year:t[0],month:t[1]}),a.checkDate("limit").calendar(e,null,"init")):(t=a.getAsYM(r.year,r.month,"sub"),lay.extend(r,{year:t[0],month:t[1]}),a.checkDate("limit").calendar(null,null,"init"),a.autoCalendarModel.auto?a.choose(lay(s).find("td.layui-this"),i):a.done(null,"change"))},nextMonth:function(){var e,t;a.rangeLinked?(t=a.panelYM[0],t=a.getAsYM(t.year,t.month),e=lay.extend({},o.dateTime,a.panelYM[0],{year:t[0],month:t[1]}),a.checkDate("limit").calendar(e,null,"init")):(t=a.getAsYM(r.year,r.month),lay.extend(r,{year:t[0],month:t[1]}),a.checkDate("limit").calendar(null,null,"init"),a.autoCalendarModel.auto?a.choose(lay(s).find("td.layui-this"),i):a.done(null,"change"))},nextYear:function(){e()||(a.rangeLinked?(o.dateTime.year++,a.checkDate("limit").calendar(null,0,"init")):(r.year++,a.checkDate("limit").calendar(null,i),a.autoCalendarModel.auto?a.choose(lay(s).find("td.layui-this"),i):a.done(null,"change")))}}},x.prototype.changeEvent=function(){var a=this;a.config,lay(a.elem).on("click",function(e){lay.stope(e)}).on("mousedown",function(e){lay.stope(e)}),lay.each(a.elemHeader,function(i,e){lay(e[0]).on("click",function(e){a.change(i).prevYear()}),lay(e[1]).on("click",function(e){a.change(i).prevMonth()}),lay(e[2]).find("span").on("click",function(e){var t=(n=lay(this)).attr("lay-ym"),n=n.attr("lay-type");t&&(t=t.split("-"),a.listYM[i]=[0|t[0],0|t[1]],a.list(n,i),lay(a.footer).find("."+L).addClass(E))}),lay(e[3]).on("click",function(e){a.change(i).nextMonth()}),lay(e[4]).on("click",function(e){a.change(i).nextYear()})}),lay.each(a.table,function(e,t){lay(t).find("td").on("click",function(){a.choose(lay(this),e)})}),lay(a.footer).find("span").on("click",function(){var e=lay(this).attr("lay-type");a.tool(this,e)})},x.prototype.isInput=function(e){return/input|textarea/.test(e.tagName.toLocaleLowerCase())||/INPUT|TEXTAREA/.test(e.tagName)},x.prototype.events=function(){var e,t=this,n=t.config;n.elem[0]&&!n.elem[0].eventHandler&&(n.elem.on(n.trigger,e=function(){b.thisId!==n.id&&t.render()}),n.elem[0].eventHandler=!0,n.eventElem.on(n.trigger,e),t.unbind=function(){t.remove(),n.elem.off(n.trigger,e),n.elem.removeAttr("lay-key"),n.elem.removeAttr(s),n.elem[0].eventHandler=!1,n.eventElem.off(n.trigger,e),n.eventElem.removeAttr("lay-key"),delete o.that[n.id]})},o.that={},o.getThis=function(e){var t=o.that[e];return!t&&i&&layui.hint().error(e?n+" instance with ID '"+e+"' not found":"ID argument required"),t},r.run=function(i){i(v).on("mousedown",function(e){var t,n;b.thisId&&(t=o.getThis(b.thisId))&&(n=t.config,e.target===n.elem[0]||e.target===n.eventElem[0]||e.target===i(n.closeStop)[0]||n.elem[0]&&n.elem[0].contains(e.target)||t.remove())}).on("keydown",function(e){var t;b.thisId&&(t=o.getThis(b.thisId))&&"static"!==t.config.position&&13===e.keyCode&&i("#"+t.elemID)[0]&&t.elemID===x.thisElemDate&&(e.preventDefault(),i(t.footer).find(S)[0].click())}),i(a).on("resize",function(){if(b.thisId){var e=o.getThis(b.thisId);if(e)return!(!e.elem||!i(".layui-laydate")[0])&&void e.position()}})},b.render=function(e){return e=new x(e),o.call(e)},b.reload=function(e,t){if(e=o.getThis(e))return e.reload(t)},b.getInst=function(e){if(e=o.getThis(e))return e.inst},b.hint=function(e,t){if(e=o.getThis(e))return e.hint(t)},b.unbind=function(e){if(e=o.getThis(e))return e.unbind()},b.close=function(e){if(e=o.getThis(e||b.thisId))return e.remove()},b.parse=function(n,i,a){return n=n||{},i=((i="string"==typeof i?o.formatArr(i):i)||[]).concat(),lay.each(i,function(e,t){/yyyy|y/.test(t)?i[e]=lay.digit(n.year,t.length):/MM|M/.test(t)?i[e]=lay.digit(n.month+(a||0),t.length):/dd|d/.test(t)?i[e]=lay.digit(n.date,t.length):/HH|H/.test(t)?i[e]=lay.digit(n.hours,t.length):/mm|m/.test(t)?i[e]=lay.digit(n.minutes,t.length):/ss|s/.test(t)&&(i[e]=lay.digit(n.seconds,t.length))}),i.join("")},b.getEndDate=function(e,t){var n=new Date;return n.setFullYear(t||n.getFullYear(),e||n.getMonth()+1,1),new Date(n.getTime()-864e5).getDate()},i?(b.ready(),layui.define("lay",function(e){b.path=layui.cache.dir,r.run(lay),e(n,b)})):"function"==typeof define&&define.amd?define(function(){return r.run(lay),b}):(b.ready(),r.run(a.lay),a.laydate=b)}(window,window.document),function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)}("undefined"!=typeof window?window:this,function(w,F){function O(e,t){return t.toUpperCase()}var u=[],h=w.document,d=u.slice,R=u.concat,P=u.push,q=u.indexOf,B={},z=B.toString,y=B.hasOwnProperty,m={},e="1.12.4",k=function(e,t){return new k.fn.init(e,t)},W=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,Y=/^-ms-/,$=/-([\da-z])/gi;function X(e){var t=!!e&&"length"in e&&e.length,n=k.type(e);return"function"!==n&&!k.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}k.fn=k.prototype={jquery:e,constructor:k,selector:"",length:0,toArray:function(){return d.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:d.call(this)},pushStack:function(e){return(e=k.merge(this.constructor(),e)).prevObject=this,e.context=this.context,e},each:function(e){return k.each(this,e)},map:function(n){return this.pushStack(k.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(d.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:P,sort:u.sort,splice:u.splice},k.extend=k.fn.extend=function(){var e,t,n,i,a,o=arguments[0]||{},r=1,l=arguments.length,s=!1;for("boolean"==typeof o&&(s=o,o=arguments[r]||{},r++),"object"==typeof o||k.isFunction(o)||(o={}),r===l&&(o=this,r--);r<l;r++)if(null!=(i=arguments[r]))for(n in i)a=o[n],o!==(t=i[n])&&(s&&t&&(k.isPlainObject(t)||(e=k.isArray(t)))?(a=e?(e=!1,a&&k.isArray(a)?a:[]):a&&k.isPlainObject(a)?a:{},o[n]=k.extend(s,a,t)):void 0!==t&&(o[n]=t));return o},k.extend({expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===k.type(e)},isArray:Array.isArray||function(e){return"array"===k.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){var t=e&&e.toString();return!k.isArray(e)&&0<=t-parseFloat(t)+1},isEmptyObject:function(e){for(var t in e)return!1;return!0},isPlainObject:function(e){if(!e||"object"!==k.type(e)||e.nodeType||k.isWindow(e))return!1;try{if(e.constructor&&!y.call(e,"constructor")&&!y.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(!m.ownFirst)for(var t in e)return y.call(e,t);for(t in e);return void 0===t||y.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?B[z.call(e)]||"object":typeof e},globalEval:function(e){e&&k.trim(e)&&(w.execScript||function(e){w.eval.call(w,e)})(e)},camelCase:function(e){return e.replace(Y,"ms-").replace($,O)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,i=0;if(X(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(W,"")},makeArray:function(e,t){return t=t||[],null!=e&&(X(Object(e))?k.merge(t,"string"==typeof e?[e]:e):P.call(t,e)),t},inArray:function(e,t,n){var i;if(t){if(q)return q.call(t,e,n);for(i=t.length,n=n?n<0?Math.max(0,i+n):n:0;n<i;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,a=e.length;i<n;)e[a++]=t[i++];if(n!=n)for(;void 0!==t[i];)e[a++]=t[i++];return e.length=a,e},grep:function(e,t,n){for(var i=[],a=0,o=e.length,r=!n;a<o;a++)!t(e[a],a)!=r&&i.push(e[a]);return i},map:function(e,t,n){var i,a,o=0,r=[];if(X(e))for(i=e.length;o<i;o++)null!=(a=t(e[o],o,n))&&r.push(a);else for(o in e)null!=(a=t(e[o],o,n))&&r.push(a);return R.apply([],r)},guid:1,proxy:function(e,t){var n,i;return"string"==typeof t&&(i=e[t],t=e,e=i),k.isFunction(e)?(n=d.call(arguments,2),(i=function(){return e.apply(t||this,n.concat(d.call(arguments)))}).guid=e.guid=e.guid||k.guid++,i):void 0},now:function(){return+new Date},support:m}),"function"==typeof Symbol&&(k.fn[Symbol.iterator]=u[Symbol.iterator]),k.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){B["[object "+t+"]"]=t.toLowerCase()});function i(e,t,n){for(var i=[],a=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(a&&k(e).is(n))break;i.push(e)}return i}function V(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var e=function(F){function b(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1}function u(e,t,n){var i="0x"+t-65536;return i!=i||n?t:i<0?String.fromCharCode(65536+i):String.fromCharCode(i>>10|55296,1023&i|56320)}function O(){C()}var e,h,w,o,R,y,P,q,k,s,c,C,T,t,E,m,i,a,g,D="sizzle"+ +new Date,v=F.document,S=0,B=0,z=ce(),W=ce(),N=ce(),Y=function(e,t){return e===t&&(c=!0),0},$={}.hasOwnProperty,n=[],X=n.pop,V=n.push,L=n.push,U=n.slice,G="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",r="[\\x20\\t\\r\\n\\f]",l="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",K="\\["+r+"*("+l+")(?:"+r+"*([*^$|!~]?=)"+r+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+l+"))|)"+r+"*\\]",J=":("+l+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+K+")*)|.*)\\)|)",Z=new RegExp(r+"+","g"),A=new RegExp("^"+r+"+|((?:^|[^\\\\])(?:\\\\.)*)"+r+"+$","g"),Q=new RegExp("^"+r+"*,"+r+"*"),ee=new RegExp("^"+r+"*([>+~]|"+r+")"+r+"*"),te=new RegExp("="+r+"*([^\\]'\"]*?)"+r+"*\\]","g"),ne=new RegExp(J),ie=new RegExp("^"+l+"$"),f={ID:new RegExp("^#("+l+")"),CLASS:new RegExp("^\\.("+l+")"),TAG:new RegExp("^("+l+"|[*])"),ATTR:new RegExp("^"+K),PSEUDO:new RegExp("^"+J),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+r+"*(even|odd|(([+-]|)(\\d*)n|)"+r+"*(?:([+-]|)"+r+"*(\\d+)|))"+r+"*\\)|)","i"),bool:new RegExp("^(?:"+G+")$","i"),needsContext:new RegExp("^"+r+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+r+"*((?:-\\d)?\\d*)"+r+"*\\)|)(?=[^-]|$)","i")},ae=/^(?:input|select|textarea|button)$/i,oe=/^h\d$/i,d=/^[^{]+\{\s*\[native \w/,re=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,le=/[+~]/,se=/'|\\/g,p=new RegExp("\\\\([\\da-f]{1,6}"+r+"?|("+r+")|.)","ig");try{L.apply(n=U.call(v.childNodes),v.childNodes),n[v.childNodes.length].nodeType}catch(e){L={apply:n.length?function(e,t){V.apply(e,U.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function I(e,t,n,i){var a,o,r,l,s,c,d,u,f=t&&t.ownerDocument,p=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==p&&9!==p&&11!==p)return n;if(!i&&((t?t.ownerDocument||t:v)!==T&&C(t),t=t||T,E)){if(11!==p&&(c=re.exec(e)))if(a=c[1]){if(9===p){if(!(r=t.getElementById(a)))return n;if(r.id===a)return n.push(r),n}else if(f&&(r=f.getElementById(a))&&g(t,r)&&r.id===a)return n.push(r),n}else{if(c[2])return L.apply(n,t.getElementsByTagName(e)),n;if((a=c[3])&&h.getElementsByClassName&&t.getElementsByClassName)return L.apply(n,t.getElementsByClassName(a)),n}if(h.qsa&&!N[e+" "]&&(!m||!m.test(e))){if(1!==p)f=t,u=e;else if("object"!==t.nodeName.toLowerCase()){for((l=t.getAttribute("id"))?l=l.replace(se,"\\$&"):t.setAttribute("id",l=D),o=(d=y(e)).length,s=ie.test(l)?"#"+l:"[id='"+l+"']";o--;)d[o]=s+" "+_(d[o]);u=d.join(","),f=le.test(e)&&fe(t.parentNode)||t}if(u)try{return L.apply(n,f.querySelectorAll(u)),n}catch(e){}finally{l===D&&t.removeAttribute("id")}}}return q(e.replace(A,"$1"),t,n,i)}function ce(){var n=[];function i(e,t){return n.push(e+" ")>w.cacheLength&&delete i[n.shift()],i[e+" "]=t}return i}function M(e){return e[D]=!0,e}function x(e){var t=T.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function de(e,t){for(var n=e.split("|"),i=n.length;i--;)w.attrHandle[n[i]]=t}function ue(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function j(r){return M(function(o){return o=+o,M(function(e,t){for(var n,i=r([],e.length,o),a=i.length;a--;)e[n=i[a]]&&(e[n]=!(t[n]=e[n]))})})}function fe(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in h=I.support={},R=I.isXML=function(e){return!!(e=e&&(e.ownerDocument||e).documentElement)&&"HTML"!==e.nodeName},C=I.setDocument=function(e){return(e=e?e.ownerDocument||e:v)!==T&&9===e.nodeType&&e.documentElement&&(t=(T=e).documentElement,E=!R(T),(e=T.defaultView)&&e.top!==e&&(e.addEventListener?e.addEventListener("unload",O,!1):e.attachEvent&&e.attachEvent("onunload",O)),h.attributes=x(function(e){return e.className="i",!e.getAttribute("className")}),h.getElementsByTagName=x(function(e){return e.appendChild(T.createComment("")),!e.getElementsByTagName("*").length}),h.getElementsByClassName=d.test(T.getElementsByClassName),h.getById=x(function(e){return t.appendChild(e).id=D,!T.getElementsByName||!T.getElementsByName(D).length}),h.getById?(w.find.ID=function(e,t){if(void 0!==t.getElementById&&E)return(e=t.getElementById(e))?[e]:[]},w.filter.ID=function(e){var t=e.replace(p,u);return function(e){return e.getAttribute("id")===t}}):(delete w.find.ID,w.filter.ID=function(e){var t=e.replace(p,u);return function(e){return(e=void 0!==e.getAttributeNode&&e.getAttributeNode("id"))&&e.value===t}}),w.find.TAG=h.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):h.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],a=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[a++];)1===n.nodeType&&i.push(n);return i},w.find.CLASS=h.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&E)return t.getElementsByClassName(e)},i=[],m=[],(h.qsa=d.test(T.querySelectorAll))&&(x(function(e){t.appendChild(e).innerHTML="<a id='"+D+"'></a><select id='"+D+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+r+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+r+"*(?:value|"+G+")"),e.querySelectorAll("[id~="+D+"-]").length||m.push("~="),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+D+"+*").length||m.push(".#.+[+~]")}),x(function(e){var t=T.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+r+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(h.matchesSelector=d.test(a=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.msMatchesSelector))&&x(function(e){h.disconnectedMatch=a.call(e,"div"),a.call(e,"[s!='']:x"),i.push("!=",J)}),m=m.length&&new RegExp(m.join("|")),i=i.length&&new RegExp(i.join("|")),e=d.test(t.compareDocumentPosition),g=e||d.test(t.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e;return e===(t=t&&t.parentNode)||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Y=e?function(e,t){var n;return e===t?(c=!0,0):!e.compareDocumentPosition-!t.compareDocumentPosition||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!h.sortDetached&&t.compareDocumentPosition(e)===n?e===T||e.ownerDocument===v&&g(v,e)?-1:t===T||t.ownerDocument===v&&g(v,t)?1:s?b(s,e)-b(s,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,i=0,a=e.parentNode,o=t.parentNode,r=[e],l=[t];if(!a||!o)return e===T?-1:t===T?1:a?-1:o?1:s?b(s,e)-b(s,t):0;if(a===o)return ue(e,t);for(n=e;n=n.parentNode;)r.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;r[i]===l[i];)i++;return i?ue(r[i],l[i]):r[i]===v?-1:l[i]===v?1:0}),T},I.matches=function(e,t){return I(e,null,null,t)},I.matchesSelector=function(e,t){if((e.ownerDocument||e)!==T&&C(e),t=t.replace(te,"='$1']"),h.matchesSelector&&E&&!N[t+" "]&&(!i||!i.test(t))&&(!m||!m.test(t)))try{var n=a.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return 0<I(t,T,null,[e]).length},I.contains=function(e,t){return(e.ownerDocument||e)!==T&&C(e),g(e,t)},I.attr=function(e,t){(e.ownerDocument||e)!==T&&C(e);var n=w.attrHandle[t.toLowerCase()];return void 0!==(n=n&&$.call(w.attrHandle,t.toLowerCase())?n(e,t,!E):void 0)?n:h.attributes||!E?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},I.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},I.uniqueSort=function(e){var t,n=[],i=0,a=0;if(c=!h.detectDuplicates,s=!h.sortStable&&e.slice(0),e.sort(Y),c){for(;t=e[a++];)t===e[a]&&(i=n.push(a));for(;i--;)e.splice(n[i],1)}return s=null,e},o=I.getText=function(e){var t,n="",i=0,a=e.nodeType;if(a){if(1===a||9===a||11===a){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===a||4===a)return e.nodeValue}else for(;t=e[i++];)n+=o(t);return n},(w=I.selectors={cacheLength:50,createPseudo:M,match:f,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(p,u),e[3]=(e[3]||e[4]||e[5]||"").replace(p,u),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||I.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&I.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return f.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&ne.test(n)&&(t=(t=y(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(p,u).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=z[e+" "];return t||(t=new RegExp("(^|"+r+")"+e+"("+r+"|$)"))&&z(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(e){return null==(e=I.attr(e,t))?"!="===n:!n||(e+="","="===n?e===i:"!="===n?e!==i:"^="===n?i&&0===e.indexOf(i):"*="===n?i&&-1<e.indexOf(i):"$="===n?i&&e.slice(-i.length)===i:"~="===n?-1<(" "+e.replace(Z," ")+" ").indexOf(i):"|="===n&&(e===i||e.slice(0,i.length+1)===i+"-"))}},CHILD:function(h,e,t,y,m){var g="nth"!==h.slice(0,3),v="last"!==h.slice(-4),x="of-type"===e;return 1===y&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,a,o,r,l,s,c=g!=v?"nextSibling":"previousSibling",d=e.parentNode,u=x&&e.nodeName.toLowerCase(),f=!n&&!x,p=!1;if(d){if(g){for(;c;){for(r=e;r=r[c];)if(x?r.nodeName.toLowerCase()===u:1===r.nodeType)return!1;s=c="only"===h&&!s&&"nextSibling"}return!0}if(s=[v?d.firstChild:d.lastChild],v&&f){for(p=(l=(i=(a=(o=(r=d)[D]||(r[D]={}))[r.uniqueID]||(o[r.uniqueID]={}))[h]||[])[0]===S&&i[1])&&i[2],r=l&&d.childNodes[l];r=++l&&r&&r[c]||(p=l=0,s.pop());)if(1===r.nodeType&&++p&&r===e){a[h]=[S,l,p];break}}else if(!1===(p=f?l=(i=(a=(o=(r=e)[D]||(r[D]={}))[r.uniqueID]||(o[r.uniqueID]={}))[h]||[])[0]===S&&i[1]:p))for(;(r=++l&&r&&r[c]||(p=l=0,s.pop()))&&((x?r.nodeName.toLowerCase()!==u:1!==r.nodeType)||!++p||(f&&((a=(o=r[D]||(r[D]={}))[r.uniqueID]||(o[r.uniqueID]={}))[h]=[S,p]),r!==e)););return(p-=m)===y||p%y==0&&0<=p/y}}},PSEUDO:function(e,o){var t,r=w.pseudos[e]||w.setFilters[e.toLowerCase()]||I.error("unsupported pseudo: "+e);return r[D]?r(o):1<r.length?(t=[e,e,"",o],w.setFilters.hasOwnProperty(e.toLowerCase())?M(function(e,t){for(var n,i=r(e,o),a=i.length;a--;)e[n=b(e,i[a])]=!(t[n]=i[a])}):function(e){return r(e,0,t)}):r}},pseudos:{not:M(function(e){var i=[],a=[],l=P(e.replace(A,"$1"));return l[D]?M(function(e,t,n,i){for(var a,o=l(e,null,i,[]),r=e.length;r--;)(a=o[r])&&(e[r]=!(t[r]=a))}):function(e,t,n){return i[0]=e,l(i,null,n,a),i[0]=null,!a.pop()}}),has:M(function(t){return function(e){return 0<I(t,e).length}}),contains:M(function(t){return t=t.replace(p,u),function(e){return-1<(e.textContent||e.innerText||o(e)).indexOf(t)}}),lang:M(function(n){return ie.test(n||"")||I.error("unsupported lang: "+n),n=n.replace(p,u).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=F.location&&F.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===t},focus:function(e){return e===T.activeElement&&(!T.hasFocus||T.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},header:function(e){return oe.test(e.nodeName)},input:function(e){return ae.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:j(function(){return[0]}),last:j(function(e,t){return[t-1]}),eq:j(function(e,t,n){return[n<0?n+t:n]}),even:j(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:j(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:j(function(e,t,n){for(var i=n<0?n+t:n;0<=--i;)e.push(i);return e}),gt:j(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})w.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function pe(){}function _(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function he(r,e,t){var l=e.dir,s=t&&"parentNode"===l,c=B++;return e.first?function(e,t,n){for(;e=e[l];)if(1===e.nodeType||s)return r(e,t,n)}:function(e,t,n){var i,a,o=[S,c];if(n){for(;e=e[l];)if((1===e.nodeType||s)&&r(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||s){if((i=(a=(a=e[D]||(e[D]={}))[e.uniqueID]||(a[e.uniqueID]={}))[l])&&i[0]===S&&i[1]===c)return o[2]=i[2];if((a[l]=o)[2]=r(e,t,n))return!0}}}function ye(a){return 1<a.length?function(e,t,n){for(var i=a.length;i--;)if(!a[i](e,t,n))return!1;return!0}:a[0]}function H(e,t,n,i,a){for(var o,r=[],l=0,s=e.length,c=null!=t;l<s;l++)!(o=e[l])||n&&!n(o,i,a)||(r.push(o),c&&t.push(l));return r}return pe.prototype=w.filters=w.pseudos,w.setFilters=new pe,y=I.tokenize=function(e,t){var n,i,a,o,r,l,s,c=W[e+" "];if(c)return t?0:c.slice(0);for(r=e,l=[],s=w.preFilter;r;){for(o in n&&!(i=Q.exec(r))||(i&&(r=r.slice(i[0].length)||r),l.push(a=[])),n=!1,(i=ee.exec(r))&&(n=i.shift(),a.push({value:n,type:i[0].replace(A," ")}),r=r.slice(n.length)),w.filter)!(i=f[o].exec(r))||s[o]&&!(i=s[o](i))||(n=i.shift(),a.push({value:n,type:o,matches:i}),r=r.slice(n.length));if(!n)break}return t?r.length:r?I.error(e):W(e,l).slice(0)},P=I.compile=function(e,t){var n,m,g,v,x,i,a=[],o=[],r=N[e+" "];if(!r){for(n=(t=t||y(e)).length;n--;)((r=function e(t){for(var i,n,a,o=t.length,r=w.relative[t[0].type],l=r||w.relative[" "],s=r?1:0,c=he(function(e){return e===i},l,!0),d=he(function(e){return-1<b(i,e)},l,!0),u=[function(e,t,n){return e=!r&&(n||t!==k)||((i=t).nodeType?c:d)(e,t,n),i=null,e}];s<o;s++)if(n=w.relative[t[s].type])u=[he(ye(u),n)];else{if((n=w.filter[t[s].type].apply(null,t[s].matches))[D]){for(a=++s;a<o&&!w.relative[t[a].type];a++);return function e(p,h,y,m,g,t){return m&&!m[D]&&(m=e(m)),g&&!g[D]&&(g=e(g,t)),M(function(e,t,n,i){var a,o,r,l=[],s=[],c=t.length,d=e||function(e,t,n){for(var i=0,a=t.length;i<a;i++)I(e,t[i],n);return n}(h||"*",n.nodeType?[n]:n,[]),u=!p||!e&&h?d:H(d,l,p,n,i),f=y?g||(e?p:c||m)?[]:t:u;if(y&&y(u,f,n,i),m)for(a=H(f,s),m(a,[],n,i),o=a.length;o--;)(r=a[o])&&(f[s[o]]=!(u[s[o]]=r));if(e){if(g||p){if(g){for(a=[],o=f.length;o--;)(r=f[o])&&a.push(u[o]=r);g(null,f=[],a,i)}for(o=f.length;o--;)(r=f[o])&&-1<(a=g?b(e,r):l[o])&&(e[a]=!(t[a]=r))}}else f=H(f===t?f.splice(c,f.length):f),g?g(null,t,f,i):L.apply(t,f)})}(1<s&&ye(u),1<s&&_(t.slice(0,s-1).concat({value:" "===t[s-2].type?"*":""})).replace(A,"$1"),n,s<a&&e(t.slice(s,a)),a<o&&e(t=t.slice(a)),a<o&&_(t))}u.push(n)}return ye(u)}(t[n]))[D]?a:o).push(r);(r=N(e,(v=0<(g=a).length,x=0<(m=o).length,i=function(e,t,n,i,a){var o,r,l,s=0,c="0",d=e&&[],u=[],f=k,p=e||x&&w.find.TAG("*",a),h=S+=null==f?1:Math.random()||.1,y=p.length;for(a&&(k=t===T||t||a);c!==y&&null!=(o=p[c]);c++){if(x&&o){for(r=0,t||o.ownerDocument===T||(C(o),n=!E);l=m[r++];)if(l(o,t||T,n)){i.push(o);break}a&&(S=h)}v&&((o=!l&&o)&&s--,e)&&d.push(o)}if(s+=c,v&&c!==s){for(r=0;l=g[r++];)l(d,u,t,n);if(e){if(0<s)for(;c--;)d[c]||u[c]||(u[c]=X.call(i));u=H(u)}L.apply(i,u),a&&!e&&0<u.length&&1<s+g.length&&I.uniqueSort(i)}return a&&(S=h,k=f),d},v?M(i):i))).selector=e}return r},q=I.select=function(e,t,n,i){var a,o,r,l,s,c="function"==typeof e&&e,d=!i&&y(e=c.selector||e);if(n=n||[],1===d.length){if(2<(o=d[0]=d[0].slice(0)).length&&"ID"===(r=o[0]).type&&h.getById&&9===t.nodeType&&E&&w.relative[o[1].type]){if(!(t=(w.find.ID(r.matches[0].replace(p,u),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(a=f.needsContext.test(e)?0:o.length;a--&&(r=o[a],!w.relative[l=r.type]);)if((s=w.find[l])&&(i=s(r.matches[0].replace(p,u),le.test(o[0].type)&&fe(t.parentNode)||t))){if(o.splice(a,1),e=i.length&&_(o))break;return L.apply(n,i),n}}return(c||P(e,d))(i,t,!E,n,!t||le.test(e)&&fe(t.parentNode)||t),n},h.sortStable=D.split("").sort(Y).join("")===D,h.detectDuplicates=!!c,C(),h.sortDetached=x(function(e){return 1&e.compareDocumentPosition(T.createElement("div"))}),x(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),h.attributes&&x(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),x(function(e){return null==e.getAttribute("disabled")})||de(G,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),I}(w),U=(k.find=e,k.expr=e.selectors,k.expr[":"]=k.expr.pseudos,k.uniqueSort=k.unique=e.uniqueSort,k.text=e.getText,k.isXMLDoc=e.isXML,k.contains=e.contains,k.expr.match.needsContext),G=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,K=/^.[^:#\[\.,]*$/;function J(e,n,i){if(k.isFunction(n))return k.grep(e,function(e,t){return!!n.call(e,t,e)!==i});if(n.nodeType)return k.grep(e,function(e){return e===n!==i});if("string"==typeof n){if(K.test(n))return k.filter(n,e,i);n=k.filter(n,e)}return k.grep(e,function(e){return-1<k.inArray(e,n)!==i})}k.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?k.find.matchesSelector(i,e)?[i]:[]:k.find.matches(e,k.grep(t,function(e){return 1===e.nodeType}))},k.fn.extend({find:function(e){var t,n=[],i=this,a=i.length;if("string"!=typeof e)return this.pushStack(k(e).filter(function(){for(t=0;t<a;t++)if(k.contains(i[t],this))return!0}));for(t=0;t<a;t++)k.find(e,i[t],n);return(n=this.pushStack(1<a?k.unique(n):n)).selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(J(this,e||[],!1))},not:function(e){return this.pushStack(J(this,e||[],!0))},is:function(e){return!!J(this,"string"==typeof e&&U.test(e)?k(e):e||[],!1).length}});var Z,Q=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,ee=((k.fn.init=function(e,t,n){if(e){if(n=n||Z,"string"!=typeof e)return e.nodeType?(this.context=this[0]=e,this.length=1,this):k.isFunction(e)?void 0!==n.ready?n.ready(e):e(k):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),k.makeArray(e,this));if(!(i="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&3<=e.length?[null,e,null]:Q.exec(e))||!i[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(i[1]){if(t=t instanceof k?t[0]:t,k.merge(this,k.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:h,!0)),G.test(i[1])&&k.isPlainObject(t))for(var i in t)k.isFunction(this[i])?this[i](t[i]):this.attr(i,t[i])}else{if((n=h.getElementById(i[2]))&&n.parentNode){if(n.id!==i[2])return Z.find(e);this.length=1,this[0]=n}this.context=h,this.selector=e}}return this}).prototype=k.fn,Z=k(h),/^(?:parents|prev(?:Until|All))/),te={children:!0,contents:!0,next:!0,prev:!0};function ne(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}k.fn.extend({has:function(e){var t,n=k(e,this),i=n.length;return this.filter(function(){for(t=0;t<i;t++)if(k.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,a=this.length,o=[],r=U.test(e)||"string"!=typeof e?k(e,t||this.context):0;i<a;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(r?-1<r.index(n):1===n.nodeType&&k.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?k.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?k.inArray(this[0],k(e)):k.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(k.uniqueSort(k.merge(this.get(),k(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),k.each({parent:function(e){return(e=e.parentNode)&&11!==e.nodeType?e:null},parents:function(e){return i(e,"parentNode")},parentsUntil:function(e,t,n){return i(e,"parentNode",n)},next:function(e){return ne(e,"nextSibling")},prev:function(e){return ne(e,"previousSibling")},nextAll:function(e){return i(e,"nextSibling")},prevAll:function(e){return i(e,"previousSibling")},nextUntil:function(e,t,n){return i(e,"nextSibling",n)},prevUntil:function(e,t,n){return i(e,"previousSibling",n)},siblings:function(e){return V((e.parentNode||{}).firstChild,e)},children:function(e){return V(e.firstChild)},contents:function(e){return k.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:k.merge([],e.childNodes)}},function(i,a){k.fn[i]=function(e,t){var n=k.map(this,a,e);return(t="Until"!==i.slice(-5)?e:t)&&"string"==typeof t&&(n=k.filter(t,n)),1<this.length&&(te[i]||(n=k.uniqueSort(n)),ee.test(i))&&(n=n.reverse()),this.pushStack(n)}});var ie,ae,C=/\S+/g;function oe(){h.addEventListener?(h.removeEventListener("DOMContentLoaded",t),w.removeEventListener("load",t)):(h.detachEvent("onreadystatechange",t),w.detachEvent("onload",t))}function t(){!h.addEventListener&&"load"!==w.event.type&&"complete"!==h.readyState||(oe(),k.ready())}for(ae in k.Callbacks=function(i){var e,n;i="string"==typeof i?(e=i,n={},k.each(e.match(C)||[],function(e,t){n[t]=!0}),n):k.extend({},i);function a(){for(l=i.once,r=o=!0;c.length;d=-1)for(t=c.shift();++d<s.length;)!1===s[d].apply(t[0],t[1])&&i.stopOnFalse&&(d=s.length,t=!1);i.memory||(t=!1),o=!1,l&&(s=t?[]:"")}var o,t,r,l,s=[],c=[],d=-1,u={add:function(){return s&&(t&&!o&&(d=s.length-1,c.push(t)),function n(e){k.each(e,function(e,t){k.isFunction(t)?i.unique&&u.has(t)||s.push(t):t&&t.length&&"string"!==k.type(t)&&n(t)})}(arguments),t)&&!o&&a(),this},remove:function(){return k.each(arguments,function(e,t){for(var n;-1<(n=k.inArray(t,s,n));)s.splice(n,1),n<=d&&d--}),this},has:function(e){return e?-1<k.inArray(e,s):0<s.length},empty:function(){return s=s&&[],this},disable:function(){return l=c=[],s=t="",this},disabled:function(){return!s},lock:function(){return l=!0,t||u.disable(),this},locked:function(){return!!l},fireWith:function(e,t){return l||(t=[e,(t=t||[]).slice?t.slice():t],c.push(t),o)||a(),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!r}};return u},k.extend({Deferred:function(e){var o=[["resolve","done",k.Callbacks("once memory"),"resolved"],["reject","fail",k.Callbacks("once memory"),"rejected"],["notify","progress",k.Callbacks("memory")]],a="pending",r={state:function(){return a},always:function(){return l.done(arguments).fail(arguments),this},then:function(){var a=arguments;return k.Deferred(function(i){k.each(o,function(e,t){var n=k.isFunction(a[e])&&a[e];l[t[1]](function(){var e=n&&n.apply(this,arguments);e&&k.isFunction(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this===r?i.promise():this,n?[e]:arguments)})}),a=null}).promise()},promise:function(e){return null!=e?k.extend(e,r):r}},l={};return r.pipe=r.then,k.each(o,function(e,t){var n=t[2],i=t[3];r[t[1]]=n.add,i&&n.add(function(){a=i},o[1^e][2].disable,o[2][2].lock),l[t[0]]=function(){return l[t[0]+"With"](this===l?r:this,arguments),this},l[t[0]+"With"]=n.fireWith}),r.promise(l),e&&e.call(l,l),l},when:function(e){function t(t,n,i){return function(e){n[t]=this,i[t]=1<arguments.length?d.call(arguments):e,i===a?c.notifyWith(n,i):--s||c.resolveWith(n,i)}}var a,n,i,o=0,r=d.call(arguments),l=r.length,s=1!==l||e&&k.isFunction(e.promise)?l:0,c=1===s?e:k.Deferred();if(1<l)for(a=new Array(l),n=new Array(l),i=new Array(l);o<l;o++)r[o]&&k.isFunction(r[o].promise)?r[o].promise().progress(t(o,n,a)).done(t(o,i,r)).fail(c.reject):--s;return s||c.resolveWith(i,r),c.promise()}}),k.fn.ready=function(e){return k.ready.promise().done(e),this},k.extend({isReady:!1,readyWait:1,holdReady:function(e){e?k.readyWait++:k.ready(!0)},ready:function(e){(!0===e?--k.readyWait:k.isReady)||(k.isReady=!0)!==e&&0<--k.readyWait||(ie.resolveWith(h,[k]),k.fn.triggerHandler&&(k(h).triggerHandler("ready"),k(h).off("ready")))}}),k.ready.promise=function(e){if(!ie)if(ie=k.Deferred(),"complete"===h.readyState||"loading"!==h.readyState&&!h.documentElement.doScroll)w.setTimeout(k.ready);else if(h.addEventListener)h.addEventListener("DOMContentLoaded",t),w.addEventListener("load",t);else{h.attachEvent("onreadystatechange",t),w.attachEvent("onload",t);var n=!1;try{n=null==w.frameElement&&h.documentElement}catch(e){}n&&n.doScroll&&!function t(){if(!k.isReady){try{n.doScroll("left")}catch(e){return w.setTimeout(t,50)}oe(),k.ready()}}()}return ie.promise(e)},k.ready.promise(),k(m))break;m.ownFirst="0"===ae,m.inlineBlockNeedsLayout=!1,k(function(){var e,t,n=h.getElementsByTagName("body")[0];n&&n.style&&(e=h.createElement("div"),(t=h.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(t).appendChild(e),void 0!==e.style.zoom&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",m.inlineBlockNeedsLayout=e=3===e.offsetWidth,e)&&(n.style.zoom=1),n.removeChild(t))}),e=h.createElement("div"),m.deleteExpando=!0;try{delete e.test}catch(e){m.deleteExpando=!1}function g(e){var t=k.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)}var a,re=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,le=/([A-Z])/g;function se(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(le,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:re.test(n)?k.parseJSON(n):n)}catch(e){}k.data(e,t,n)}else n=void 0}return n}function ce(e){for(var t in e)if(("data"!==t||!k.isEmptyObject(e[t]))&&"toJSON"!==t)return;return 1}function de(e,t,n,i){if(g(e)){var a,o=k.expando,r=e.nodeType,l=r?k.cache:e,s=r?e[o]:e[o]&&o;if(s&&l[s]&&(i||l[s].data)||void 0!==n||"string"!=typeof t)return l[s=s||(r?e[o]=u.pop()||k.guid++:o)]||(l[s]=r?{}:{toJSON:k.noop}),"object"!=typeof t&&"function"!=typeof t||(i?l[s]=k.extend(l[s],t):l[s].data=k.extend(l[s].data,t)),e=l[s],i||(e.data||(e.data={}),e=e.data),void 0!==n&&(e[k.camelCase(t)]=n),"string"==typeof t?null==(a=e[t])&&(a=e[k.camelCase(t)]):a=e,a}}function ue(e,t,n){if(g(e)){var i,a,o=e.nodeType,r=o?k.cache:e,l=o?e[k.expando]:k.expando;if(r[l]){if(t&&(i=n?r[l]:r[l].data)){a=(t=k.isArray(t)?t.concat(k.map(t,k.camelCase)):t in i||(t=k.camelCase(t))in i?[t]:t.split(" ")).length;for(;a--;)delete i[t[a]];if(n?!ce(i):!k.isEmptyObject(i))return}(n||(delete r[l].data,ce(r[l])))&&(o?k.cleanData([e],!0):m.deleteExpando||r!=r.window?delete r[l]:r[l]=void 0)}}}k.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){return!!(e=e.nodeType?k.cache[e[k.expando]]:e[k.expando])&&!ce(e)},data:function(e,t,n){return de(e,t,n)},removeData:function(e,t){return ue(e,t)},_data:function(e,t,n){return de(e,t,n,!0)},_removeData:function(e,t){return ue(e,t,!0)}}),k.fn.extend({data:function(e,t){var n,i,a,o=this[0],r=o&&o.attributes;if(void 0!==e)return"object"==typeof e?this.each(function(){k.data(this,e)}):1<arguments.length?this.each(function(){k.data(this,e,t)}):o?se(o,e,k.data(o,e)):void 0;if(this.length&&(a=k.data(o),1===o.nodeType)&&!k._data(o,"parsedAttrs")){for(n=r.length;n--;)r[n]&&0===(i=r[n].name).indexOf("data-")&&se(o,i=k.camelCase(i.slice(5)),a[i]);k._data(o,"parsedAttrs",!0)}return a},removeData:function(e){return this.each(function(){k.removeData(this,e)})}}),k.extend({queue:function(e,t,n){var i;if(e)return i=k._data(e,t=(t||"fx")+"queue"),n&&(!i||k.isArray(n)?i=k._data(e,t,k.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=k.queue(e,t),i=n.length,a=n.shift(),o=k._queueHooks(e,t);"inprogress"===a&&(a=n.shift(),i--),a&&("fx"===t&&n.unshift("inprogress"),delete o.stop,a.call(e,function(){k.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return k._data(e,n)||k._data(e,n,{empty:k.Callbacks("once memory").add(function(){k._removeData(e,t+"queue"),k._removeData(e,n)})})}}),k.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?k.queue(this[0],t):void 0===n?this:this.each(function(){var e=k.queue(this,t,n);k._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&k.dequeue(this,t)})},dequeue:function(e){return this.each(function(){k.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--a||o.resolveWith(r,[r])}var i,a=1,o=k.Deferred(),r=this,l=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";l--;)(i=k._data(r[l],e+"queueHooks"))&&i.empty&&(a++,i.empty.add(n));return n(),o.promise(t)}}),m.shrinkWrapBlocks=function(){var e,t,n;return null!=a?a:(a=!1,(t=h.getElementsByTagName("body")[0])&&t.style?(e=h.createElement("div"),(n=h.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",t.appendChild(n).appendChild(e),void 0!==e.style.zoom&&(e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",e.appendChild(h.createElement("div")).style.width="5px",a=3!==e.offsetWidth),t.removeChild(n),a):void 0)};function fe(e,t){return"none"===k.css(e=t||e,"display")||!k.contains(e.ownerDocument,e)}var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,pe=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),l=["Top","Right","Bottom","Left"];function he(e,t,n,i){var a,o=1,r=20,l=i?function(){return i.cur()}:function(){return k.css(e,t,"")},s=l(),c=n&&n[3]||(k.cssNumber[t]?"":"px"),d=(k.cssNumber[t]||"px"!==c&&+s)&&pe.exec(k.css(e,t));if(d&&d[3]!==c)for(c=c||d[3],n=n||[],d=+s||1;d/=o=o||".5",k.style(e,t,d+c),o!==(o=l()/s)&&1!==o&&--r;);return n&&(d=+d||+s||0,a=n[1]?d+(n[1]+1)*n[2]:+n[2],i)&&(i.unit=c,i.start=d,i.end=a),a}function f(e,t,n,i,a,o,r){var l=0,s=e.length,c=null==n;if("object"===k.type(n))for(l in a=!0,n)f(e,t,l,n[l],!0,o,r);else if(void 0!==i&&(a=!0,k.isFunction(i)||(r=!0),t=c?r?(t.call(e,i),null):(c=t,function(e,t,n){return c.call(k(e),n)}):t))for(;l<s;l++)t(e[l],n,r?i:i.call(e[l],l,t(e[l],n)));return a?e:c?t.call(e):s?t(e[0],n):o}var ye=/^(?:checkbox|radio)$/i,me=/<([\w:-]+)/,ge=/^$|\/(?:java|ecma)script/i,ve=/^\s+/,xe="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";function be(e){var t=xe.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}S=h.createElement("div"),D=h.createDocumentFragment(),j=h.createElement("input"),S.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",m.leadingWhitespace=3===S.firstChild.nodeType,m.tbody=!S.getElementsByTagName("tbody").length,m.htmlSerialize=!!S.getElementsByTagName("link").length,m.html5Clone="<:nav></:nav>"!==h.createElement("nav").cloneNode(!0).outerHTML,j.type="checkbox",j.checked=!0,D.appendChild(j),m.appendChecked=j.checked,S.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!S.cloneNode(!0).lastChild.defaultValue,D.appendChild(S),(j=h.createElement("input")).setAttribute("type","radio"),j.setAttribute("checked","checked"),j.setAttribute("name","t"),S.appendChild(j),m.checkClone=S.cloneNode(!0).cloneNode(!0).lastChild.checked,m.noCloneEvent=!!S.addEventListener,S[k.expando]=1,m.attributes=!S.getAttribute(k.expando);var v={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:m.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};function x(e,t){var n,i,a=0,o=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[a]);a++)!t||k.nodeName(i,t)?o.push(i):k.merge(o,x(i,t));return void 0===t||t&&k.nodeName(e,t)?k.merge([e],o):o}function we(e,t){for(var n,i=0;null!=(n=e[i]);i++)k._data(n,"globalEval",!t||k._data(t[i],"globalEval"))}v.optgroup=v.option,v.tbody=v.tfoot=v.colgroup=v.caption=v.thead,v.th=v.td;var ke=/<|&#?\w+;/,Ce=/<tbody/i;function Te(e){ye.test(e.type)&&(e.defaultChecked=e.checked)}function Ee(e,t,n,i,a){for(var o,r,l,s,c,d,u,f=e.length,p=be(t),h=[],y=0;y<f;y++)if((r=e[y])||0===r)if("object"===k.type(r))k.merge(h,r.nodeType?[r]:r);else if(ke.test(r)){for(s=s||p.appendChild(t.createElement("div")),c=(me.exec(r)||["",""])[1].toLowerCase(),u=v[c]||v._default,s.innerHTML=u[1]+k.htmlPrefilter(r)+u[2],o=u[0];o--;)s=s.lastChild;if(!m.leadingWhitespace&&ve.test(r)&&h.push(t.createTextNode(ve.exec(r)[0])),!m.tbody)for(o=(r="table"!==c||Ce.test(r)?"<table>"!==u[1]||Ce.test(r)?0:s:s.firstChild)&&r.childNodes.length;o--;)k.nodeName(d=r.childNodes[o],"tbody")&&!d.childNodes.length&&r.removeChild(d);for(k.merge(h,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=p.lastChild}else h.push(t.createTextNode(r));for(s&&p.removeChild(s),m.appendChecked||k.grep(x(h,"input"),Te),y=0;r=h[y++];)if(i&&-1<k.inArray(r,i))a&&a.push(r);else if(l=k.contains(r.ownerDocument,r),s=x(p.appendChild(r),"script"),l&&we(s),n)for(o=0;r=s[o++];)ge.test(r.type||"")&&n.push(r);return s=null,p}var De,Se,Ne=h.createElement("div");for(De in{submit:!0,change:!0,focusin:!0})(m[De]=(Se="on"+De)in w)||(Ne.setAttribute(Se,"t"),m[De]=!1===Ne.attributes[Se].expando);var Le=/^(?:input|select|textarea)$/i,Ae=/^key/,Ie=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Me=/^(?:focusinfocus|focusoutblur)$/,je=/^([^.]*)(?:\.(.+)|)/;function _e(){return!0}function s(){return!1}function He(){try{return h.activeElement}catch(e){}}function Fe(e,t,n,i,a,o){var r,l;if("object"==typeof t){for(l in"string"!=typeof n&&(i=i||n,n=void 0),t)Fe(e,l,n,i,t[l],o);return e}if(null==i&&null==a?(a=n,i=n=void 0):null==a&&("string"==typeof n?(a=i,i=void 0):(a=i,i=n,n=void 0)),!1===a)a=s;else if(!a)return e;return 1===o&&(r=a,(a=function(e){return k().off(e),r.apply(this,arguments)}).guid=r.guid||(r.guid=k.guid++)),e.each(function(){k.event.add(this,t,a,i,n)})}k.event={global:{},add:function(e,t,n,i,a){var o,r,l,s,c,d,u,f,p,h=k._data(e);if(h)for(n.handler&&(n=(l=n).handler,a=l.selector),n.guid||(n.guid=k.guid++),o=(o=h.events)||(h.events={}),(c=h.handle)||((c=h.handle=function(e){return void 0===k||e&&k.event.triggered===e.type?void 0:k.event.dispatch.apply(c.elem,arguments)}).elem=e),r=(t=(t||"").match(C)||[""]).length;r--;)u=p=(f=je.exec(t[r])||[])[1],f=(f[2]||"").split(".").sort(),u&&(s=k.event.special[u]||{},u=(a?s.delegateType:s.bindType)||u,s=k.event.special[u]||{},p=k.extend({type:u,origType:p,data:i,handler:n,guid:n.guid,selector:a,needsContext:a&&k.expr.match.needsContext.test(a),namespace:f.join(".")},l),(d=o[u])||((d=o[u]=[]).delegateCount=0,s.setup&&!1!==s.setup.call(e,i,f,c))||(e.addEventListener?e.addEventListener(u,c,!1):e.attachEvent&&e.attachEvent("on"+u,c)),s.add&&(s.add.call(e,p),p.handler.guid||(p.handler.guid=n.guid)),a?d.splice(d.delegateCount++,0,p):d.push(p),k.event.global[u]=!0)},remove:function(e,t,n,i,a){var o,r,l,s,c,d,u,f,p,h,y,m=k.hasData(e)&&k._data(e);if(m&&(d=m.events)){for(c=(t=(t||"").match(C)||[""]).length;c--;)if(p=y=(l=je.exec(t[c])||[])[1],h=(l[2]||"").split(".").sort(),p){for(u=k.event.special[p]||{},f=d[p=(i?u.delegateType:u.bindType)||p]||[],l=l[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)r=f[o],!a&&y!==r.origType||n&&n.guid!==r.guid||l&&!l.test(r.namespace)||i&&i!==r.selector&&("**"!==i||!r.selector)||(f.splice(o,1),r.selector&&f.delegateCount--,u.remove&&u.remove.call(e,r));s&&!f.length&&(u.teardown&&!1!==u.teardown.call(e,h,m.handle)||k.removeEvent(e,p,m.handle),delete d[p])}else for(p in d)k.event.remove(e,p+t[c],n,i,!0);k.isEmptyObject(d)&&(delete m.handle,k._removeData(e,"events"))}},trigger:function(e,t,n,i){var a,o,r,l,s,c,d=[n||h],u=y.call(e,"type")?e.type:e,f=y.call(e,"namespace")?e.namespace.split("."):[],p=s=n=n||h;if(3!==n.nodeType&&8!==n.nodeType&&!Me.test(u+k.event.triggered)&&(-1<u.indexOf(".")&&(u=(f=u.split(".")).shift(),f.sort()),o=u.indexOf(":")<0&&"on"+u,(e=e[k.expando]?e:new k.Event(u,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=f.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:k.makeArray(t,[e]),l=k.event.special[u]||{},i||!l.trigger||!1!==l.trigger.apply(n,t))){if(!i&&!l.noBubble&&!k.isWindow(n)){for(r=l.delegateType||u,Me.test(r+u)||(p=p.parentNode);p;p=p.parentNode)d.push(p),s=p;s===(n.ownerDocument||h)&&d.push(s.defaultView||s.parentWindow||w)}for(c=0;(p=d[c++])&&!e.isPropagationStopped();)e.type=1<c?r:l.bindType||u,(a=(k._data(p,"events")||{})[e.type]&&k._data(p,"handle"))&&a.apply(p,t),(a=o&&p[o])&&a.apply&&g(p)&&(e.result=a.apply(p,t),!1===e.result)&&e.preventDefault();if(e.type=u,!i&&!e.isDefaultPrevented()&&(!l._default||!1===l._default.apply(d.pop(),t))&&g(n)&&o&&n[u]&&!k.isWindow(n)){(s=n[o])&&(n[o]=null),k.event.triggered=u;try{n[u]()}catch(e){}k.event.triggered=void 0,s&&(n[o]=s)}return e.result}},dispatch:function(e){e=k.event.fix(e);var t,n,i,a,o,r=d.call(arguments),l=(k._data(this,"events")||{})[e.type]||[],s=k.event.special[e.type]||{};if((r[0]=e).delegateTarget=this,!s.preDispatch||!1!==s.preDispatch.call(this,e)){for(o=k.event.handlers.call(this,e,l),t=0;(i=o[t++])&&!e.isPropagationStopped();)for(e.currentTarget=i.elem,n=0;(a=i.handlers[n++])&&!e.isImmediatePropagationStopped();)e.rnamespace&&!e.rnamespace.test(a.namespace)||(e.handleObj=a,e.data=a.data,void 0!==(a=((k.event.special[a.origType]||{}).handle||a.handler).apply(i.elem,r))&&!1===(e.result=a)&&(e.preventDefault(),e.stopPropagation()));return s.postDispatch&&s.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,a,o,r=[],l=t.delegateCount,s=e.target;if(l&&s.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;s!=this;s=s.parentNode||this)if(1===s.nodeType&&(!0!==s.disabled||"click"!==e.type)){for(i=[],n=0;n<l;n++)void 0===i[a=(o=t[n]).selector+" "]&&(i[a]=o.needsContext?-1<k(a,this).index(s):k.find(a,this,null,[s]).length),i[a]&&i.push(o);i.length&&r.push({elem:s,handlers:i})}return l<t.length&&r.push({elem:this,handlers:t.slice(l)}),r},fix:function(e){if(e[k.expando])return e;var t,n,i,a=e.type,o=e,r=this.fixHooks[a];for(r||(this.fixHooks[a]=r=Ie.test(a)?this.mouseHooks:Ae.test(a)?this.keyHooks:{}),i=r.props?this.props.concat(r.props):this.props,e=new k.Event(o),t=i.length;t--;)e[n=i[t]]=o[n];return e.target||(e.target=o.srcElement||h),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,r.filter?r.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,a=t.button,o=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=(n=e.target.ownerDocument||h).documentElement,n=n.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&o&&(e.relatedTarget=o===e.target?t.toElement:o),e.which||void 0===a||(e.which=1&a?1:2&a?3:4&a?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==He()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===He()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(k.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return k.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n){e=k.extend(new k.Event,n,{type:e,isSimulated:!0}),k.event.trigger(e,null,t),e.isDefaultPrevented()&&n.preventDefault()}},k.removeEvent=h.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)}:function(e,t,n){t="on"+t,e.detachEvent&&(void 0===e[t]&&(e[t]=null),e.detachEvent(t,n))},k.Event=function(e,t){if(!(this instanceof k.Event))return new k.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?_e:s):this.type=e,t&&k.extend(this,t),this.timeStamp=e&&e.timeStamp||k.now(),this[k.expando]=!0},k.Event.prototype={constructor:k.Event,isDefaultPrevented:s,isPropagationStopped:s,isImmediatePropagationStopped:s,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=_e,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=_e,e&&!this.isSimulated&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=_e,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},k.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,a){k.event.special[e]={delegateType:a,bindType:a,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||k.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=a),t}}}),m.submit||(k.event.special.submit={setup:function(){if(k.nodeName(this,"form"))return!1;k.event.add(this,"click._submit keypress._submit",function(e){e=e.target,(e=k.nodeName(e,"input")||k.nodeName(e,"button")?k.prop(e,"form"):void 0)&&!k._data(e,"submit")&&(k.event.add(e,"submit._submit",function(e){e._submitBubble=!0}),k._data(e,"submit",!0))})},postDispatch:function(e){e._submitBubble&&(delete e._submitBubble,this.parentNode)&&!e.isTrigger&&k.event.simulate("submit",this.parentNode,e)},teardown:function(){if(k.nodeName(this,"form"))return!1;k.event.remove(this,"._submit")}}),m.change||(k.event.special.change={setup:function(){if(Le.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(k.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._justChanged=!0)}),k.event.add(this,"click._change",function(e){this._justChanged&&!e.isTrigger&&(this._justChanged=!1),k.event.simulate("change",this,e)})),!1;k.event.add(this,"beforeactivate._change",function(e){e=e.target,Le.test(e.nodeName)&&!k._data(e,"change")&&(k.event.add(e,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||k.event.simulate("change",this.parentNode,e)}),k._data(e,"change",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return k.event.remove(this,"._change"),!Le.test(this.nodeName)}}),m.focusin||k.each({focus:"focusin",blur:"focusout"},function(n,i){function a(e){k.event.simulate(i,e.target,k.event.fix(e))}k.event.special[i]={setup:function(){var e=this.ownerDocument||this,t=k._data(e,i);t||e.addEventListener(n,a,!0),k._data(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this,t=k._data(e,i)-1;t?k._data(e,i,t):(e.removeEventListener(n,a,!0),k._removeData(e,i))}}}),k.fn.extend({on:function(e,t,n,i){return Fe(this,e,t,n,i)},one:function(e,t,n,i){return Fe(this,e,t,n,i,1)},off:function(e,t,n){var i,a;if(e&&e.preventDefault&&e.handleObj)i=e.handleObj,k(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=s),this.each(function(){k.event.remove(this,e,n,t)});for(a in e)this.off(a,t,e[a])}return this},trigger:function(e,t){return this.each(function(){k.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return k.event.trigger(e,t,n,!0)}});var Oe=/ jQuery\d+="(?:null|\d+)"/g,Re=new RegExp("<(?:"+xe+")[\\s/>]","i"),Pe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,qe=/<script|<style|<link/i,Be=/checked\s*(?:[^=]|=\s*.checked.)/i,ze=/^true\/(.*)/,We=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Ye=be(h).appendChild(h.createElement("div"));function $e(e,t){return k.nodeName(e,"table")&&k.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function Xe(e){return e.type=(null!==k.find.attr(e,"type"))+"/"+e.type,e}function Ve(e){var t=ze.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Ue(e,t){if(1===t.nodeType&&k.hasData(e)){var n,i,a,e=k._data(e),o=k._data(t,e),r=e.events;if(r)for(n in delete o.handle,o.events={},r)for(i=0,a=r[n].length;i<a;i++)k.event.add(t,n,r[n][i]);o.data&&(o.data=k.extend({},o.data))}}function b(n,i,a,o){i=R.apply([],i);var e,t,r,l,s,c,d=0,u=n.length,f=u-1,p=i[0],h=k.isFunction(p);if(h||1<u&&"string"==typeof p&&!m.checkClone&&Be.test(p))return n.each(function(e){var t=n.eq(e);h&&(i[0]=p.call(this,e,t.html())),b(t,i,a,o)});if(u&&(e=(c=Ee(i,n[0].ownerDocument,!1,n,o)).firstChild,1===c.childNodes.length&&(c=e),e||o)){for(r=(l=k.map(x(c,"script"),Xe)).length;d<u;d++)t=c,d!==f&&(t=k.clone(t,!0,!0),r)&&k.merge(l,x(t,"script")),a.call(n[d],t,d);if(r)for(s=l[l.length-1].ownerDocument,k.map(l,Ve),d=0;d<r;d++)t=l[d],ge.test(t.type||"")&&!k._data(t,"globalEval")&&k.contains(s,t)&&(t.src?k._evalUrl&&k._evalUrl(t.src):k.globalEval((t.text||t.textContent||t.innerHTML||"").replace(We,"")));c=null}return n}function Ge(e,t,n){for(var i,a=t?k.filter(t,e):e,o=0;null!=(i=a[o]);o++)n||1!==i.nodeType||k.cleanData(x(i)),i.parentNode&&(n&&k.contains(i.ownerDocument,i)&&we(x(i,"script")),i.parentNode.removeChild(i));return e}k.extend({htmlPrefilter:function(e){return e.replace(Pe,"<$1></$2>")},clone:function(e,t,n){var i,a,o,r,l,s=k.contains(e.ownerDocument,e);if(m.html5Clone||k.isXMLDoc(e)||!Re.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Ye.innerHTML=e.outerHTML,Ye.removeChild(o=Ye.firstChild)),!(m.noCloneEvent&&m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||k.isXMLDoc(e)))for(i=x(o),l=x(e),r=0;null!=(a=l[r]);++r)if(i[r]){d=p=void 0;var c,d,u,f=a,p=i[r];if(1===p.nodeType){if(c=p.nodeName.toLowerCase(),!m.noCloneEvent&&p[k.expando]){for(d in(u=k._data(p)).events)k.removeEvent(p,d,u.handle);p.removeAttribute(k.expando)}"script"===c&&p.text!==f.text?(Xe(p).text=f.text,Ve(p)):"object"===c?(p.parentNode&&(p.outerHTML=f.outerHTML),m.html5Clone&&f.innerHTML&&!k.trim(p.innerHTML)&&(p.innerHTML=f.innerHTML)):"input"===c&&ye.test(f.type)?(p.defaultChecked=p.checked=f.checked,p.value!==f.value&&(p.value=f.value)):"option"===c?p.defaultSelected=p.selected=f.defaultSelected:"input"!==c&&"textarea"!==c||(p.defaultValue=f.defaultValue)}}if(t)if(n)for(l=l||x(e),i=i||x(o),r=0;null!=(a=l[r]);r++)Ue(a,i[r]);else Ue(e,o);return 0<(i=x(o,"script")).length&&we(i,!s&&x(e,"script")),i=l=a=null,o},cleanData:function(e,t){for(var n,i,a,o,r=0,l=k.expando,s=k.cache,c=m.attributes,d=k.event.special;null!=(n=e[r]);r++)if((t||g(n))&&(o=(a=n[l])&&s[a])){if(o.events)for(i in o.events)d[i]?k.event.remove(n,i):k.removeEvent(n,i,o.handle);s[a]&&(delete s[a],c||void 0===n.removeAttribute?n[l]=void 0:n.removeAttribute(l),u.push(a))}}}),k.fn.extend({domManip:b,detach:function(e){return Ge(this,e,!0)},remove:function(e){return Ge(this,e)},text:function(e){return f(this,function(e){return void 0===e?k.text(this):this.empty().append((this[0]&&this[0].ownerDocument||h).createTextNode(e))},null,e,arguments.length)},append:function(){return b(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||$e(this,e).appendChild(e)})},prepend:function(){return b(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=$e(this,e)).insertBefore(e,t.firstChild)})},before:function(){return b(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return b(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&k.cleanData(x(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&k.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return k.clone(this,e,t)})},html:function(e){return f(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(Oe,""):void 0;if("string"==typeof e&&!qe.test(e)&&(m.htmlSerialize||!Re.test(e))&&(m.leadingWhitespace||!ve.test(e))&&!v[(me.exec(e)||["",""])[1].toLowerCase()]){e=k.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(k.cleanData(x(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return b(this,arguments,function(e){var t=this.parentNode;k.inArray(this,n)<0&&(k.cleanData(x(this)),t)&&t.replaceChild(e,this)},n)}}),k.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,r){k.fn[e]=function(e){for(var t,n=0,i=[],a=k(e),o=a.length-1;n<=o;n++)t=n===o?this:this.clone(!0),k(a[n])[r](t),P.apply(i,t.get());return this.pushStack(i)}});var Ke,Je={HTML:"block",BODY:"block"};function Ze(e,t){return e=k(t.createElement(e)).appendTo(t.body),t=k.css(e[0],"display"),e.detach(),t}function Qe(e){var t=h,n=Je[e];return n||("none"!==(n=Ze(e,t))&&n||((t=((Ke=(Ke||k("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement))[0].contentWindow||Ke[0].contentDocument).document).write(),t.close(),n=Ze(e,t),Ke.detach()),Je[e]=n),n}function et(e,t,n,i){var a,o={};for(a in t)o[a]=e.style[a],e.style[a]=t[a];for(a in i=n.apply(e,i||[]),t)e.style[a]=o[a];return i}var n,tt,nt,it,at,ot,rt,o,lt=/^margin/,st=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),ct=h.documentElement;function r(){var e,t=h.documentElement;t.appendChild(rt),o.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",n=nt=ot=!1,tt=at=!0,w.getComputedStyle&&(e=w.getComputedStyle(o),n="1%"!==(e||{}).top,ot="2px"===(e||{}).marginLeft,nt="4px"===(e||{width:"4px"}).width,o.style.marginRight="50%",tt="4px"===(e||{marginRight:"4px"}).marginRight,(e=o.appendChild(h.createElement("div"))).style.cssText=o.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",e.style.marginRight=e.style.width="0",o.style.width="1px",at=!parseFloat((w.getComputedStyle(e)||{}).marginRight),o.removeChild(e)),o.style.display="none",(it=0===o.getClientRects().length)&&(o.style.display="",o.innerHTML="<table><tr><td></td><td>t</td></tr></table>",o.childNodes[0].style.borderCollapse="separate",(e=o.getElementsByTagName("td"))[0].style.cssText="margin:0;border:0;padding:0;display:none",it=0===e[0].offsetHeight)&&(e[0].style.display="",e[1].style.display="none",it=0===e[0].offsetHeight),t.removeChild(rt)}rt=h.createElement("div"),(o=h.createElement("div")).style&&(o.style.cssText="float:left;opacity:.5",m.opacity="0.5"===o.style.opacity,m.cssFloat=!!o.style.cssFloat,o.style.backgroundClip="content-box",o.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===o.style.backgroundClip,(rt=h.createElement("div")).style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",o.innerHTML="",rt.appendChild(o),m.boxSizing=""===o.style.boxSizing||""===o.style.MozBoxSizing||""===o.style.WebkitBoxSizing,k.extend(m,{reliableHiddenOffsets:function(){return null==n&&r(),it},boxSizingReliable:function(){return null==n&&r(),nt},pixelMarginRight:function(){return null==n&&r(),tt},pixelPosition:function(){return null==n&&r(),n},reliableMarginRight:function(){return null==n&&r(),at},reliableMarginLeft:function(){return null==n&&r(),ot}}));var c,p,dt=/^(top|right|bottom|left)$/;function ut(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}w.getComputedStyle?(c=function(e){var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:w).getComputedStyle(e)},p=function(e,t,n){var i,a,o=e.style;return""!==(a=(n=n||c(e))?n.getPropertyValue(t)||n[t]:void 0)&&void 0!==a||k.contains(e.ownerDocument,e)||(a=k.style(e,t)),n&&!m.pixelMarginRight()&&st.test(a)&&lt.test(t)&&(e=o.width,t=o.minWidth,i=o.maxWidth,o.minWidth=o.maxWidth=o.width=a,a=n.width,o.width=e,o.minWidth=t,o.maxWidth=i),void 0===a?a:a+""}):ct.currentStyle&&(c=function(e){return e.currentStyle},p=function(e,t,n){var i,a,o,r=e.style;return null==(n=(n=n||c(e))?n[t]:void 0)&&r&&r[t]&&(n=r[t]),st.test(n)&&!dt.test(t)&&(i=r.left,(o=(a=e.runtimeStyle)&&a.left)&&(a.left=e.currentStyle.left),r.left="fontSize"===t?"1em":n,n=r.pixelLeft+"px",r.left=i,o)&&(a.left=o),void 0===n?n:n+""||"auto"});var ft=/alpha\([^)]*\)/i,pt=/opacity\s*=\s*([^)]*)/i,ht=/^(none|table(?!-c[ea]).+)/,yt=new RegExp("^("+e+")(.*)$","i"),mt={position:"absolute",visibility:"hidden",display:"block"},gt={letterSpacing:"0",fontWeight:"400"},vt=["Webkit","O","Moz","ms"],xt=h.createElement("div").style;function bt(e){if(e in xt)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=vt.length;n--;)if((e=vt[n]+t)in xt)return e}function wt(e,t){for(var n,i,a,o=[],r=0,l=e.length;r<l;r++)(i=e[r]).style&&(o[r]=k._data(i,"olddisplay"),n=i.style.display,t?(o[r]||"none"!==n||(i.style.display=""),""===i.style.display&&fe(i)&&(o[r]=k._data(i,"olddisplay",Qe(i.nodeName)))):(a=fe(i),(n&&"none"!==n||!a)&&k._data(i,"olddisplay",a?n:k.css(i,"display"))));for(r=0;r<l;r++)!(i=e[r]).style||t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[r]||"":"none");return e}function kt(e,t,n){var i=yt.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function Ct(e,t,n,i,a){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,r=0;o<4;o+=2)"margin"===n&&(r+=k.css(e,n+l[o],!0,a)),i?("content"===n&&(r-=k.css(e,"padding"+l[o],!0,a)),"margin"!==n&&(r-=k.css(e,"border"+l[o]+"Width",!0,a))):(r+=k.css(e,"padding"+l[o],!0,a),"padding"!==n&&(r+=k.css(e,"border"+l[o]+"Width",!0,a)));return r}function Tt(e,t,n){var i=!0,a="width"===t?e.offsetWidth:e.offsetHeight,o=c(e),r=m.boxSizing&&"border-box"===k.css(e,"boxSizing",!1,o);if(a<=0||null==a){if(((a=p(e,t,o))<0||null==a)&&(a=e.style[t]),st.test(a))return a;i=r&&(m.boxSizingReliable()||a===e.style[t]),a=parseFloat(a)||0}return a+Ct(e,t,n||(r?"border":"content"),i,o)+"px"}function T(e,t,n,i,a){return new T.prototype.init(e,t,n,i,a)}k.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=p(e,"opacity"))?"1":t}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:m.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var a,o,r,l=k.camelCase(t),s=e.style;if(t=k.cssProps[l]||(k.cssProps[l]=bt(l)||l),r=k.cssHooks[t]||k.cssHooks[l],void 0===n)return r&&"get"in r&&void 0!==(a=r.get(e,!1,i))?a:s[t];if("string"==(o=typeof n)&&(a=pe.exec(n))&&a[1]&&(n=he(e,t,a),o="number"),null!=n&&n==n&&("number"===o&&(n+=a&&a[3]||(k.cssNumber[l]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(s[t]="inherit"),!(r&&"set"in r&&void 0===(n=r.set(e,n,i)))))try{s[t]=n}catch(e){}}},css:function(e,t,n,i){var a,o=k.camelCase(t);return t=k.cssProps[o]||(k.cssProps[o]=bt(o)||o),"normal"===(a=void 0===(a=(o=k.cssHooks[t]||k.cssHooks[o])&&"get"in o?o.get(e,!0,n):a)?p(e,t,i):a)&&t in gt&&(a=gt[t]),(""===n||n)&&(o=parseFloat(a),!0===n||isFinite(o))?o||0:a}}),k.each(["height","width"],function(e,a){k.cssHooks[a]={get:function(e,t,n){if(t)return ht.test(k.css(e,"display"))&&0===e.offsetWidth?et(e,mt,function(){return Tt(e,a,n)}):Tt(e,a,n)},set:function(e,t,n){var i=n&&c(e);return kt(0,t,n?Ct(e,a,n,m.boxSizing&&"border-box"===k.css(e,"boxSizing",!1,i),i):0)}}}),m.opacity||(k.cssHooks.opacity={get:function(e,t){return pt.test((t&&e.currentStyle?e.currentStyle:e.style).filter||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,e=e.currentStyle,i=k.isNumeric(t)?"alpha(opacity="+100*t+")":"",a=e&&e.filter||n.filter||"";((n.zoom=1)<=t||""===t)&&""===k.trim(a.replace(ft,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||e&&!e.filter)||(n.filter=ft.test(a)?a.replace(ft,i):a+" "+i)}}),k.cssHooks.marginRight=ut(m.reliableMarginRight,function(e,t){if(t)return et(e,{display:"inline-block"},p,[e,"marginRight"])}),k.cssHooks.marginLeft=ut(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(p(e,"marginLeft"))||(k.contains(e.ownerDocument,e)?e.getBoundingClientRect().left-et(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}):0))+"px"}),k.each({margin:"",padding:"",border:"Width"},function(a,o){k.cssHooks[a+o]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[a+l[t]+o]=i[t]||i[t-2]||i[0];return n}},lt.test(a)||(k.cssHooks[a+o].set=kt)}),k.fn.extend({css:function(e,t){return f(this,function(e,t,n){var i,a,o={},r=0;if(k.isArray(t)){for(i=c(e),a=t.length;r<a;r++)o[t[r]]=k.css(e,t[r],!1,i);return o}return void 0!==n?k.style(e,t,n):k.css(e,t)},e,t,1<arguments.length)},show:function(){return wt(this,!0)},hide:function(){return wt(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){fe(this)?k(this).show():k(this).hide()})}}),((k.Tween=T).prototype={constructor:T,init:function(e,t,n,i,a,o){this.elem=e,this.prop=n,this.easing=a||k.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(k.cssNumber[n]?"":"px")},cur:function(){var e=T.propHooks[this.prop];return(e&&e.get?e:T.propHooks._default).get(this)},run:function(e){var t,n=T.propHooks[this.prop];return this.options.duration?this.pos=t=k.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:T.propHooks._default).set(this),this}}).init.prototype=T.prototype,(T.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=k.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){k.fx.step[e.prop]?k.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[k.cssProps[e.prop]]&&!k.cssHooks[e.prop]?e.elem[e.prop]=e.now:k.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=T.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},k.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},k.fx=T.prototype.init,k.fx.step={};var E,Et,D,S,Dt=/^(?:toggle|show|hide)$/,St=/queueHooks$/;function Nt(){return w.setTimeout(function(){E=void 0}),E=k.now()}function Lt(e,t){var n,i={height:e},a=0;for(t=t?1:0;a<4;a+=2-t)i["margin"+(n=l[a])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function At(e,t,n){for(var i,a=(N.tweeners[t]||[]).concat(N.tweeners["*"]),o=0,r=a.length;o<r;o++)if(i=a[o].call(n,t,e))return i}function N(a,e,t){var n,o,i,r,l,s,c,d=0,u=N.prefilters.length,f=k.Deferred().always(function(){delete p.elem}),p=function(){if(o)return!1;for(var e=E||Nt(),t=1-((e=Math.max(0,h.startTime+h.duration-e))/h.duration||0),n=0,i=h.tweens.length;n<i;n++)h.tweens[n].run(t);return f.notifyWith(a,[h,t,e]),t<1&&i?e:(f.resolveWith(a,[h]),!1)},h=f.promise({elem:a,props:k.extend({},e),opts:k.extend(!0,{specialEasing:{},easing:k.easing._default},t),originalProperties:e,originalOptions:t,startTime:E||Nt(),duration:t.duration,tweens:[],createTween:function(e,t){return t=k.Tween(a,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing),h.tweens.push(t),t},stop:function(e){var t=0,n=e?h.tweens.length:0;if(!o){for(o=!0;t<n;t++)h.tweens[t].run(1);e?(f.notifyWith(a,[h,1,0]),f.resolveWith(a,[h,e])):f.rejectWith(a,[h,e])}return this}}),y=h.props,m=y,g=h.opts.specialEasing;for(i in m)if(l=g[r=k.camelCase(i)],s=m[i],k.isArray(s)&&(l=s[1],s=m[i]=s[0]),i!==r&&(m[r]=s,delete m[i]),(c=k.cssHooks[r])&&"expand"in c)for(i in s=c.expand(s),delete m[r],s)i in m||(m[i]=s[i],g[i]=l);else g[r]=l;for(;d<u;d++)if(n=N.prefilters[d].call(h,a,y,h.opts))return k.isFunction(n.stop)&&(k._queueHooks(h.elem,h.opts.queue).stop=k.proxy(n.stop,n)),n;return k.map(y,At,h),k.isFunction(h.opts.start)&&h.opts.start.call(a,h),k.fx.timer(k.extend(p,{elem:a,anim:h,queue:h.opts.queue})),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always)}k.Animation=k.extend(N,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return he(n.elem,e,pe.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,a=(e=k.isFunction(e)?(t=e,["*"]):e.match(C)).length;i<a;i++)n=e[i],N.tweeners[n]=N.tweeners[n]||[],N.tweeners[n].unshift(t)},prefilters:[function(t,e,n){var i,a,o,r,l,s,c,d=this,u={},f=t.style,p=t.nodeType&&fe(t),h=k._data(t,"fxshow");for(i in n.queue||(null==(l=k._queueHooks(t,"fx")).unqueued&&(l.unqueued=0,s=l.empty.fire,l.empty.fire=function(){l.unqueued||s()}),l.unqueued++,d.always(function(){d.always(function(){l.unqueued--,k.queue(t,"fx").length||l.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],"inline"===("none"===(c=k.css(t,"display"))?k._data(t,"olddisplay")||Qe(t.nodeName):c))&&"none"===k.css(t,"float")&&(m.inlineBlockNeedsLayout&&"inline"!==Qe(t.nodeName)?f.zoom=1:f.display="inline-block"),n.overflow&&(f.overflow="hidden",m.shrinkWrapBlocks()||d.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),e)if(a=e[i],Dt.exec(a)){if(delete e[i],o=o||"toggle"===a,a===(p?"hide":"show")){if("show"!==a||!h||void 0===h[i])continue;p=!0}u[i]=h&&h[i]||k.style(t,i)}else c=void 0;if(k.isEmptyObject(u))"inline"===("none"===c?Qe(t.nodeName):c)&&(f.display=c);else for(i in h?"hidden"in h&&(p=h.hidden):h=k._data(t,"fxshow",{}),o&&(h.hidden=!p),p?k(t).show():d.done(function(){k(t).hide()}),d.done(function(){for(var e in k._removeData(t,"fxshow"),u)k.style(t,e,u[e])}),u)r=At(p?h[i]:0,i,d),i in h||(h[i]=r.start,p&&(r.end=r.start,r.start="width"===i||"height"===i?1:0))}],prefilter:function(e,t){t?N.prefilters.unshift(e):N.prefilters.push(e)}}),k.speed=function(e,t,n){var i=e&&"object"==typeof e?k.extend({},e):{complete:n||!n&&t||k.isFunction(e)&&e,duration:e,easing:n&&t||t&&!k.isFunction(t)&&t};return i.duration=k.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in k.fx.speeds?k.fx.speeds[i.duration]:k.fx.speeds._default,null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){k.isFunction(i.old)&&i.old.call(this),i.queue&&k.dequeue(this,i.queue)},i},k.fn.extend({fadeTo:function(e,t,n,i){return this.filter(fe).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){var a=k.isEmptyObject(t),o=k.speed(e,n,i);return(e=function(){var e=N(this,k.extend({},t),o);(a||k._data(this,"finish"))&&e.stop(!0)}).finish=e,a||!1===o.queue?this.each(e):this.queue(o.queue,e)},stop:function(a,e,o){function r(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof a&&(o=e,e=a,a=void 0),e&&!1!==a&&this.queue(a||"fx",[]),this.each(function(){var e=!0,t=null!=a&&a+"queueHooks",n=k.timers,i=k._data(this);if(t)i[t]&&i[t].stop&&r(i[t]);else for(t in i)i[t]&&i[t].stop&&St.test(t)&&r(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=a&&n[t].queue!==a||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||k.dequeue(this,a)})},finish:function(r){return!1!==r&&(r=r||"fx"),this.each(function(){var e,t=k._data(this),n=t[r+"queue"],i=t[r+"queueHooks"],a=k.timers,o=n?n.length:0;for(t.finish=!0,k.queue(this,r,[]),i&&i.stop&&i.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===r&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),k.each(["toggle","show","hide"],function(e,i){var a=k.fn[i];k.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?a.apply(this,arguments):this.animate(Lt(i,!0),e,t,n)}}),k.each({slideDown:Lt("show"),slideUp:Lt("hide"),slideToggle:Lt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){k.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),k.timers=[],k.fx.tick=function(){var e,t=k.timers,n=0;for(E=k.now();n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||k.fx.stop(),E=void 0},k.fx.timer=function(e){k.timers.push(e),e()?k.fx.start():k.timers.pop()},k.fx.interval=13,k.fx.start=function(){Et=Et||w.setInterval(k.fx.tick,k.fx.interval)},k.fx.stop=function(){w.clearInterval(Et),Et=null},k.fx.speeds={slow:600,fast:200,_default:400},k.fn.delay=function(i,e){return i=k.fx&&k.fx.speeds[i]||i,this.queue(e=e||"fx",function(e,t){var n=w.setTimeout(e,i);t.stop=function(){w.clearTimeout(n)}})},D=h.createElement("input"),j=h.createElement("div"),e=(S=h.createElement("select")).appendChild(h.createElement("option")),(j=h.createElement("div")).setAttribute("className","t"),j.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",H=j.getElementsByTagName("a")[0],D.setAttribute("type","checkbox"),j.appendChild(D),(H=j.getElementsByTagName("a")[0]).style.cssText="top:1px",m.getSetAttribute="t"!==j.className,m.style=/top/.test(H.getAttribute("style")),m.hrefNormalized="/a"===H.getAttribute("href"),m.checkOn=!!D.value,m.optSelected=e.selected,m.enctype=!!h.createElement("form").enctype,S.disabled=!0,m.optDisabled=!e.disabled,(D=h.createElement("input")).setAttribute("value",""),m.input=""===D.getAttribute("value"),D.value="t",D.setAttribute("type","radio"),m.radioValue="t"===D.value;var It=/\r/g,Mt=/[\x20\t\r\n\f]+/g;k.fn.extend({val:function(t){var n,e,i,a=this[0];return arguments.length?(i=k.isFunction(t),this.each(function(e){1!==this.nodeType||(null==(e=i?t.call(this,e,k(this).val()):t)?e="":"number"==typeof e?e+="":k.isArray(e)&&(e=k.map(e,function(e){return null==e?"":e+""})),(n=k.valHooks[this.type]||k.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value"))||(this.value=e)})):a?(n=k.valHooks[a.type]||k.valHooks[a.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(a,"value"))?e:"string"==typeof(e=a.value)?e.replace(It,""):null==e?"":e:void 0}}),k.extend({valHooks:{option:{get:function(e){var t=k.find.attr(e,"value");return null!=t?t:k.trim(k.text(e)).replace(Mt," ")}},select:{get:function(e){for(var t,n=e.options,i=e.selectedIndex,a="select-one"===e.type||i<0,o=a?null:[],r=a?i+1:n.length,l=i<0?r:a?i:0;l<r;l++)if(((t=n[l]).selected||l===i)&&(m.optDisabled?!t.disabled:null===t.getAttribute("disabled"))&&(!t.parentNode.disabled||!k.nodeName(t.parentNode,"optgroup"))){if(t=k(t).val(),a)return t;o.push(t)}return o},set:function(e,t){for(var n,i,a=e.options,o=k.makeArray(t),r=a.length;r--;)if(i=a[r],-1<k.inArray(k.valHooks.option.get(i),o))try{i.selected=n=!0}catch(e){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),a}}}}),k.each(["radio","checkbox"],function(){k.valHooks[this]={set:function(e,t){if(k.isArray(t))return e.checked=-1<k.inArray(k(e).val(),t)}},m.checkOn||(k.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var L,jt,A=k.expr.attrHandle,_t=/^(?:checked|selected)$/i,I=m.getSetAttribute,Ht=m.input,Ft=(k.fn.extend({attr:function(e,t){return f(this,k.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){k.removeAttr(this,e)})}}),k.extend({attr:function(e,t,n){var i,a,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?k.prop(e,t,n):(1===o&&k.isXMLDoc(e)||(t=t.toLowerCase(),a=k.attrHooks[t]||(k.expr.match.bool.test(t)?jt:L)),void 0!==n?null===n?void k.removeAttr(e,t):a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:(e.setAttribute(t,n+""),n):a&&"get"in a&&null!==(i=a.get(e,t))||null!=(i=k.find.attr(e,t))?i:void 0)},attrHooks:{type:{set:function(e,t){var n;if(!m.radioValue&&"radio"===t&&k.nodeName(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,i,a=0,o=t&&t.match(C);if(o&&1===e.nodeType)for(;n=o[a++];)i=k.propFix[n]||n,k.expr.match.bool.test(n)?Ht&&I||!_t.test(n)?e[i]=!1:e[k.camelCase("default-"+n)]=e[i]=!1:k.attr(e,n,""),e.removeAttribute(I?n:i)}}),jt={set:function(e,t,n){return!1===t?k.removeAttr(e,n):Ht&&I||!_t.test(n)?e.setAttribute(!I&&k.propFix[n]||n,n):e[k.camelCase("default-"+n)]=e[n]=!0,n}},k.each(k.expr.match.bool.source.match(/\w+/g),function(e,t){var o=A[t]||k.find.attr;Ht&&I||!_t.test(t)?A[t]=function(e,t,n){var i,a;return n||(a=A[t],A[t]=i,i=null!=o(e,t,n)?t.toLowerCase():null,A[t]=a),i}:A[t]=function(e,t,n){if(!n)return e[k.camelCase("default-"+t)]?t.toLowerCase():null}}),Ht&&I||(k.attrHooks.value={set:function(e,t,n){if(!k.nodeName(e,"input"))return L&&L.set(e,t,n);e.defaultValue=t}}),I||(L={set:function(e,t,n){var i=e.getAttributeNode(n);if(i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n))return t}},A.id=A.name=A.coords=function(e,t,n){if(!n)return(n=e.getAttributeNode(t))&&""!==n.value?n.value:null},k.valHooks.button={get:function(e,t){if((t=e.getAttributeNode(t))&&t.specified)return t.value},set:L.set},k.attrHooks.contenteditable={set:function(e,t,n){L.set(e,""!==t&&t,n)}},k.each(["width","height"],function(e,n){k.attrHooks[n]={set:function(e,t){if(""===t)return e.setAttribute(n,"auto"),t}}})),m.style||(k.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}}),/^(?:input|select|textarea|button|object)$/i),Ot=/^(?:a|area)$/i,Rt=(k.fn.extend({prop:function(e,t){return f(this,k.prop,e,t,1<arguments.length)},removeProp:function(e){return e=k.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(e){}})}}),k.extend({prop:function(e,t,n){var i,a,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&k.isXMLDoc(e)||(t=k.propFix[t]||t,a=k.propHooks[t]),void 0!==n?a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:e[t]=n:a&&"get"in a&&null!==(i=a.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=k.find.attr(e,"tabindex");return t?parseInt(t,10):Ft.test(e.nodeName)||Ot.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.hrefNormalized||k.each(["href","src"],function(e,t){k.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),m.optSelected||(k.propHooks.selected={get:function(e){return(e=e.parentNode)&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex,null},set:function(e){(e=e.parentNode)&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),k.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){k.propFix[this.toLowerCase()]=this}),m.enctype||(k.propFix.enctype="encoding"),/[\t\r\n\f]/g);function M(e){return k.attr(e,"class")||""}k.fn.extend({addClass:function(t){var e,n,i,a,o,r,l=0;if(k.isFunction(t))return this.each(function(e){k(this).addClass(t.call(this,e,M(this)))});if("string"==typeof t&&t)for(e=t.match(C)||[];n=this[l++];)if(r=M(n),i=1===n.nodeType&&(" "+r+" ").replace(Rt," ")){for(o=0;a=e[o++];)i.indexOf(" "+a+" ")<0&&(i+=a+" ");r!==(r=k.trim(i))&&k.attr(n,"class",r)}return this},removeClass:function(t){var e,n,i,a,o,r,l=0;if(k.isFunction(t))return this.each(function(e){k(this).removeClass(t.call(this,e,M(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof t&&t)for(e=t.match(C)||[];n=this[l++];)if(r=M(n),i=1===n.nodeType&&(" "+r+" ").replace(Rt," ")){for(o=0;a=e[o++];)for(;-1<i.indexOf(" "+a+" ");)i=i.replace(" "+a+" "," ");r!==(r=k.trim(i))&&k.attr(n,"class",r)}return this},toggleClass:function(a,t){var o=typeof a;return"boolean"==typeof t&&"string"==o?t?this.addClass(a):this.removeClass(a):k.isFunction(a)?this.each(function(e){k(this).toggleClass(a.call(this,e,M(this),t),t)}):this.each(function(){var e,t,n,i;if("string"==o)for(t=0,n=k(this),i=a.match(C)||[];e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==a&&"boolean"!=o||((e=M(this))&&k._data(this,"__className__",e),k.attr(this,"class",!e&&!1!==a&&k._data(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,i=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+M(t)+" ").replace(Rt," ").indexOf(i))return!0;return!1}}),k.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,n){k.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),k.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var j=w.location,Pt=k.now(),qt=/\?/,Bt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g,zt=(k.parseJSON=function(e){var a,o,t;return w.JSON&&w.JSON.parse?w.JSON.parse(e+""):(o=null,(t=k.trim(e+""))&&!k.trim(t.replace(Bt,function(e,t,n,i){return 0===(o=a&&t?0:o)?e:(a=n||t,o+=!i-!n,"")}))?Function("return "+t)():k.error("Invalid JSON: "+e))},k.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{w.DOMParser?t=(new w.DOMParser).parseFromString(e,"text/xml"):((t=new w.ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||k.error("Invalid XML: "+e),t},/#.*$/),Wt=/([?&])_=[^&]*/,Yt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,$t=/^(?:GET|HEAD)$/,Xt=/^\/\//,Vt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ut={},Gt={},Kt="*/".concat("*"),Jt=j.href,_=Vt.exec(Jt.toLowerCase())||[];function Zt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,a=e.toLowerCase().match(C)||[];if(k.isFunction(t))for(;n=a[i++];)"+"===n.charAt(0)?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Qt(t,i,a,o){var r={},l=t===Gt;function s(e){var n;return r[e]=!0,k.each(t[e]||[],function(e,t){return"string"!=typeof(t=t(i,a,o))||l||r[t]?l?!(n=t):void 0:(i.dataTypes.unshift(t),s(t),!1)}),n}return s(i.dataTypes[0])||!r["*"]&&s("*")}function en(e,t){var n,i,a=k.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((a[i]?e:n=n||{})[i]=t[i]);return n&&k.extend(!0,e,n),e}k.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Jt,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(_[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Kt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":k.parseJSON,"text xml":k.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?en(en(e,k.ajaxSettings),t):en(k.ajaxSettings,e)},ajaxPrefilter:Zt(Ut),ajaxTransport:Zt(Gt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var n,s,c,d,u,f,i,p=k.ajaxSetup({},t=t||{}),h=p.context||p,y=p.context&&(h.nodeType||h.jquery)?k(h):k.event,m=k.Deferred(),g=k.Callbacks("once memory"),v=p.statusCode||{},a={},o={},x=0,r="canceled",b={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!i)for(i={};t=Yt.exec(c);)i[t[1].toLowerCase()]=t[2];t=i[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?c:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=o[n]=o[n]||e,a[e]=t),this},overrideMimeType:function(e){return x||(p.mimeType=e),this},statusCode:function(e){if(e)if(x<2)for(var t in e)v[t]=[v[t],e[t]];else b.always(e[b.status]);return this},abort:function(e){return e=e||r,f&&f.abort(e),l(0,e),this}};if(m.promise(b).complete=g.add,b.success=b.done,b.error=b.fail,p.url=((e||p.url||Jt)+"").replace(zt,"").replace(Xt,_[1]+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=k.trim(p.dataType||"*").toLowerCase().match(C)||[""],null==p.crossDomain&&(e=Vt.exec(p.url.toLowerCase()),p.crossDomain=!(!e||e[1]===_[1]&&e[2]===_[2]&&(e[3]||("http:"===e[1]?"80":"443"))===(_[3]||("http:"===_[1]?"80":"443")))),p.data&&p.processData&&"string"!=typeof p.data&&(p.data=k.param(p.data,p.traditional)),Qt(Ut,p,t,b),2!==x){for(n in(u=k.event&&p.global)&&0==k.active++&&k.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!$t.test(p.type),s=p.url,p.hasContent||(p.data&&(s=p.url+=(qt.test(s)?"&":"?")+p.data,delete p.data),!1===p.cache&&(p.url=Wt.test(s)?s.replace(Wt,"$1_="+Pt++):s+(qt.test(s)?"&":"?")+"_="+Pt++)),p.ifModified&&(k.lastModified[s]&&b.setRequestHeader("If-Modified-Since",k.lastModified[s]),k.etag[s])&&b.setRequestHeader("If-None-Match",k.etag[s]),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&b.setRequestHeader("Content-Type",p.contentType),b.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Kt+"; q=0.01":""):p.accepts["*"]),p.headers)b.setRequestHeader(n,p.headers[n]);if(p.beforeSend&&(!1===p.beforeSend.call(h,b,p)||2===x))return b.abort();for(n in r="abort",{success:1,error:1,complete:1})b[n](p[n]);if(f=Qt(Gt,p,t,b)){if(b.readyState=1,u&&y.trigger("ajaxSend",[b,p]),2===x)return b;p.async&&0<p.timeout&&(d=w.setTimeout(function(){b.abort("timeout")},p.timeout));try{x=1,f.send(a,l)}catch(e){if(!(x<2))throw e;l(-1,e)}}else l(-1,"No Transport")}return b;function l(e,t,n,i){var a,o,r,l=t;2!==x&&(x=2,d&&w.clearTimeout(d),f=void 0,c=i||"",b.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(r=function(e,t,n){for(var i,a,o,r,l=e.contents,s=e.dataTypes;"*"===s[0];)s.shift(),void 0===a&&(a=e.mimeType||t.getResponseHeader("Content-Type"));if(a)for(r in l)if(l[r]&&l[r].test(a)){s.unshift(r);break}if(s[0]in n)o=s[0];else{for(r in n){if(!s[0]||e.converters[r+" "+s[0]]){o=r;break}i=i||r}o=o||i}if(o)return o!==s[0]&&s.unshift(o),n[o]}(p,b,n)),r=function(e,t,n,i){var a,o,r,l,s,c={},d=e.dataTypes.slice();if(d[1])for(r in e.converters)c[r.toLowerCase()]=e.converters[r];for(o=d.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!s&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),s=o,o=d.shift())if("*"===o)o=s;else if("*"!==s&&s!==o){if(!(r=c[s+" "+o]||c["* "+o]))for(a in c)if((l=a.split(" "))[1]===o&&(r=c[s+" "+l[0]]||c["* "+l[0]])){!0===r?r=c[a]:!0!==c[a]&&(o=l[0],d.unshift(l[1]));break}if(!0!==r)if(r&&e.throws)t=r(t);else try{t=r(t)}catch(e){return{state:"parsererror",error:r?e:"No conversion from "+s+" to "+o}}}return{state:"success",data:t}}(p,r,b,i),i?(p.ifModified&&((n=b.getResponseHeader("Last-Modified"))&&(k.lastModified[s]=n),n=b.getResponseHeader("etag"))&&(k.etag[s]=n),204===e||"HEAD"===p.type?l="nocontent":304===e?l="notmodified":(l=r.state,a=r.data,i=!(o=r.error))):(o=l,!e&&l||(l="error",e<0&&(e=0))),b.status=e,b.statusText=(t||l)+"",i?m.resolveWith(h,[a,l,b]):m.rejectWith(h,[b,l,o]),b.statusCode(v),v=void 0,u&&y.trigger(i?"ajaxSuccess":"ajaxError",[b,p,i?a:o]),g.fireWith(h,[b,l]),u)&&(y.trigger("ajaxComplete",[b,p]),--k.active||k.event.trigger("ajaxStop"))}},getJSON:function(e,t,n){return k.get(e,t,n,"json")},getScript:function(e,t){return k.get(e,void 0,t,"script")}}),k.each(["get","post"],function(e,a){k[a]=function(e,t,n,i){return k.isFunction(t)&&(i=i||n,n=t,t=void 0),k.ajax(k.extend({url:e,type:a,dataType:i,data:t,success:n},k.isPlainObject(e)&&e))}}),k._evalUrl=function(e){return k.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},k.fn.extend({wrapAll:function(t){var e;return k.isFunction(t)?this.each(function(e){k(this).wrapAll(t.call(this,e))}):(this[0]&&(e=k(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)),this)},wrapInner:function(n){return k.isFunction(n)?this.each(function(e){k(this).wrapInner(n.call(this,e))}):this.each(function(){var e=k(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=k.isFunction(t);return this.each(function(e){k(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(){return this.parent().each(function(){k.nodeName(this,"body")||k(this).replaceWith(this.childNodes)}).end()}}),k.expr.filters.hidden=function(e){if(m.reliableHiddenOffsets())return e.offsetWidth<=0&&e.offsetHeight<=0&&!e.getClientRects().length;var t,n=e;if(!k.contains(n.ownerDocument||h,n))return!0;for(;n&&1===n.nodeType;){if("none"===((t=n).style&&t.style.display||k.css(t,"display"))||"hidden"===n.type)return!0;n=n.parentNode}return!1},k.expr.filters.visible=function(e){return!k.expr.filters.hidden(e)};var tn=/%20/g,nn=/\[\]$/,an=/\r?\n/g,on=/^(?:submit|button|image|reset|file)$/i,rn=/^(?:input|select|textarea|keygen)/i,ln=(k.param=function(e,t){function n(e,t){t=k.isFunction(t)?t():null==t?"":t,a[a.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)}var i,a=[];if(void 0===t&&(t=k.ajaxSettings&&k.ajaxSettings.traditional),k.isArray(e)||e.jquery&&!k.isPlainObject(e))k.each(e,function(){n(this.name,this.value)});else for(i in e)!function n(i,e,a,o){if(k.isArray(e))k.each(e,function(e,t){a||nn.test(i)?o(i,t):n(i+"["+("object"==typeof t&&null!=t?e:"")+"]",t,a,o)});else if(a||"object"!==k.type(e))o(i,e);else for(var t in e)n(i+"["+t+"]",e[t],a,o)}(i,e[i],t,n);return a.join("&").replace(tn,"+")},k.fn.extend({serialize:function(){return k.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=k.prop(this,"elements");return e?k.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!k(this).is(":disabled")&&rn.test(this.nodeName)&&!on.test(e)&&(this.checked||!ye.test(e))}).map(function(e,t){var n=k(this).val();return null==n?null:k.isArray(n)?k.map(n,function(e){return{name:t.name,value:e.replace(an,"\r\n")}}):{name:t.name,value:n.replace(an,"\r\n")}}).get()}}),k.ajaxSettings.xhr=void 0!==w.ActiveXObject?function(){return this.isLocal?dn():8<h.documentMode?cn():/^(get|post|head|put|delete|options)$/i.test(this.type)&&cn()||dn()}:cn,0),sn={},H=k.ajaxSettings.xhr();function cn(){try{return new w.XMLHttpRequest}catch(e){}}function dn(){try{return new w.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}w.attachEvent&&w.attachEvent("onunload",function(){for(var e in sn)sn[e](void 0,!0)}),m.cors=!!H&&"withCredentials"in H,(m.ajax=!!H)&&k.ajaxTransport(function(s){var c;if(!s.crossDomain||m.cors)return{send:function(e,o){var t,r=s.xhr(),l=++ln;if(r.open(s.type,s.url,s.async,s.username,s.password),s.xhrFields)for(t in s.xhrFields)r[t]=s.xhrFields[t];for(t in s.mimeType&&r.overrideMimeType&&r.overrideMimeType(s.mimeType),s.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)void 0!==e[t]&&r.setRequestHeader(t,e[t]+"");r.send(s.hasContent&&s.data||null),c=function(e,t){var n,i,a;if(c&&(t||4===r.readyState))if(delete sn[l],c=void 0,r.onreadystatechange=k.noop,t)4!==r.readyState&&r.abort();else{a={},n=r.status,"string"==typeof r.responseText&&(a.text=r.responseText);try{i=r.statusText}catch(e){i=""}n||!s.isLocal||s.crossDomain?1223===n&&(n=204):n=a.text?200:404}a&&o(n,i,a,r.getAllResponseHeaders())},s.async?4===r.readyState?w.setTimeout(c):r.onreadystatechange=sn[l]=c:c()},abort:function(){c&&c(void 0,!0)}}}),k.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return k.globalEval(e),e}}}),k.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),k.ajaxTransport("script",function(t){var i,a;if(t.crossDomain)return a=h.head||k("head")[0]||h.documentElement,{send:function(e,n){(i=h.createElement("script")).async=!0,t.scriptCharset&&(i.charset=t.scriptCharset),i.src=t.url,i.onload=i.onreadystatechange=function(e,t){!t&&i.readyState&&!/loaded|complete/.test(i.readyState)||(i.onload=i.onreadystatechange=null,i.parentNode&&i.parentNode.removeChild(i),i=null,t)||n(200,"success")},a.insertBefore(i,a.firstChild)},abort:function(){i&&i.onload(void 0,!0)}}});var un=[],fn=/(=)\?(?=&|$)|\?\?/,pn=(k.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=un.pop()||k.expando+"_"+Pt++;return this[e]=!0,e}}),k.ajaxPrefilter("json jsonp",function(e,t,n){var i,a,o,r=!1!==e.jsonp&&(fn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&fn.test(e.data)&&"data");if(r||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=k.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,r?e[r]=e[r].replace(fn,"$1"+i):!1!==e.jsonp&&(e.url+=(qt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||k.error(i+" was not called"),o[0]},e.dataTypes[0]="json",a=w[i],w[i]=function(){o=arguments},n.always(function(){void 0===a?k(w).removeProp(i):w[i]=a,e[i]&&(e.jsonpCallback=t.jsonpCallback,un.push(i)),o&&k.isFunction(a)&&a(o[0]),o=a=void 0}),"script"}),k.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||h;var i=G.exec(e),n=!n&&[];return i?[t.createElement(i[1])]:(i=Ee([e],t,n),n&&n.length&&k(n).remove(),k.merge([],i.childNodes))},k.fn.load);function hn(e){return k.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}return k.fn.load=function(e,t,n){var i,a,o,r,l;return"string"!=typeof e&&pn?pn.apply(this,arguments):(r=this,-1<(l=e.indexOf(" "))&&(i=k.trim(e.slice(l,e.length)),e=e.slice(0,l)),k.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(a="POST"),0<r.length&&k.ajax({url:e,type:a||"GET",dataType:"html",data:t}).done(function(e){o=arguments,r.html(i?k("<div>").append(k.parseHTML(e)).find(i):e)}).always(n&&function(e,t){r.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this)},k.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){k.fn[t]=function(e){return this.on(t,e)}}),k.expr.filters.animated=function(t){return k.grep(k.timers,function(e){return t===e.elem}).length},k.offset={setOffset:function(e,t,n){var i,a,o,r,l=k.css(e,"position"),s=k(e),c={};"static"===l&&(e.style.position="relative"),o=s.offset(),i=k.css(e,"top"),r=k.css(e,"left"),l=("absolute"===l||"fixed"===l)&&-1<k.inArray("auto",[i,r])?(a=(l=s.position()).top,l.left):(a=parseFloat(i)||0,parseFloat(r)||0),null!=(t=k.isFunction(t)?t.call(e,n,k.extend({},o)):t).top&&(c.top=t.top-o.top+a),null!=t.left&&(c.left=t.left-o.left+l),"using"in t?t.using.call(e,c):s.css(c)}},k.fn.extend({offset:function(t){var e,n,i,a;return arguments.length?void 0===t?this:this.each(function(e){k.offset.setOffset(this,t,e)}):(n={top:0,left:0},(a=(i=this[0])&&i.ownerDocument)?(e=a.documentElement,k.contains(e,i)?(void 0!==i.getBoundingClientRect&&(n=i.getBoundingClientRect()),i=hn(a),{top:n.top+(i.pageYOffset||e.scrollTop)-(e.clientTop||0),left:n.left+(i.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):n):void 0)},position:function(){var e,t,n,i;if(this[0])return n={top:0,left:0},i=this[0],"fixed"===k.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),(n=k.nodeName(e[0],"html")?n:e.offset()).top+=k.css(e[0],"borderTopWidth",!0),n.left+=k.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-k.css(i,"marginTop",!0),left:t.left-n.left-k.css(i,"marginLeft",!0)}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&!k.nodeName(e,"html")&&"static"===k.css(e,"position");)e=e.offsetParent;return e||ct})}}),k.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,a){var o=/Y/.test(a);k.fn[t]=function(e){return f(this,function(e,t,n){var i=hn(e);if(void 0===n)return i?a in i?i[a]:i.document.documentElement[t]:e[t];i?i.scrollTo(o?k(i).scrollLeft():n,o?n:k(i).scrollTop()):e[t]=n},t,e,arguments.length,null)}}),k.each(["top","left"],function(e,n){k.cssHooks[n]=ut(m.pixelPosition,function(e,t){if(t)return t=p(e,n),st.test(t)?k(e).position()[n]+"px":t})}),k.each({Height:"height",Width:"width"},function(o,r){k.each({padding:"inner"+o,content:r,"":"outer"+o},function(i,e){k.fn[e]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),a=i||(!0===e||!0===t?"margin":"border");return f(this,function(e,t,n){var i;return k.isWindow(e)?e.document.documentElement["client"+o]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+o],i["scroll"+o],e.body["offset"+o],i["offset"+o],i["client"+o])):void 0===n?k.css(e,t,a):k.style(e,t,n,a)},r,n?e:void 0,n,null)}})}),k.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),k.fn.size=function(){return this.length},k.fn.andSelf=k.fn.addBack,layui.define(function(e){e("jquery",layui.$=k)}),k}),function(p,h){"use strict";function t(e){function t(){n.creat()}var n=this;n.index=++v.index,n.config.maxWidth=m(g).width()-30,n.config=m.extend({},n.config,u.config,e),document.body?t():setTimeout(function(){t()},30)}function y(e){return i.skin?" "+i.skin+" "+i.skin+"-"+e:""}var m,g,e,n=p.layui&&layui.define,u={getPath:(e=document.currentScript&&"SCRIPT"===document.currentScript.tagName.toUpperCase()?document.currentScript.src:function(){for(var e,t=document.getElementsByTagName("script"),n=t.length-1,i=n;0<i;i--)if("interactive"===t[i].readyState){e=t[i].src;break}return e||t[n].src}(),(p.LAYUI_GLOBAL||{}).layer_dir||e.substring(0,e.lastIndexOf("/")+1)),config:{removeFocus:!0},end:{},beforeEnd:{},events:{resize:{}},minStackIndex:0,minStackArr:[],btn:["确定","取消"],type:["dialog","page","iframe","loading","tips"],getStyle:function(e,t){return(e=e.currentStyle||p.getComputedStyle(e,null))[e.getPropertyValue?"getPropertyValue":"getAttribute"](t)},link:function(e,i,t){var n,a,o,r,l,s;v.path&&(n=document.getElementsByTagName("head")[0],a=document.createElement("link"),o=((t="string"==typeof i?i:t)||e).replace(/\.|\//g,""),r="layuicss-"+o,l="creating",s=0,a.rel="stylesheet",a.href=v.path+e,a.id=r,document.getElementById(r)||n.appendChild(a),"function"==typeof i)&&!function e(t){var n=document.getElementById(r);return 100<++s?p.console&&console.error(o+".css: Invalid"):void(1989===parseInt(u.getStyle(n,"width"))?(t===l&&n.removeAttribute("lay-status"),n.getAttribute("lay-status")===l?setTimeout(e,100):i()):(n.setAttribute("lay-status",l),setTimeout(function(){e(l)},100)))}()}},v={v:"3.7.0",ie:(e=navigator.userAgent.toLowerCase(),!!(p.ActiveXObject||"ActiveXObject"in p)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")),index:p.layer&&p.layer.v?1e5:0,path:u.getPath,config:function(e,t){return v.cache=u.config=m.extend({},u.config,e=e||{}),v.path=u.config.path||v.path,"string"==typeof e.extend&&(e.extend=[e.extend]),u.config.path&&v.ready(),e.extend&&(n?layui.addcss("modules/layer/"+e.extend):u.link("css/"+e.extend)),this},ready:function(e){var t=(n?"modules/":"css/")+"layer.css?v="+v.v;return n?layui["layui.all"]?"function"==typeof e&&e():layui.addcss(t,e,"layer"):u.link(t,e,"layer"),this},alert:function(e,t,n){var i="function"==typeof t;return v.open(m.extend({content:e,yes:n=i?t:n},i?{}:t))},confirm:function(e,t,n,i){var a="function"==typeof t;return a&&(i=n,n=t),v.open(m.extend({content:e,btn:u.btn,yes:n,btn2:i},a?{}:t))},msg:function(e,t,n){var i="function"==typeof t,a=((a=u.config.skin)?a+" "+a+"-msg":"")||"layui-layer-msg",o=f.anim.length-1;return i&&(n=t),v.open(m.extend({content:e,time:3e3,shade:!1,skin:a,title:!1,closeBtn:!1,btn:!1,resize:!1,end:n,removeFocus:!1},i&&!u.config.skin?{skin:a+" layui-layer-hui",anim:o}:(-1!==(t=t||{}).icon&&(t.icon!==h||u.config.skin)||(t.skin=a+" "+(t.skin||"layui-layer-hui")),t)))},load:function(e,t){return v.open(m.extend({type:3,icon:e||0,resize:!1,shade:.01,removeFocus:!1},t))},tips:function(e,t,n){return v.open(m.extend({type:4,content:[e,t],closeBtn:!1,time:3e3,shade:!1,resize:!1,fixed:!1,maxWidth:260,removeFocus:!1},n))}},f=(t.pt=t.prototype,["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"]),x=(f.anim={0:"layer-anim-00",1:"layer-anim-01",2:"layer-anim-02",3:"layer-anim-03",4:"layer-anim-04",5:"layer-anim-05",6:"layer-anim-06",slideDown:"layer-anim-slide-down",slideLeft:"layer-anim-slide-left",slideUp:"layer-anim-slide-up",slideRight:"layer-anim-slide-right"},f.SHADE="layui-layer-shade",f.MOVE="layui-layer-move","LAYUI-LAYER-SHADE-KEY"),s="LAYUI_LAYER_CONTENT_RECORD_HEIGHT",i=(t.pt.config={type:0,shade:.3,fixed:!0,move:f[1],title:"信息",offset:"auto",area:"auto",closeBtn:1,icon:-1,time:0,zIndex:19891014,maxWidth:360,anim:0,isOutAnim:!0,minStack:!0,moveType:1,resize:!0,scrollbar:!0,tips:2},t.pt.vessel=function(e,t){var n,i=this.index,a=this.config,o=a.zIndex+i,r="object"==typeof a.title,l=a.maxmin&&(1===a.type||2===a.type),r=a.title?'<div class="layui-layer-title" style="'+(r?a.title[1]:"")+'">'+(r?a.title[0]:a.title)+"</div>":"";return a.zIndex=o,t([a.shade?'<div class="'+f.SHADE+'" id="'+f.SHADE+i+'" times="'+i+'" style="z-index:'+(o-1)+'; "></div>':"",'<div class="'+f[0]+" layui-layer-"+u.type[a.type]+(0!=a.type&&2!=a.type||a.shade?"":" layui-layer-border")+" "+(a.skin||"")+'" id="'+f[0]+i+'" type="'+u.type[a.type]+'" times="'+i+'" showtime="'+a.time+'" conType="'+(e?"object":"string")+'" style="z-index: '+o+"; width:"+a.area[0]+";height:"+a.area[1]+";position:"+(a.fixed?"fixed;":"absolute;")+'">'+(e&&2!=a.type?"":r)+"<div"+(a.id?' id="'+a.id+'"':"")+' class="layui-layer-content'+(0==a.type&&-1!==a.icon?" layui-layer-padding":"")+(3==a.type?" layui-layer-loading"+a.icon:"")+'">'+(i=["layui-icon-tips","layui-icon-success","layui-icon-error","layui-icon-question","layui-icon-lock","layui-icon-face-cry","layui-icon-face-smile"],o="layui-anim layui-anim-rotate layui-anim-loop",0==a.type&&-1!==a.icon?'<i class="layui-layer-face layui-icon '+((n=16==a.icon?"layui-icon layui-icon-loading "+o:n)||i[a.icon]||i[0])+'"></i>':3==a.type?(n=["layui-icon-loading","layui-icon-loading-1"],2==a.icon?'<div class="layui-layer-loading-2 '+o+'"></div>':'<i class="layui-layer-loading-icon layui-icon '+(n[a.icon]||n[0])+" "+o+'"></i>'):"")+((1!=a.type||!e)&&a.content||"")+'</div><div class="layui-layer-setwin">'+(i=[],l&&(i.push('<span class="layui-layer-min"></span>'),i.push('<span class="layui-layer-max"></span>')),a.closeBtn&&i.push('<span class="layui-icon layui-icon-close '+[f[7],f[7]+(a.title?a.closeBtn:4==a.type?"1":"2")].join(" ")+'"></span>'),i.join(""))+"</div>"+(a.btn?function(){var e="";"string"==typeof a.btn&&(a.btn=[a.btn]);for(var t,n=0,i=a.btn.length;n<i;n++)e+='<a class="'+f[6]+n+'">'+a.btn[n]+"</a>";return'<div class="'+(t=[f[6]],a.btnAlign&&t.push(f[6]+"-"+a.btnAlign),t.join(" "))+'">'+e+"</div>"}():"")+(a.resize?'<span class="layui-layer-resize"></span>':"")+"</div>"],r,m('<div class="'+f.MOVE+'" id="'+f.MOVE+'"></div>')),this},t.pt.creat=function(){function e(e){var t;r.shift&&(r.anim=r.shift),f.anim[r.anim]&&(t="layer-anim "+f.anim[r.anim],e.addClass(t).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){m(this).removeClass(t)}))}var t,n,i,a,o=this,r=o.config,l=o.index,s="object"==typeof(d=r.content),c=m("body");if(r.id&&m("."+f[0]).find("#"+r.id)[0])n=(t=m("#"+r.id).closest("."+f[0])).attr("times"),i=t.data("config"),a=m("#"+f.SHADE+n),"min"===(t.data("maxminStatus")||{})?v.restore(n):i.hideOnClose&&(a.show(),t.show(),e(t),setTimeout(function(){a.css({opacity:a.data(x)})},10));else{switch(r.removeFocus&&document.activeElement&&document.activeElement.blur(),"string"==typeof r.area&&(r.area="auto"===r.area?["",""]:[r.area,""]),6==v.ie&&(r.fixed=!1),r.type){case 0:r.btn="btn"in r?r.btn:u.btn[0],v.closeAll("dialog");break;case 2:var d=r.content=s?r.content:[r.content||"","auto"];r.content='<iframe scrolling="'+(r.content[1]||"auto")+'" allowtransparency="true" id="'+f[4]+l+'" name="'+f[4]+l+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+r.content[0]+'"></iframe>';break;case 3:delete r.title,delete r.closeBtn,-1===r.icon&&r.icon,v.closeAll("loading");break;case 4:s||(r.content=[r.content,"body"]),r.follow=r.content[1],r.content=r.content[0]+'<i class="layui-layer-TipsG"></i>',delete r.title,r.tips="object"==typeof r.tips?r.tips:[r.tips,!0],r.tipsMore||v.closeAll("tips")}o.vessel(s,function(e,t,n){c.append(e[0]),s?2==r.type||4==r.type?m("body").append(e[1]):d.parents("."+f[0])[0]||(d.data("display",d.css("display")).show().addClass("layui-layer-wrap").wrap(e[1]),m("#"+f[0]+l).find("."+f[5]).before(t)):c.append(e[1]),m("#"+f.MOVE)[0]||c.append(u.moveElem=n),o.layero=m("#"+f[0]+l),o.shadeo=m("#"+f.SHADE+l),r.scrollbar||u.setScrollbar(l)}).auto(l),o.shadeo.css({"background-color":r.shade[1]||"#000",opacity:r.shade[0]||r.shade,transition:r.shade[2]||""}),o.shadeo.data(x,r.shade[0]||r.shade),2==r.type&&6==v.ie&&o.layero.find("iframe").attr("src",d[0]),4==r.type?o.tips():(o.offset(),parseInt(u.getStyle(document.getElementById(f.MOVE),"z-index"))||(o.layero.css("visibility","hidden"),v.ready(function(){o.offset(),o.layero.css("visibility","visible")}))),r.fixed&&!u.events.resize[o.index]&&(u.events.resize[o.index]=function(){o.resize()},g.on("resize",u.events.resize[o.index])),r.time<=0||setTimeout(function(){v.close(o.index)},r.time),o.move().callback(),e(o.layero),o.layero.data("config",r)}},t.pt.resize=function(){var e=this,t=e.config;e.offset(),(/^\d+%$/.test(t.area[0])||/^\d+%$/.test(t.area[1]))&&e.auto(e.index),4==t.type&&e.tips()},t.pt.auto=function(e){var t=this.config,n=m("#"+f[0]+e),i=((""===t.area[0]||"auto"===t.area[0])&&0<t.maxWidth&&(v.ie&&v.ie<8&&t.btn&&n.width(n.innerWidth()),n.outerWidth()>t.maxWidth)&&n.width(t.maxWidth),[n.innerWidth(),n.innerHeight()]),a=n.find(f[1]).outerHeight()||0,o=n.find("."+f[6]).outerHeight()||0,e=function(e){(e=n.find(e)).height(i[1]-a-o-2*(0|parseFloat(e.css("padding-top"))))};return 2===t.type?e("iframe"):""===t.area[1]||"auto"===t.area[1]?0<t.maxHeight&&n.outerHeight()>t.maxHeight?(i[1]=t.maxHeight,e("."+f[5])):t.fixed&&i[1]>=g.height()&&(i[1]=g.height(),e("."+f[5])):e("."+f[5]),this},t.pt.offset=function(){var e=this,t=e.config,n=e.layero,i=[n.outerWidth(),n.outerHeight()],a="object"==typeof t.offset;e.offsetTop=(g.height()-i[1])/2,e.offsetLeft=(g.width()-i[0])/2,a?(e.offsetTop=t.offset[0],e.offsetLeft=t.offset[1]||e.offsetLeft):"auto"!==t.offset&&("t"===t.offset?e.offsetTop=0:"r"===t.offset?e.offsetLeft=g.width()-i[0]:"b"===t.offset?e.offsetTop=g.height()-i[1]:"l"===t.offset?e.offsetLeft=0:"lt"===t.offset?(e.offsetTop=0,e.offsetLeft=0):"lb"===t.offset?(e.offsetTop=g.height()-i[1],e.offsetLeft=0):"rt"===t.offset?(e.offsetTop=0,e.offsetLeft=g.width()-i[0]):"rb"===t.offset?(e.offsetTop=g.height()-i[1],e.offsetLeft=g.width()-i[0]):e.offsetTop=t.offset),t.fixed||(e.offsetTop=/%$/.test(e.offsetTop)?g.height()*parseFloat(e.offsetTop)/100:parseFloat(e.offsetTop),e.offsetLeft=/%$/.test(e.offsetLeft)?g.width()*parseFloat(e.offsetLeft)/100:parseFloat(e.offsetLeft),e.offsetTop+=g.scrollTop(),e.offsetLeft+=g.scrollLeft()),"min"===n.data("maxminStatus")&&(e.offsetTop=g.height()-(n.find(f[1]).outerHeight()||0),e.offsetLeft=n.css("left")),n.css({top:e.offsetTop,left:e.offsetLeft})},t.pt.tips=function(){var e=this.config,t=this.layero,n=[t.outerWidth(),t.outerHeight()],i={width:(o=(o=m(e.follow))[0]?o:m("body")).outerWidth(),height:o.outerHeight(),top:o.offset().top,left:o.offset().left},a=t.find(".layui-layer-TipsG"),o=e.tips[0];e.tips[1]||a.remove(),i.autoLeft=function(){0<i.left+n[0]-g.width()?(i.tipLeft=i.left+i.width-n[0],a.css({right:12,left:"auto"})):i.tipLeft=i.left},i.where=[function(){i.autoLeft(),i.tipTop=i.top-n[1]-10,a.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",e.tips[1])},function(){i.tipLeft=i.left+i.width+10,i.tipTop=i.top-(.75*i.height<21?21-.5*i.height:0),i.tipTop=Math.max(i.tipTop,0),a.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",e.tips[1])},function(){i.autoLeft(),i.tipTop=i.top+i.height+10,a.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",e.tips[1])},function(){i.tipLeft=i.left-n[0]-10,i.tipTop=i.top-(.75*i.height<21?21-.5*i.height:0),i.tipTop=Math.max(i.tipTop,0),a.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",e.tips[1])}],i.where[o-1](),1===o?i.top-(g.scrollTop()+n[1]+16)<0&&i.where[2]():2===o?0<g.width()-(i.left+i.width+n[0]+16)||i.where[3]():3===o?0<i.top-g.scrollTop()+i.height+n[1]+16-g.height()&&i.where[0]():4===o&&0<n[0]+16-i.left&&i.where[1](),t.find("."+f[5]).css({"background-color":e.tips[1],"padding-right":e.closeBtn?"30px":""}),t.css({left:i.tipLeft-(e.fixed?g.scrollLeft():0),top:i.tipTop-(e.fixed?g.scrollTop():0)})},t.pt.move=function(){var i=this,a=i.config,e=m(document),o=i.layero,s=["LAY_MOVE_DICT","LAY_RESIZE_DICT"],t=o.find(a.move),n=o.find(".layui-layer-resize");return a.move&&t.css("cursor","move"),t.on("mousedown",function(e){var t,n;e.button||(t=m(this),n={},a.move&&(n.layero=o,n.config=a,n.offset=[e.clientX-parseFloat(o.css("left")),e.clientY-parseFloat(o.css("top"))],t.data(s[0],n),u.eventMoveElem=t,u.moveElem.css("cursor","move").show()),e.preventDefault())}),n.on("mousedown",function(e){var t=m(this),n={};a.resize&&(n.layero=o,n.config=a,n.offset=[e.clientX,e.clientY],n.index=i.index,n.area=[o.outerWidth(),o.outerHeight()],t.data(s[1],n),u.eventResizeElem=t,u.moveElem.css("cursor","se-resize").show()),e.preventDefault()}),u.docEvent||(e.on("mousemove",function(e){var t,n,i,a,o,r,l;u.eventMoveElem&&(t=(a=u.eventMoveElem.data(s[0])||{}).layero,o=a.config,r=e.clientX-a.offset[0],l=e.clientY-a.offset[1],n="fixed"===t.css("position"),e.preventDefault(),a.stX=n?0:g.scrollLeft(),a.stY=n?0:g.scrollTop(),o.moveOut||(n=g.width()-t.outerWidth()+a.stX,i=g.height()-t.outerHeight()+a.stY,n<(r=r<a.stX?a.stX:r)&&(r=n),i<(l=l<a.stY?a.stY:l)&&(l=i)),t.css({left:r,top:l})),u.eventResizeElem&&(o=(a=u.eventResizeElem.data(s[1])||{}).config,r=e.clientX-a.offset[0],l=e.clientY-a.offset[1],e.preventDefault(),v.style(a.index,{width:a.area[0]+r,height:a.area[1]+l}),o.resizing)&&o.resizing(a.layero)}).on("mouseup",function(e){var t,n;u.eventMoveElem&&(n=(t=u.eventMoveElem.data(s[0])||{}).config,u.eventMoveElem.removeData(s[0]),delete u.eventMoveElem,u.moveElem.hide(),n.moveEnd)&&n.moveEnd(t.layero),u.eventResizeElem&&(u.eventResizeElem.removeData(s[1]),delete u.eventResizeElem,u.moveElem.hide())}),u.docEvent=!0),i},t.pt.btnLoading=function(e,t){t?e.find(".layui-layer-btn-loading-icon")[0]||e.addClass("layui-layer-btn-is-loading").attr({disabled:""}).prepend('<i class="layui-layer-btn-loading-icon layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>'):e.removeClass("layui-layer-btn-is-loading").removeAttr("disabled").find(".layui-layer-btn-loading-icon").remove()},t.pt.callback=function(){var i=this,a=i.layero,o=i.config;i.openLayer(),o.success&&(2==o.type?a.find("iframe").on("load",function(){o.success(a,i.index,i)}):o.success(a,i.index,i)),6==v.ie&&i.IE6(a),a.find("."+f[6]).children("a").on("click",function(){var e,t=m(this),n=t.index();t.attr("disabled")||(o.btnAsync?(e=0===n?o.yes||o.btn1:o["btn"+(n+1)],i.loading=function(e){i.btnLoading(t,e)},e?u.promiseLikeResolve(e.call(o,i.index,a,i)).then(function(e){!1!==e&&v.close(i.index)},function(e){e!==h&&p.console&&p.console.error("layer error hint: "+e)}):v.close(i.index)):0===n?o.yes?o.yes(i.index,a,i):o.btn1?o.btn1(i.index,a,i):v.close(i.index):!1!==(o["btn"+(n+1)]&&o["btn"+(n+1)](i.index,a,i))&&v.close(i.index))}),a.find("."+f[7]).on("click",function(){!1!==(o.cancel&&o.cancel(i.index,a,i))&&v.close(i.index)}),o.shadeClose&&i.shadeo.on("click",function(){v.close(i.index)}),a.find(".layui-layer-min").on("click",function(){!1!==(o.min&&o.min(a,i.index,i))&&v.min(i.index,o)}),a.find(".layui-layer-max").on("click",function(){m(this).hasClass("layui-layer-maxmin")?(v.restore(i.index),o.restore&&o.restore(a,i.index,i)):(v.full(i.index,o),setTimeout(function(){o.full&&o.full(a,i.index,i)},100))}),o.end&&(u.end[i.index]=o.end),o.beforeEnd&&(u.beforeEnd[i.index]=m.proxy(o.beforeEnd,o,a,i.index,i))},u.reselect=function(){m.each(m("select"),function(e,t){var n=m(this);n.parents("."+f[0])[0]||1==n.attr("layer")&&m("."+f[0]).length<1&&n.removeAttr("layer").show()})},t.pt.IE6=function(e){m("select").each(function(e,t){var n=m(this);n.parents("."+f[0])[0]||"none"!==n.css("display")&&n.attr({layer:"1"}).hide()})},t.pt.openLayer=function(){v.zIndex=this.config.zIndex,v.setTop=function(e){return v.zIndex=parseInt(e[0].style.zIndex),e.on("mousedown",function(){v.zIndex++,e.css("z-index",v.zIndex+1)}),v.zIndex}},u.record=function(e){if(!e[0])return p.console&&console.error("index error");var t=e.attr("type"),n=e.find(".layui-layer-content"),t=t===u.type[2]?n.children("iframe"):n,i=[e[0].style.width||u.getStyle(e[0],"width"),e[0].style.height||u.getStyle(e[0],"height"),e.position().top,e.position().left+parseFloat(e.css("margin-left"))];e.find(".layui-layer-max").addClass("layui-layer-maxmin"),e.attr({area:i}),n.data(s,u.getStyle(t[0],"height"))},u.setScrollbar=function(e){f.html.css("overflow","hidden").attr("layer-full",e)},u.restScrollbar=function(e){f.html.attr("layer-full")==e&&(f.html[0].style[f.html[0].style.removeProperty?"removeProperty":"removeAttribute"]("overflow"),f.html.removeAttr("layer-full"))},u.promiseLikeResolve=function(e){var t=m.Deferred();return e&&"function"==typeof e.then?e.then(t.resolve,t.reject):t.resolve(e),t.promise()},(p.layer=v).getChildFrame=function(e,t){return t=t||m("."+f[4]).attr("times"),m("#"+f[0]+t).find("iframe").contents().find(e)},v.getFrameIndex=function(e){return m("#"+e).parents("."+f[4]).attr("times")},v.iframeAuto=function(e){var t,n,i;e&&(t=v.getChildFrame("html",e).outerHeight(),n=(e=m("#"+f[0]+e)).find(f[1]).outerHeight()||0,i=e.find("."+f[6]).outerHeight()||0,e.css({height:t+n+i}),e.find("iframe").css({height:t}))},v.iframeSrc=function(e,t){m("#"+f[0]+e).find("iframe").attr("src",t)},v.style=function(e,t,n){var i=(e=m("#"+f[0]+e)).find(".layui-layer-content"),a=e.attr("type"),o=e.find(f[1]).outerHeight()||0,r=e.find("."+f[6]).outerHeight()||0;e.attr("minLeft"),a!==u.type[3]&&a!==u.type[4]&&(n||(parseFloat(t.width)<=260&&(t.width=260),parseFloat(t.height)-o-r<=64&&(t.height=64+o+r)),e.css(t),r=e.find("."+f[6]).outerHeight()||0,a===u.type[2]?e.find("iframe").css({height:("number"==typeof t.height?t.height:e.height())-o-r}):i.css({height:("number"==typeof t.height?t.height:e.height())-o-r-parseFloat(i.css("padding-top"))-parseFloat(i.css("padding-bottom"))}))},v.min=function(e,t){var n,i,a,o,r,l,s=m("#"+f[0]+e),c=s.data("maxminStatus");"min"!==c&&("max"===c&&v.restore(e),s.data("maxminStatus","min"),t=t||s.data("config")||{},c=m("#"+f.SHADE+e),n=s.find(".layui-layer-min"),i=s.find(f[1]).outerHeight()||0,o=(a="string"==typeof(o=s.attr("minLeft")))?o:181*u.minStackIndex+"px",r=s.css("position"),l={width:180,height:i,position:"fixed",overflow:"hidden"},u.record(s),0<u.minStackArr.length&&(o=u.minStackArr[0],u.minStackArr.shift()),parseFloat(o)+180>g.width()&&(o=g.width()-180-(u.minStackArr.edgeIndex=u.minStackArr.edgeIndex||0,u.minStackArr.edgeIndex+=3))<0&&(o=0),t.minStack&&(l.left=o,l.top=g.height()-i,a||u.minStackIndex++,s.attr("minLeft",o)),s.attr("position",r),v.style(e,l,!0),n.hide(),"page"===s.attr("type")&&s.find(f[4]).hide(),u.restScrollbar(e),c.hide())},v.restore=function(e){var t=m("#"+f[0]+e),n=m("#"+f.SHADE+e),i=t.find(".layui-layer-content"),a=t.attr("area").split(","),o=t.attr("type"),r=t.data("config")||{},l=i.data(s);t.removeData("maxminStatus"),v.style(e,{width:a[0],height:a[1],top:parseFloat(a[2]),left:parseFloat(a[3]),position:t.attr("position"),overflow:"visible"},!0),t.find(".layui-layer-max").removeClass("layui-layer-maxmin"),t.find(".layui-layer-min").show(),"page"===o&&t.find(f[4]).show(),r.scrollbar?u.restScrollbar(e):u.setScrollbar(e),l!==h&&(i.removeData(s),(o===u.type[2]?i.children("iframe"):i).css({height:l})),n.show()},v.full=function(t){var n=m("#"+f[0]+t),e=n.data("maxminStatus");"max"!==e&&("min"===e&&v.restore(t),n.data("maxminStatus","max"),u.record(n),f.html.attr("layer-full")||u.setScrollbar(t),setTimeout(function(){var e="fixed"===n.css("position");v.style(t,{top:e?0:g.scrollTop(),left:e?0:g.scrollLeft(),width:"100%",height:"100%"},!0),n.find(".layui-layer-min").hide()},100))},v.title=function(e,t){m("#"+f[0]+(t||v.index)).find(f[1]).html(e)},v.close=function(o,r){var e,t,l=(e=m("."+f[0]).children("#"+o).closest("."+f[0]))[0]?(o=e.attr("times"),e):m("#"+f[0]+o),s=l.attr("type"),n=l.data("config")||{},c=n.id&&n.hideOnClose;l[0]&&(t=function(){function e(){var e="layui-layer-wrap";if(c)return l.removeClass("layer-anim "+a),l.hide();if(s===u.type[1]&&"object"===l.attr("conType")){l.children(":not(."+f[5]+")").remove();for(var t=l.find("."+e),n=0;n<2;n++)t.unwrap();t.css("display",t.data("display")).removeClass(e)}else{if(s===u.type[2])try{var i=m("#"+f[4]+o)[0];i.contentWindow.document.write(""),i.contentWindow.close(),l.find("."+f[5])[0].removeChild(i)}catch(e){}l[0].innerHTML="",l.remove()}"function"==typeof u.end[o]&&u.end[o](),delete u.end[o],"function"==typeof r&&r(),u.events.resize[o]&&(g.off("resize",u.events.resize[o]),delete u.events.resize[o])}var a={slideDown:"layer-anim-slide-down-out",slideLeft:"layer-anim-slide-left-out",slideUp:"layer-anim-slide-up-out",slideRight:"layer-anim-slide-right-out"}[n.anim]||"layer-anim-close",t=m("#"+f.SHADE+o);v.ie&&v.ie<10||!n.isOutAnim?t[c?"hide":"remove"]():(t.css({opacity:0}),setTimeout(function(){t[c?"hide":"remove"]()},350)),n.isOutAnim&&l.addClass("layer-anim "+a),6==v.ie&&u.reselect(),u.restScrollbar(o),"string"==typeof l.attr("minLeft")&&(u.minStackIndex--,u.minStackArr.push(l.attr("minLeft"))),v.ie&&v.ie<10||!n.isOutAnim?e():setTimeout(function(){e()},200)},c||"function"!=typeof u.beforeEnd[o]?(delete u.beforeEnd[o],t()):u.promiseLikeResolve(u.beforeEnd[o]()).then(function(e){!1!==e&&(delete u.beforeEnd[o],t())},function(e){e!==h&&p.console&&p.console.error("layer error hint: "+e)}))},v.closeAll=function(n,i){"function"==typeof n&&(i=n,n=null);var a=m("."+f[0]);m.each(a,function(e){var t=m(this);(n?t.attr("type")===n:1)&&v.close(t.attr("times"),e===a.length-1?i:null)}),0===a.length&&"function"==typeof i&&i()},v.closeLast=function(n,e){var t,i=[],a=m.isArray(n);m("string"==typeof n?".layui-layer-"+n:".layui-layer").each(function(e,t){if(t=m(t),a&&-1===n.indexOf(t.attr("type"))||"none"===t.css("display"))return!0;i.push(Number(t.attr("times")))}),0<i.length&&(t=Math.max.apply(null,i),v.close(t,e))},v.cache||{});v.prompt=function(n,i){var e="",t="";"function"==typeof(n=n||{})&&(i=n),n.area&&(e='style="width: '+(o=n.area)[0]+"; height: "+o[1]+';"',delete n.area),n.placeholder&&(t=' placeholder="'+n.placeholder+'"');var a,o=2==n.formType?'<textarea class="layui-layer-input"'+e+t+"></textarea>":'<input type="'+(1==n.formType?"password":"text")+'" class="layui-layer-input"'+t+">",r=n.success;return delete n.success,v.open(m.extend({type:1,btn:["确定","取消"],content:o,skin:"layui-layer-prompt"+y("prompt"),maxWidth:g.width(),success:function(e){(a=e.find(".layui-layer-input")).val(n.value||"").focus(),"function"==typeof r&&r(e)},resize:!1,yes:function(e){var t=a.val();t.length>(n.maxlength||500)?v.tips("最多输入"+(n.maxlength||500)+"个字符",a,{tips:1}):i&&i(t,e,a)}},n))},v.tab=function(i){var a=(i=i||{}).tab||{},o="layui-this",r=i.success;return delete i.success,v.open(m.extend({type:1,skin:"layui-layer-tab"+y("tab"),resize:!1,title:function(){var e=a.length,t=1,n="";if(0<e)for(n='<span class="'+o+'">'+a[0].title+"</span>";t<e;t++)n+="<span>"+a[t].title+"</span>";return n}(),content:'<ul class="layui-layer-tabmain">'+function(){var e=a.length,t=1,n="";if(0<e)for(n='<li class="layui-layer-tabli '+o+'">'+(a[0].content||"no content")+"</li>";t<e;t++)n+='<li class="layui-layer-tabli">'+(a[t].content||"no  content")+"</li>";return n}()+"</ul>",success:function(e){var t=e.find(".layui-layer-title").children(),n=e.find(".layui-layer-tabmain").children();t.on("mousedown",function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0;var t=(e=m(this)).index();e.addClass(o).siblings().removeClass(o),n.eq(t).show().siblings().hide(),"function"==typeof i.change&&i.change(t)}),"function"==typeof r&&r(e)}},i))},v.photos=function(i,e,a){var r={};if((i=m.extend(!0,{toolbar:!0,footer:!0},i)).photos){var t=(d=!("string"==typeof i.photos||i.photos instanceof m))?i.photos:{},o=t.data||[],l=t.start||0,s=i.success;if(r.imgIndex=1+(0|l),i.img=i.img||"img",delete i.success,d){if(0===o.length)return v.msg("没有图片")}else{var n=m(i.photos),c=function(){o=[],n.find(i.img).each(function(e){var t=m(this);t.attr("layer-index",e),o.push({alt:t.attr("alt"),pid:t.attr("layer-pid"),src:t.attr("lay-src")||t.attr("layer-src")||t.attr("src"),thumb:t.attr("src")})})};if(c(),0===o.length)return;if(e||n.on("click",i.img,function(){c();var e=m(this).attr("layer-index");v.photos(m.extend(i,{photos:{start:e,data:o,tab:i.tab},full:i.full}),!0)}),!e)return}r.imgprev=function(e){r.imgIndex--,r.imgIndex<1&&(r.imgIndex=o.length),r.tabimg(e)},r.imgnext=function(e,t){r.imgIndex++,r.imgIndex>o.length&&(r.imgIndex=1,t)||r.tabimg(e)},r.keyup=function(e){var t;r.end||(t=e.keyCode,e.preventDefault(),37===t?r.imgprev(!0):39===t?r.imgnext(!0):27===t&&v.close(r.index))},r.tabimg=function(e){if(!(o.length<=1))return t.start=r.imgIndex-1,v.close(r.index),v.photos(i,!0,e)},r.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},r.image={},r.getTransform=function(e){var t=[],n=e.rotate,i=e.scaleX,e=e.scale;return r.isNumber(n)&&0!==n&&t.push("rotate("+n+"deg)"),r.isNumber(i)&&1!==i&&t.push("scaleX("+i+")"),r.isNumber(e)&&t.push("scale("+e+")"),t.length?t.join(" "):"none"},r.event=function(e,n,i){var a,o;r.main.find(".layui-layer-photos-prev").on("click",function(e){e.preventDefault(),r.imgprev(!0)}),r.main.find(".layui-layer-photos-next").on("click",function(e){e.preventDefault(),r.imgnext(!0)}),m(document).on("keyup",r.keyup),e.off("click").on("click","*[toolbar-event]",function(){var e=m(this);switch(e.attr("toolbar-event")){case"rotate":r.image.rotate=((r.image.rotate||0)+Number(e.attr("data-option")))%360,r.imgElem.css({transform:r.getTransform(r.image)});break;case"scalex":r.image.scaleX=-1===r.image.scaleX?1:-1,r.imgElem.css({transform:r.getTransform(r.image)});break;case"zoom":var t=Number(e.attr("data-option"));r.image.scale=(r.image.scale||1)+t,t<0&&r.image.scale<0-t&&(r.image.scale=0-t),r.imgElem.css({transform:r.getTransform(r.image)});break;case"reset":r.image.scaleX=1,r.image.scale=1,r.image.rotate=0,r.imgElem.css({transform:"none"});break;case"close":v.close(n)}i.offset(),i.auto(n)}),r.main.on("mousewheel DOMMouseScroll",function(e){var t=e.originalEvent.wheelDelta||-e.originalEvent.detail,n=r.main.find('[toolbar-event="zoom"]');(0<t?n.eq(0):n.eq(1)).trigger("click"),e.preventDefault()}),(p.layui||p.lay)&&(a=p.layui.lay||p.lay,o=function(e,t){var n=Date.now()-t.timeStart,n=t.distanceX/n,i=g.width()/3;(.25<Math.abs(n)||Math.abs(t.distanceX)>i)&&("left"===t.direction?r.imgnext(!0):"right"===t.direction&&r.imgprev(!0))},m.each([i.shadeo,r.main],function(e,t){a.touchSwipe(t,{onTouchEnd:o})}))},r.loadi=v.load(1,{shade:!("shade"in i)&&[.9,h,"unset"],scrollbar:!1});var d=o[l].src,u=function(e){v.close(r.loadi);var t,n=o[l].alt||"";a&&(i.anim=-1),r.index=v.open(m.extend({type:1,id:"layui-layer-photos",area:(e=[e.width,e.height],t=[m(p).width()-100,m(p).height()-100],!i.full&&(t[0]<e[0]||t[1]<e[1])&&((t=[e[0]/t[0],e[1]/t[1]])[1]<t[0]?(e[0]=e[0]/t[0],e[1]=e[1]/t[0]):t[0]<t[1]&&(e[0]=e[0]/t[1],e[1]=e[1]/t[1])),[e[0]+"px",e[1]+"px"]),title:!1,shade:[.9,h,"unset"],shadeClose:!0,closeBtn:!1,move:".layer-layer-photos-main img",moveType:1,scrollbar:!1,moveOut:!0,anim:5,isOutAnim:!1,skin:"layui-layer-photos"+y("photos"),content:'<div class="layer-layer-photos-main"><img src="'+o[l].src+'" alt="'+n+'" layer-pid="'+(o[l].pid||"")+'">'+(t=['<div class="layui-layer-photos-pointer">'],1<o.length&&t.push(['<div class="layer-layer-photos-page">','<span class="layui-icon layui-icon-left layui-layer-photos-prev"></span>','<span class="layui-icon layui-icon-right layui-layer-photos-next"></span>',"</div>"].join("")),i.toolbar&&t.push(['<div class="layui-layer-photos-toolbar layui-layer-photos-header">','<span toolbar-event="rotate" data-option="90" title="旋转"><i class="layui-icon layui-icon-refresh"></i></span>','<span toolbar-event="scalex" title="变换"><i class="layui-icon layui-icon-slider"></i></span>','<span toolbar-event="zoom" data-option="0.1" title="放大"><i class="layui-icon layui-icon-add-circle"></i></span>','<span toolbar-event="zoom" data-option="-0.1" title="缩小"><i class="layui-icon layui-icon-reduce-circle"></i></span>','<span toolbar-event="reset" title="还原"><i class="layui-icon layui-icon-refresh-1"></i></span>','<span toolbar-event="close" title="关闭"><i class="layui-icon layui-icon-close"></i></span>',"</div>"].join("")),i.footer&&t.push(['<div class="layui-layer-photos-toolbar layui-layer-photos-footer">',"<h3>"+n+"</h3>","<em>"+r.imgIndex+" / "+o.length+"</em>",'<a href="'+o[l].src+'" target="_blank">查看原图</a>',"</div>"].join("")),t.push("</div>"),t.join(""))+"</div>",success:function(e,t,n){r.main=e.find(".layer-layer-photos-main"),r.footer=e.find(".layui-layer-photos-footer"),r.imgElem=r.main.children("img"),r.event(e,t,n),i.tab&&i.tab(o[l],e),"function"==typeof s&&s(e)},end:function(){r.end=!0,m(document).off("keyup",r.keyup)}},i))},f=new Image;f.src=d,f.complete?u(f):(f.onload=function(){f.onload=null,u(f)},f.onerror=function(e){f.onerror=null,v.close(r.loadi),v.msg("当前图片地址异常，<br>是否继续查看下一张？",{time:3e4,btn:["下一张","不看了"],yes:function(){1<o.length&&r.imgnext(!0,!0)}})})}},u.run=function(e){g=(m=e)(p);var e=navigator.userAgent.toLowerCase(),e=/android|iphone|ipod|ipad|ios/.test(e),i=m(p);e&&m.each({Height:"height",Width:"width"},function(e,t){var n="inner"+e;g[t]=function(){return n in p?p[n]:i[t]()}}),f.html=m("html"),v.open=function(e){return new t(e).index}},p.layui&&layui.define?(v.ready(),layui.define(["jquery","lay"],function(e){v.path=layui.cache.dir,u.run(layui.$),e("layer",p.layer=v)})):"function"==typeof define&&define.amd?define(["jquery"],function(){return u.run(p.jQuery),v}):(v.ready(),u.run(p.jQuery))}(window),layui.define("jquery",function(e){"use strict";var d=layui.$,h=layui.hint(),t={fixbar:function(i){var a,t,e,n,o="layui-fixbar",r=d(document),l=(i=d.extend(!0,{target:"body",bars:[],default:!0,margin:160,duration:320},i),d(i.target)),s=i.scroll?d(i.scroll):d("body"===i.target?r:l),c=(i.default&&(i.bar1&&i.bars.push({type:"bar1",icon:"layui-icon-chat"}),i.bar2&&i.bars.push({type:"bar2",icon:"layui-icon-help"}),i.bars.push({type:"top",icon:"layui-icon-top"})),d("<ul>").addClass(o));layui.each(i.bars,function(e,t){var n=d('<li class="layui-icon">');n.addClass(t.icon).attr({"lay-type":t.type,style:t.style||(i.bgcolor?"background-color: "+i.bgcolor:"")}).html(t.content),n.on("click",function(){var e=d(this).attr("lay-type");"top"===e&&("body"===i.target?d("html,body"):s).animate({scrollTop:0},i.duration),"function"==typeof i.click&&i.click.call(this,e)}),"object"===layui.type(i.on)&&layui.each(i.on,function(e,t){n.on(e,function(){var e=d(this).attr("lay-type");"function"==typeof t&&t.call(this,e)})}),"top"===t.type&&(n.addClass("layui-fixbar-top"),a=n),c.append(n)}),l.find("."+o).remove(),"object"==typeof i.css&&c.css(i.css),l.append(c),a&&(e=function e(){return s.scrollTop()>=i.margin?t||(a.show(),t=1):t&&(a.hide(),t=0),e}()),s.on("scroll",function(){e&&(clearTimeout(n),n=setTimeout(function(){e()},100))})},countdown:function(a){a=d.extend(!0,{date:new Date,now:new Date},a);var o=arguments,r=(1<o.length&&(a.date=new Date(o[0]),a.now=new Date(o[1]),a.clock=o[2]),{options:a,clear:function(){clearTimeout(r.timer)},reload:function(e){this.clear(),d.extend(!0,this.options,{now:new Date},e),l()}}),l=("function"==typeof a.ready&&a.ready(),function e(){var t=new Date(a.date),n=new Date(a.now),t=0<(t=t.getTime()-n.getTime())?t:0,i={d:Math.floor(t/864e5),h:Math.floor(t/36e5)%24,m:Math.floor(t/6e4)%60,s:Math.floor(t/1e3)%60};return 1<o.length&&(i=[i.d,i.h,i.m,i.s]),r.timer=setTimeout(function(){n.setTime(n.getTime()+1e3),a.now=n,l()},1e3),"function"==typeof a.clock&&a.clock(i,r),t<=0&&(clearTimeout(r.timer),"function"==typeof a.done)&&a.done(i,r),e}());return r},timeAgo:function(e,t){var n=this,i=[[],[]],a=(new Date).getTime()-new Date(e).getTime();return 26784e5<a?(a=new Date(e),i[0][0]=n.digit(a.getFullYear(),4),i[0][1]=n.digit(a.getMonth()+1),i[0][2]=n.digit(a.getDate()),t||(i[1][0]=n.digit(a.getHours()),i[1][1]=n.digit(a.getMinutes()),i[1][2]=n.digit(a.getSeconds())),i[0].join("-")+" "+i[1].join(":")):864e5<=a?(a/1e3/60/60/24|0)+" 天前":36e5<=a?(a/1e3/60/60|0)+" 小时前":18e4<=a?(a/1e3/60|0)+" 分钟前":a<0?"未来":"刚刚"},digit:function(e,t){var n="";t=t||2;for(var i=(e=String(e)).length;i<t;i++)n+="0";return e<Math.pow(10,t)?n+(0|e):e},toDateString:function(e,t,n){var i,a,o,r,l,s,c,d,u,f,p;return null===e||""===e?"":(i=this,(a=new Date(function(){if(e)return isNaN(e)||"string"!=typeof e?e:parseInt(e)}()||new Date)).getDate()?(o=a.getFullYear(),r=a.getMonth(),l=a.getDate(),s=a.getHours(),c=a.getMinutes(),d=a.getSeconds(),u=a.getMilliseconds(),f=n&&n.customMeridiem||function(e,t){return(e=100*e+t)<600?"凌晨":e<900?"早上":e<1100?"上午":e<1300?"中午":e<1800?"下午":"晚上"},p={yy:function(){return String(o).slice(-2)},yyyy:function(){return i.digit(o,4)},M:function(){return String(r+1)},MM:function(){return i.digit(r+1)},d:function(){return String(l)},dd:function(){return i.digit(l)},H:function(){return String(s)},HH:function(){return i.digit(s)},h:function(){return String(s%12||12)},hh:function(){return i.digit(s%12||12)},A:function(){return f(s,c)},m:function(){return String(c)},mm:function(){return i.digit(c)},s:function(){return String(d)},ss:function(){return i.digit(d)},SSS:function(){return i.digit(u,3)}},(t=t||"yyyy-MM-dd HH:mm:ss").replace(/\[([^\]]+)]|y{1,4}|M{1,2}|d{1,2}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|SSS/g,function(e,t){return t||p[e]&&p[e]()||e})):(h.error('Invalid millisecond for "util.toDateString(millisecond)"'),""))},escape:function(e){return null==e?"":/[<"'>]|&(?=#[a-zA-Z0-9]+)/g.test(e+="")?e.replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;"):e},unescape:function(e){return null==e&&(e=""),(e+="").replace(/\&amp;/g,"&").replace(/\&lt;/g,"<").replace(/\&gt;/g,">").replace(/\&#39;/g,"'").replace(/\&quot;/g,'"')},openWin:function(e){var t=(e=e||{}).window||window.open(e.url||"",e.target,e.specs);e.url||(t.document.open("text/html","replace"),t.document.write(e.content||""),t.document.close())},toVisibleArea:function(e){var t,n,i,a,o,r,l,s;(e=d.extend({margin:160,duration:200,type:"y"},e)).scrollElem[0]&&e.thisElem[0]&&(t=e.scrollElem,l=e.thisElem,i=(o="y"===e.type)?"top":"left",a=t[n=o?"scrollTop":"scrollLeft"](),o=t[o?"height":"width"](),r=t.offset()[i],s={},(l=l.offset()[i]-r)>o-e.margin||l<e.margin)&&(s[n]=l-o/2+a,t.animate(s,e.duration))},on:function(i,a,e){"object"==typeof i&&(e=a||{},a=i,i=e.attr||"lay-on");var t,n=(e=d.extend({elem:"body",trigger:"click"},"object"==typeof e?e:{trigger:e})).elem=d(e.elem),o="["+i+"]",r="UTIL_ON_DATA";if(n[0])return n.data(r)||n.data(r,{events:{},callbacks:{}}),t=(r=n.data(r)).callbacks,a=r.events[i]=d.extend(!0,r.events[i],a),n.off(e.trigger,o,t[i]),n.on(e.trigger,o,t[i]=function(e){var t=d(this),n=t.attr(i);"function"==typeof a[n]&&a[n].call(this,t,e)}),a}};t.event=t.on,e("util",t)}),layui.define(["jquery","laytpl","lay","util"],function(e){"use strict";function r(){var t=this,e=t.config,n=e.id;return r.that[n]=t,{config:e,reload:function(e){t.reload.call(t,e)},reloadData:function(e){m.reloadData(n,e)},close:function(){t.remove()},open:function(){t.render()}}}function t(e){this.index=++m.index,this.config=u.extend({},this.config,m.config,e),this.init()}var i,n,a,u=layui.$,f=layui.laytpl,p=layui.util,o=layui.hint(),l=layui.device().mobile?"touchstart":"mousedown",s="dropdown",h="layui_"+s+"_index",y="lay-"+s+"-id",m={config:{customName:{id:"id",title:"title",children:"child"}},index:layui[s]?layui[s].index+1e4:0,set:function(e){return this.config=u.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,s,e,t)}},g="layui-dropdown",c="layui-menu-item-up",d="layui-menu-item-down",v="layui-menu-body-title",x="layui-menu-item-group",b="layui-menu-item-parent",w="layui-menu-item-checked",k="layui-menu-item-checked2",C="layui-menu-body-panel",T="layui-menu-body-panel-left",E="layui-dropdown-shade",D="."+x+">."+v;t.prototype.config={trigger:"click",content:"",className:"",style:"",show:!1,isAllowSpread:!0,isSpreadItem:!0,data:[],delay:[200,300],shade:0,accordion:!1,closeOnClick:!1},t.prototype.reload=function(e,t){this.config=u.extend({},this.config,e),this.init(!0,t)},t.prototype.init=function(e,t){var n,i=this,a=i.config,o=u(a.elem);return 1<o.length?(layui.each(o,function(){m.render(u.extend({},a,{elem:this}))}),i):(u.extend(a,lay.options(o[0])),!e&&o[0]&&o.attr(y)?(n=r.getThis(o.attr(y)))?n.reload(a,t):void 0:(a.elem=u(a.elem),a.id="id"in a?a.id:o.attr("id")||i.index,o.attr(y,a.id),a.customName=u.extend({},m.config.customName,a.customName),(a.show||"reloadData"===t&&i.elemView&&u("body").find(i.elemView.get(0)).length)&&i.render(e,t),void i.events()))},t.prototype.render=function(e,t){function n(){var e=u('<ul class="layui-menu layui-dropdown-menu"></ul>');return 0<s.data.length?d(e,s.data):e.html('<li class="layui-menu-item-none">暂无数据</li>'),e}var o=this,s=o.config,c=s.customName,i=u("body"),d=function(l,e){return layui.each(e,function(e,t){var n,i=t[c.children]&&0<t[c.children].length,a=("isSpreadItem"in t?t:s).isSpreadItem,o=(r=p.escape(t[c.title]),r=(o=t.templet||s.templet)?"function"==typeof o?o(t):f(o).render(t):r),r=(i&&(t.type=t.type||"parent"),t.type?{group:"group",parent:"parent","-":"-"}[t.type]||"parent":"");("-"===r||t[c.title]||t[c.id]||i)&&((o=u(["<li"+(n={group:"layui-menu-item-group"+(s.isAllowSpread?a?" layui-menu-item-down":" layui-menu-item-up":""),parent:b,"-":"layui-menu-item-divider"},i||r?' class="'+n[r]+'"':t.disabled?' class="layui-disabled"':"")+">",(n="href"in t?'<a href="'+t.href+'" target="'+(t.target||"_self")+'">'+o+"</a>":o,i?'<div class="'+v+'">'+n+("parent"===r?'<i class="layui-icon layui-icon-right"></i>':"group"===r&&s.isAllowSpread?'<i class="layui-icon layui-icon-'+(a?"up":"down")+'"></i>':"")+"</div>":'<div class="'+v+'">'+n+"</div>"),"</li>"].join(""))).data("item",t),i&&(a=u('<div class="layui-panel layui-menu-body-panel"></div>'),n=u("<ul></ul>"),"parent"===r?(a.append(d(n,t[c.children])),o.append(a)):o.append(d(n,t[c.children]))),l.append(o))}),l},a=['<div class="layui-dropdown layui-border-box layui-panel layui-anim layui-anim-downbit" '+y+'="'+s.id+'">',"</div>"].join("");!(e=!("contextmenu"!==s.trigger&&!lay.isTopElem(s.elem[0]))||e)&&s.elem.data(h+"_opened")||(o.elemView=u("."+g+"["+y+'="'+s.id+'"]'),"reloadData"===t&&o.elemView.length?o.elemView.html(s.content||n()):(o.elemView=u(a),o.elemView.append(s.content||n()),s.className&&o.elemView.addClass(s.className),s.style&&o.elemView.attr("style",s.style),m.thisId=s.id,o.remove(),i.append(o.elemView),s.elem.data(h+"_opened",!0),e=s.shade?'<div class="'+E+'" style="z-index:'+(o.elemView.css("z-index")-1)+"; background-color: "+(s.shade[1]||"#000")+"; opacity: "+(s.shade[0]||s.shade)+'"></div>':"",o.elemView.before(e),"mouseenter"===s.trigger&&o.elemView.on("mouseenter",function(){clearTimeout(r.timer)}).on("mouseleave",function(){o.delayRemove()})),o.position(),(r.prevElem=o.elemView).data("prevElem",s.elem),o.elemView.find(".layui-menu").on(l,function(e){layui.stope(e)}),o.elemView.find(".layui-menu li").on("click",function(e){var t=u(this),n=t.data("item")||{},i=n[c.children]&&0<n[c.children].length,a="all"===s.clickScope;n.disabled||i&&!a||"-"===n.type||(!1===("function"==typeof s.click?s.click(n,t,e):null)||i||o.remove(),layui.stope(e))}),o.elemView.find(D).on("click",function(e){var t=u(this).parent();"group"===(t.data("item")||{}).type&&s.isAllowSpread&&r.spread(t,s.accordion)}),"function"==typeof s.ready&&s.ready(o.elemView,s.elem))},t.prototype.position=function(e){var t=this.config;lay.position(t.elem[0],this.elemView[0],{position:t.position,e:this.e,clickType:"contextmenu"===t.trigger?"right":null,align:t.align||null})},t.prototype.remove=function(){this.config;var e,t,n,i=r.prevElem;i&&(t=i.attr(y),e=i.data("prevElem"),n=(t=r.getThis(t)).config.close,e&&e.data(h+"_opened",!1),i.remove(),delete r.prevElem,"function"==typeof n)&&n.call(t.config,e),lay("."+E).remove()},t.prototype.normalizedDelay=function(){var e=this.config;return{show:(e=[].concat(e.delay))[0],hide:void 0!==e[1]?e[1]:e[0]}},t.prototype.delayRemove=function(){var e=this;e.config,clearTimeout(r.timer),r.timer=setTimeout(function(){e.remove()},e.normalizedDelay().hide)},t.prototype.events=function(){var t=this,n=t.config,i=("hover"===n.trigger&&(n.trigger="mouseenter"),t.prevElem&&t.prevElem.off(n.trigger,t.prevElemCallback),"mouseenter"===n.trigger);t.prevElem=n.elem,t.prevElemCallback=function(e){clearTimeout(r.timer),t.e=e,i?r.timer=setTimeout(function(){t.render()},t.normalizedDelay().show):n.closeOnClick&&n.elem.data(h+"_opened")&&"click"===n.trigger?t.remove():t.render(),e.preventDefault()},n.elem.on(n.trigger,t.prevElemCallback),i&&n.elem.on("mouseleave",function(){t.delayRemove()})},r.that={},r.getThis=function(e){var t=r.that[e];return t||o.error(e?s+" instance with ID '"+e+"' not found":"ID argument required"),t},r.spread=function(e,t){function n(){u(this).css({display:""})}var i=e.children("ul"),a=e.hasClass(c);i.is(":animated")||(a?(e.removeClass(c).addClass(d),i.hide().stop().slideDown(200,n)):(i.stop().slideUp(200,n),e.removeClass(d).addClass(c)),a&&t&&((i=e.siblings("."+d)).children("ul").stop().slideUp(200,n),i.removeClass(d).addClass(c)))},i=u(window),n=u(document),i.on("resize",function(){if(m.thisId){var e=r.getThis(m.thisId);if(e)return!(e.elemView&&!e.elemView[0]||!u("."+g)[0])&&void("contextmenu"===e.config.trigger?e.remove():e.position())}}),lay(n).on(l,function(e){var t,n,i,a;m.thisId&&(t=r.getThis(m.thisId))&&(n=t.config,i=lay.isTopElem(n.elem[0]),a="contextmenu"===n.trigger,i=!(i||a)&&(n.elem[0]===e.target||n.elem.find(e.target)[0]),a=t.elemView&&(e.target===t.elemView[0]||t.elemView.find(e.target)[0]),i||a||("touchstart"===e.type&&n.elem.data(h+"_opened")&&u(e.target).hasClass(E)&&e.preventDefault(),"function"==typeof n.onClickOutside&&!1===n.onClickOutside(e)||t.remove()))},{passive:!1}),n.on("click",a=".layui-menu:not(.layui-dropdown-menu) li",function(e){var t=u(this),n=t.parents(".layui-menu").eq(0),i=t.hasClass(x)||t.hasClass(b),a=n.attr("lay-filter")||n.attr("id"),o=lay.options(this);t.hasClass("layui-menu-item-divider")||i||(n.find("."+w).removeClass(w),n.find("."+k).removeClass(k),t.addClass(w),t.parents("."+b).addClass(k),o.title=o.title||u.trim(t.children("."+v).text()),layui.event.call(this,s,"click("+a+")",o))}),n.on("click",a+D,function(e){var t=(i=u(this)).parents("."+x+":eq(0)"),n=lay.options(t[0]),i="string"==typeof i.parents(".layui-menu").eq(0).attr("lay-accordion");"isAllowSpread"in n&&!n.isAllowSpread||r.spread(t,i)}),n.on("mouseenter",a=".layui-menu ."+b,function(e){var t,n=u(this).find("."+C);n[0]&&((t=n[0].getBoundingClientRect()).right>i.width()&&(n.addClass(T),(t=n[0].getBoundingClientRect()).left<0)&&n.removeClass(T),t.bottom>i.height())&&n.eq(0).css("margin-top",-(t.bottom-i.height()+5))}).on("mouseleave",a,function(e){var t=u(this).children("."+C);t.removeClass(T),t.css("margin-top",0)}),m.close=function(e){return(e=r.getThis(e))?(e.remove(),r.call(e)):this},m.open=function(e){return(e=r.getThis(e))?(e.render(),r.call(e)):this},m.reload=function(e,t,n){return(e=r.getThis(e))?(e.reload(t,n),r.call(e)):this},m.reloadData=function(){var n=u.extend([],arguments),i=(n[2]="reloadData",new RegExp("^("+["data","templet","content"].join("|")+")$"));return layui.each(n[1],function(e,t){i.test(e)||delete n[1][e]}),m.reload.apply(null,n)},m.render=function(e){return e=new t(e),r.call(e)},e(s,m)}),layui.define(["jquery","lay"],function(e){"use strict";function t(e){this.index=++h.index,this.config=x.extend({},this.config,h.config,e),this.render()}var x=layui.$,b=layui.lay,h={config:{},index:layui.slider?layui.slider.index+1e4:0,set:function(e){return this.config=x.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,n,e,t)}},n="slider",y="layui-disabled",w="layui-slider-bar",k="layui-slider-wrap",C="layui-slider-wrap-btn",T="layui-slider-tips",E="layui-slider-input-txt",D="layui-slider-hover";t.prototype.config={type:"default",min:0,max:100,value:0,step:1,showstep:!1,tips:!0,tipsAlways:!1,input:!1,range:!1,height:200,disabled:!1,theme:"#16baaa"},t.prototype.precision=function(){var e=this.config,e=x.map([e.min,e.max,e.step],function(e,t){return(e=String(e).split("."))[1]?e[1].length:0});return Math.max.apply(null,e)},t.prototype.render=function(){var i=this,a=i.config;if(1<(e=x(a.elem)).length)return layui.each(e,function(){h.render(x.extend({},a,{elem:this}))}),i;x.extend(a,b.options(e[0])),a.step<=0&&(a.step=1),a.max<a.min&&(a.max=a.min+a.step),a.range?(a.value="object"==typeof a.value?a.value:[a.min,a.value],e=Math.min(a.value[0],a.value[1]),n=Math.max(a.value[0],a.value[1]),a.value[0]=Math.max(e,a.min),a.value[1]=Math.max(n,a.min),a.value[0]=Math.min(a.value[0],a.max),a.value[1]=Math.min(a.value[1],a.max),n=(a.value[0]-a.min)/(a.max-a.min)*100,o=(r=(a.value[1]-a.min)/(a.max-a.min)*100)-n+"%",n+="%",r+="%"):("object"==typeof a.value&&(a.value=Math.min.apply(null,a.value)),a.value<a.min&&(a.value=a.min),a.value>a.max&&(a.value=a.max),o=(a.value-a.min)/(a.max-a.min)*100+"%");var t,e=a.disabled?"#c2c2c2":a.theme,n='<div class="layui-slider '+("vertical"===a.type?"layui-slider-vertical":"")+'">'+(a.tips?'<div class="'+T+'" '+(a.tipsAlways?"":'style="display:none;"')+"></div>":"")+'<div class="layui-slider-bar" style="background:'+e+"; "+("vertical"===a.type?"height":"width")+":"+o+";"+("vertical"===a.type?"bottom":"left")+":"+(n||0)+';"></div><div class="layui-slider-wrap" style="'+("vertical"===a.type?"bottom":"left")+":"+(n||o)+';"><div class="layui-slider-wrap-btn" style="border: 2px solid '+e+';"></div></div>'+(a.range?'<div class="layui-slider-wrap" style="'+("vertical"===a.type?"bottom":"left")+":"+r+';"><div class="layui-slider-wrap-btn" style="border: 2px solid '+e+';"></div></div>':"")+"</div>",o=x(a.elem),r=o.next(".layui-slider");if(r[0]&&r.remove(),i.elemTemp=x(n),a.range?(i.elemTemp.find("."+k).eq(0).data("value",a.value[0]),i.elemTemp.find("."+k).eq(1).data("value",a.value[1])):i.elemTemp.find("."+k).data("value",a.value),o.html(i.elemTemp),"vertical"===a.type&&i.elemTemp.height(a.height+"px"),a.showstep){for(var l=(a.max-a.min)/a.step,s="",c=1;c<1+l;c++){var d=100*c/l;d<100&&(s+='<div class="layui-slider-step" style="'+("vertical"===a.type?"bottom":"left")+":"+d+'%"></div>')}i.elemTemp.append(s)}function u(e){e=e.parent().data("value"),e=a.setTips?a.setTips(e):e,i.elemTemp.find("."+T).html(e)}function f(e){var t="vertical"===a.type?a.height:i.elemTemp[0].offsetWidth,n=i.elemTemp.find("."+k);return("vertical"===a.type?t-e.parent()[0].offsetTop-n.height():e.parent()[0].offsetLeft)/t*100}function p(e){"vertical"===a.type?i.elemTemp.find("."+T).css({bottom:e+"%","margin-bottom":"20px",display:"inline-block"}):i.elemTemp.find("."+T).css({left:e+"%",display:"inline-block"})}a.input&&!a.range&&(e=x('<div class="layui-slider-input"><div class="layui-slider-input-txt"><input type="text" class="layui-input"></div><div class="layui-slider-input-btn"><i class="layui-icon layui-icon-up"></i><i class="layui-icon layui-icon-down"></i></div></div>'),o.css("position","relative"),o.append(e),o.find("."+E).children("input").val(a.value),"vertical"===a.type?e.css({left:0,top:-48}):i.elemTemp.css("margin-right",e.outerWidth()+15)),a.disabled?(i.elemTemp.addClass(y),i.elemTemp.find("."+C).addClass(y)):i.slide(),a.tips&&(a.tipsAlways?(u(r=i.elemTemp.find("."+C)),p(f(r))):i.elemTemp.find("."+C).on("mouseover",function(){u(x(this));var e=f(x(this));clearTimeout(t),t=setTimeout(function(){p(e)},300)}).on("mouseout",function(){clearTimeout(t),a.tipsAlways||i.elemTemp.find("."+T).css("display","none")}))},t.prototype.slide=function(e,t,n){var c=this,d=c.config,u=c.elemTemp,f=function(){return"vertical"===d.type?d.height:u[0].offsetWidth},p=u.find("."+k),l=u.next(".layui-slider-input"),s=l.children("."+E).children("input").val(),h=100/((d.max-d.min)/d.step),y=c.precision(),m=function(e,t,n){e=(e=100<(e=100<Math.ceil(e)*h?Math.ceil(e)*h:Math.round(e)*h)?100:e)<0?0:e,p.eq(t).css("vertical"===d.type?"bottom":"left",e+"%");var i,a=g(p[0].offsetLeft),o=d.range?g(p[1].offsetLeft):0,r=("vertical"===d.type?(u.find("."+T).css({bottom:e+"%","margin-bottom":"20px"}),a=g(f()-p[0].offsetTop-p.height()),o=d.range?g(f()-p[1].offsetTop-p.height()):0):u.find("."+T).css("left",e+"%"),a=100<a?100:a,o=100<o?100:o,Math.min(a,o)),a=Math.abs(a-o),o=("vertical"===d.type?u.find("."+w).css({height:a+"%",bottom:r+"%"}):u.find("."+w).css({width:a+"%",left:r+"%"}),d.min+(d.max-d.min)*e/100),o=Number(parseFloat(o).toFixed(y));s=o,l.children("."+E).children("input").val(s),p.eq(t).data("value",o),u.find("."+T).html(d.setTips?d.setTips(o):o),d.range&&(i=[p.eq(0).data("value"),p.eq(1).data("value")])[0]>i[1]&&i.reverse(),c.value=d.range?i:o,d.change&&d.change(c.value),"done"===n&&d.done&&d.done(c.value)},g=function(e){var t=e/f()*100/h,n=Math.round(t)*h;return e==f()?Math.ceil(t)*h:n},v=x(['<div class="layui-auxiliar-moving" id="LAY-slider-moving"></div'].join(""));if("set"===e)return m((t-d.min)/(d.max-d.min)*100/h,n,"done");u.find("."+C).each(function(l){var s=x(this);s.on("mousedown touchstart",function(e){"touchstart"===(e=e||window.event).type&&(e.clientX=e.originalEvent.touches[0].clientX,e.clientY=e.originalEvent.touches[0].clientY);var t,n,i,a,o=s.parent()[0].offsetLeft,r=e.clientX;"vertical"===d.type&&(o=f()-s.parent()[0].offsetTop-p.height(),r=e.clientY),t=s,n=function(e){"touchmove"===(e=e||window.event).type&&(e.clientX=e.touches[0].clientX,e.clientY=e.touches[0].clientY);var t=(t=(t=(t=o+("vertical"===d.type?r-e.clientY:e.clientX-r))<0?0:t)>f()?f():t)/f()*100/h;m(t,l),s.addClass(D),u.find("."+T).show(),e.preventDefault()},i=function(e){s.removeClass(D),d.tipsAlways||setTimeout(function(){u.find("."+T).hide()},e)},a=function(){i(b.touchEventsSupported()?1e3:0),v.remove(),d.done&&d.done(c.value),b.touchEventsSupported()&&(t[0].removeEventListener("touchmove",n,!!b.passiveSupported&&{passive:!1}),t[0].removeEventListener("touchend",a),t[0].removeEventListener("touchcancel",a))},x("#LAY-slider-moving")[0]||x("body").append(v),v.on("mousemove",n),v.on("mouseup",a).on("mouseleave",a),b.touchEventsSupported()&&(t[0].addEventListener("touchmove",n,!!b.passiveSupported&&{passive:!1}),t[0].addEventListener("touchend",a),t[0].addEventListener("touchcancel",a))})}),u.on("click",function(e){var t=x("."+C),n=x(this);!t.is(event.target)&&0===t.has(event.target).length&&t.length&&(n=(t=(t=(t="vertical"===d.type?f()-e.clientY+n.offset().top-x(window).scrollTop():e.clientX-n.offset().left-x(window).scrollLeft())<0?0:t)>f()?f():t)/f()*100/h,t=d.range?"vertical"===d.type?Math.abs(t-parseInt(x(p[0]).css("bottom")))>Math.abs(t-parseInt(x(p[1]).css("bottom")))?1:0:Math.abs(t-p[0].offsetLeft)>Math.abs(t-p[1].offsetLeft)?1:0:0,m(n,t,"done"),e.preventDefault())}),l.children(".layui-slider-input-btn").children("i").each(function(t){x(this).on("click",function(){s=l.children("."+E).children("input").val();var e=((s=1==t?s-d.step<d.min?d.min:Number(s)-d.step:Number(s)+d.step>d.max?d.max:Number(s)+d.step)-d.min)/(d.max-d.min)*100/h;m(e,0,"done")})});function i(){var e=this.value,e=(e=(e=(e=isNaN(e)?0:e)<d.min?d.min:e)>d.max?d.max:e,((this.value=e)-d.min)/(d.max-d.min)*100/h);m(e,0,"done")}l.children("."+E).children("input").on("keydown",function(e){13===e.keyCode&&(e.preventDefault(),i.call(this))}).on("change",i)},t.prototype.events=function(){this.config},h.render=function(e){return e=new t(e),function(){var n=this,i=n.config;return{setValue:function(e,t){return e=(e=e>i.max?i.max:e)<i.min?i.min:e,i.value=e,n.slide("set",e,t||0)},config:i}}.call(e)},e(n,h)}),layui.define(["jquery","lay"],function(e){"use strict";function a(){var e=this.config,t=e.id;return a.that[t]=this,{config:e}}function w(e){var t={h:0,s:0,b:0},n=Math.min(e.r,e.g,e.b),i=Math.max(e.r,e.g,e.b),a=i-n;return t.b=i,t.s=0!==i?255*a/i:0,0!==t.s?e.r==i?t.h=(e.g-e.b)/a:e.g==i?t.h=2+(e.b-e.r)/a:t.h=4+(e.r-e.g)/a:t.h=-1,i===n&&(t.h=0),t.h*=60,t.h<0&&(t.h+=360),t.s*=100/255,t.b*=100/255,t}function k(e){var t,n={},i=e.h,a=255*e.s/100,e=255*e.b/100;return 0==a?n.r=n.g=n.b=e:(e=i%60*((t=e)-(a=(255-a)*e/255))/60,(i=360===i?0:i)<60?(n.r=t,n.b=a,n.g=a+e):i<120?(n.g=t,n.b=a,n.r=t-e):i<180?(n.g=t,n.r=a,n.b=a+e):i<240?(n.b=t,n.r=a,n.g=t-e):i<300?(n.b=t,n.g=a,n.r=a+e):i<360?(n.r=t,n.g=a,n.b=t-e):(n.r=0,n.g=0,n.b=0)),{r:Math.round(n.r),g:Math.round(n.g),b:Math.round(n.b)}}function C(e){var n=[(e=k(e)).r.toString(16),e.g.toString(16),e.b.toString(16)];return E.each(n,function(e,t){1===t.length&&(n[e]="0"+t)}),n.join("")}function T(e){return{r:(e=e.match(/[0-9]{1,3}/g)||[])[0],g:e[1],b:e[2]}}function o(e){this.index=++r.index,this.config=E.extend({},this.config,r.config,e),this.render()}var E=layui.$,D=layui.lay,n=layui.hint(),t=layui.device().mobile?"click":"mousedown",r={config:{},index:layui.colorpicker?layui.colorpicker.index+1e4:0,set:function(e){return this.config=E.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,"colorpicker",e,t)}},i="colorpicker",l="layui-colorpicker",s=".layui-colorpicker-main",S="layui-icon-down",N="layui-icon-close",L="layui-colorpicker-trigger-span",A="layui-colorpicker-trigger-i",I="layui-colorpicker-side-slider",M="layui-colorpicker-basis",j="layui-colorpicker-alpha-bgcolor",_="layui-colorpicker-alpha-slider",H="layui-colorpicker-basis-cursor",F="layui-colorpicker-main-input",O=E(window),c=E(document);o.prototype.config={color:"",size:null,alpha:!1,format:"hex",predefine:!1,colors:["#16baaa","#16b777","#1E9FFF","#FF5722","#FFB800","#01AAED","#999","#c00","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585","rgb(0, 186, 189)","rgb(255, 120, 0)","rgb(250, 212, 0)","#393D49","rgba(0,0,0,.5)","rgba(255, 69, 0, 0.68)","rgba(144, 240, 144, 0.5)","rgba(31, 147, 255, 0.73)"]},o.prototype.render=function(){var e=this,t=e.config;if(1<(i=E(t.elem)).length)return layui.each(i,function(){r.render(E.extend({},t,{elem:this}))}),e;E.extend(t,D.options(i[0]));var n=E(['<div class="layui-unselect layui-colorpicker">',"<span "+("rgb"==t.format&&t.alpha?'class="layui-colorpicker-trigger-bgcolor"':"")+">",'<span class="layui-colorpicker-trigger-span" ','lay-type="'+("rgb"==t.format?t.alpha?"rgba":"torgb":"")+'" ','style="'+(n="",t.color?(n=t.color,3<(t.color.match(/[0-9]{1,3}/g)||[]).length&&(t.alpha&&"rgb"==t.format||(n="#"+C(w(T(t.color))))),"background: "+n):n)+'">','<i class="layui-icon layui-colorpicker-trigger-i '+(t.color?S:N)+'"></i>',"</span>","</span>","</div>"].join("")),i=t.elem=E(t.elem);t.size&&n.addClass("layui-colorpicker-"+t.size),i.addClass("layui-inline").html(e.elemColorBox=n),t.id="id"in t?t.id:i.attr("id")||e.index,e.color=e.elemColorBox.find("."+L)[0].style.background,e.events()},o.prototype.renderPicker=function(){var n,e=this,t=e.config,i=e.elemColorBox[0],a=e.elemPicker=E(['<div id="layui-colorpicker'+e.index+'" data-index="'+e.index+'" class="layui-anim layui-anim-downbit layui-colorpicker-main">','<div class="layui-colorpicker-main-wrapper">','<div class="layui-colorpicker-basis">','<div class="layui-colorpicker-basis-white"></div>','<div class="layui-colorpicker-basis-black"></div>','<div class="layui-colorpicker-basis-cursor"></div>',"</div>",'<div class="layui-colorpicker-side">','<div class="layui-colorpicker-side-slider"></div>',"</div>","</div>",'<div class="layui-colorpicker-main-alpha '+(t.alpha?"layui-show":"")+'">','<div class="layui-colorpicker-alpha-bgcolor">','<div class="layui-colorpicker-alpha-slider"></div>',"</div>","</div>",t.predefine?(n=['<div class="layui-colorpicker-main-pre">'],layui.each(t.colors,function(e,t){n.push(['<div class="layui-colorpicker-pre'+(3<(t.match(/[0-9]{1,3}/g)||[]).length?" layui-colorpicker-pre-isalpha":"")+'">','<div style="background:'+t+'"></div>',"</div>"].join(""))}),n.push("</div>"),n.join("")):"",'<div class="layui-colorpicker-main-input">','<div class="layui-inline">','<input type="text" class="layui-input">',"</div>",'<div class="layui-btn-container">','<button class="layui-btn layui-btn-primary layui-btn-sm" colorpicker-events="clear">清空</button>','<button class="layui-btn layui-btn-sm" colorpicker-events="confirm">确定</button>',"</div","</div>","</div>"].join(""));e.elemColorBox.find("."+L)[0],E(s)[0]&&E(s).data("index")==e.index?e.removePicker(o.thisElemInd):(e.removePicker(o.thisElemInd),E("body").append(a)),r.thisId=t.id,o.thisElemInd=e.index,o.thisColor=i.style.background,e.position(),e.pickerEvents()},o.prototype.removePicker=function(e){var t=this.config;return(e=E("#layui-colorpicker"+(e||this.index)))[0]&&(e.remove(),delete r.thisId,"function"==typeof t.close)&&t.close(this.color),this},o.prototype.position=function(){var e=this,t=e.config;return D.position(e.bindElem||e.elemColorBox[0],e.elemPicker[0],{position:t.position,align:"center"}),e},o.prototype.val=function(){var e,t=this,n=(t.config,t.elemColorBox.find("."+L)),i=t.elemPicker.find("."+F),a=n[0].style.backgroundColor;a?(e=w(T(a)),n=n.attr("lay-type"),t.select(e.h,e.s,e.b),"torgb"===n?i.find("input").val(a):"rgba"===n?(n=T(a),3===(a.match(/[0-9]{1,3}/g)||[]).length?(i.find("input").val("rgba("+n.r+", "+n.g+", "+n.b+", 1)"),t.elemPicker.find("."+_).css("left",280)):(i.find("input").val(a),a=280*a.slice(a.lastIndexOf(",")+1,a.length-1),t.elemPicker.find("."+_).css("left",a)),t.elemPicker.find("."+j)[0].style.background="linear-gradient(to right, rgba("+n.r+", "+n.g+", "+n.b+", 0), rgb("+n.r+", "+n.g+", "+n.b+"))"):i.find("input").val("#"+C(e))):(t.select(0,100,100),i.find("input").val(""),t.elemPicker.find("."+j)[0].style.background="",t.elemPicker.find("."+_).css("left",280))},o.prototype.side=function(){function c(e,t,n,i){o.select(e,t,n);var a=k({h:e,s:t,b:n}),e=C({h:e,s:t,b:n}),t=o.elemPicker.find("."+F).find("input");v.addClass(S).removeClass(N),l[0].style.background="rgb("+a.r+", "+a.g+", "+a.b+")","torgb"===s?t.val("rgb("+a.r+", "+a.g+", "+a.b+")"):"rgba"===s?(p.css("left",280*i),t.val("rgba("+a.r+", "+a.g+", "+a.b+", "+i+")"),l[0].style.background="rgba("+a.r+", "+a.g+", "+a.b+", "+i+")",f[0].style.background="linear-gradient(to right, rgba("+a.r+", "+a.g+", "+a.b+", 0), rgb("+a.r+", "+a.g+", "+a.b+"))"):t.val("#"+e),r.change&&r.change(E.trim(o.elemPicker.find("."+F).find("input").val()))}function n(e){E("#LAY-colorpicker-moving")[0]||E("body").append(t),t.on("mousemove",e),t.on("mouseup",function(){t.remove()}).on("mouseleave",function(){t.remove()})}var o=this,r=o.config,l=o.elemColorBox.find("."+L),s=l.attr("lay-type"),d=o.elemPicker.find(".layui-colorpicker-side"),i=o.elemPicker.find("."+I),u=o.elemPicker.find("."+M),a=o.elemPicker.find("."+H),f=o.elemPicker.find("."+j),p=o.elemPicker.find("."+_),h=i[0].offsetTop/180*360,y=100-(a[0].offsetTop+3)/180*100,m=(a[0].offsetLeft+3)/260*100,g=Math.round(p[0].offsetLeft/280*100)/100,v=o.elemColorBox.find("."+A),e=o.elemPicker.find(".layui-colorpicker-pre").children("div"),t=E(['<div class="layui-auxiliar-moving" id="LAY-colorpicker-moving"></div>'].join("")),x=!0,b=!0;i.on("mousedown",function(e,t){var i=this.offsetTop,a=(void 0===e.clientY?t:e).clientY;b&&layui.stope(e),n(function(e){var t=i+(e.clientY-a),n=(t=(n=d[0].offsetHeight)<(t=t<0?0:t)?n:t)/180*360;c(h=n,m,y,g),e.preventDefault()}),e.preventDefault()}),d.on("mousedown",function(e){var t=((t=(t=e.clientY-E(this).offset().top+O.scrollTop())<0?0:t)>this.offsetHeight?this.offsetHeight:t)/180*360;c(h=t,m,y,g),e.preventDefault(),x&&i.trigger("mousedown",e)}),a.on("mousedown",function(e,t){var o=this.offsetTop,r=this.offsetLeft,l=(void 0===e.clientY?t:e).clientY,s=(void 0===e.clientX?t:e).clientX;b&&layui.stope(e),n(function(e){var t=o+(e.clientY-l),n=r+(e.clientX-s),i=u[0].offsetHeight,a=(n=(a=u[0].offsetWidth)<(n=n<0?0:n)?a:n)/260*100,n=100-(t=i<(t=t<0?0:t)?i:t)/180*100;c(h,m=a,y=n,g),e.preventDefault()}),e.preventDefault()}),u.on("mousedown",function(e){var t=e.clientY-E(this).offset().top+O.scrollTop(),n=e.clientX-E(this).offset().left+O.scrollLeft(),n=((t=t<0?0:t)>this.offsetHeight&&(t=this.offsetHeight),((n=n<0?0:n)>this.offsetWidth?this.offsetWidth:n)/260*100);c(h,m=n,y=t=100-t/180*100,g),layui.stope(e),e.preventDefault(),x&&a.trigger("mousedown",e)}),p.on("mousedown",function(e,t){var i=this.offsetLeft,a=(void 0===e.clientX?t:e).clientX;b&&layui.stope(e),n(function(e){var t=i+(e.clientX-a),n=((n=f[0].offsetWidth)<(t=t<0?0:t)&&(t=n),Math.round(t/280*100)/100);c(h,m,y,g=n),e.preventDefault()}),e.preventDefault()}),f.on("mousedown",function(e){(t=(t=e.clientX-E(this).offset().left)<0?0:t)>this.offsetWidth&&(t=this.offsetWidth);var t=Math.round(t/280*100)/100;c(h,m,y,g=t),e.preventDefault(),x&&p.trigger("mousedown",e)}),e.each(function(){E(this).on("click",function(){E(this).parent(".layui-colorpicker-pre").addClass("selected").siblings().removeClass("selected");var e=this.style.backgroundColor,t=w(T(e)),n=e.slice(e.lastIndexOf(",")+1,e.length-1);h=t.h,m=t.s,y=t.b,3===(e.match(/[0-9]{1,3}/g)||[]).length&&(n=1),g=n,c(t.h,t.s,t.b,n)})}),D.touchEventsSupported()&&layui.each([{elem:d,eventType:"mousedown"},{elem:f,eventType:"mousedown"},{elem:u,eventType:"mousedown"}],function(e,i){D.touchSwipe(i.elem,{onTouchStart:function(){b=x=!1},onTouchMove:function(e){var t,n=i.eventType;e=e.touches[0],(t=document.createEvent("MouseEvent")).initMouseEvent(n,!0,!0,window,1,e.screenX,e.screenY,e.clientX,e.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)},onTouchEnd:function(){t.remove(),b=x=!0}})})},o.prototype.select=function(e,t,n,i){this.config;var a=C({h:e,s:100,b:100}),e=(C({h:e,s:t,b:n}),e/360*180),n=180-n/100*180,t=t/100*260,o=this.elemPicker.find("."+M)[0];this.elemPicker.find("."+I).css("top",e),o.style.background="#"+a,this.elemPicker.find("."+H).css({top:n/o.offsetHeight*100+"%",left:t/o.offsetWidth*100+"%"})},o.prototype.pickerEvents=function(){var l=this,s=l.config,c=l.elemColorBox.find("."+L),d=l.elemPicker.find("."+F+" input"),n={clear:function(e){c[0].style.background="",l.elemColorBox.find("."+A).removeClass(S).addClass(N),l.color="",s.done&&s.done(""),l.removePicker()},confirm:function(e,t){var n,i,a,o,r=E.trim(d.val());-1<r.indexOf(",")?(i=w(T(r)),l.select(i.h,i.s,i.b),c[0].style.background=n="#"+C(i),3<(r.match(/[0-9]{1,3}/g)||[]).length&&"rgba"===c.attr("lay-type")&&(a=280*r.slice(r.lastIndexOf(",")+1,r.length-1),l.elemPicker.find("."+_).css("left",a),n=c[0].style.background=r)):(3===(a=-1<(a=r).indexOf("#")?a.substring(1):a).length&&(a=(o=a.split(""))[0]+o[0]+o[1]+o[1]+o[2]+o[2]),o={r:(a=parseInt(a,16))>>16,g:(65280&a)>>8,b:255&a},i=w(o),c[0].style.background=n="#"+C(i),l.elemColorBox.find("."+A).removeClass(N).addClass(S)),"change"===t?(l.select(i.h,i.s,i.b,t),s.change&&s.change(n)):(l.color=r,s.done&&s.done(r),l.removePicker())}};l.elemPicker.on("click","*[colorpicker-events]",function(){var e=E(this),t=e.attr("colorpicker-events");n[t]&&n[t].call(this,e)}),d.on("keyup",function(e){var t=E(this);n.confirm.call(this,t,13===e.keyCode?null:"change")})},o.prototype.events=function(){var e=this;e.config,e.elemColorBox.on("click",function(){e.renderPicker(),E(s)[0]&&(e.val(),e.side())})},c.on(t,function(e){var t,n,i;r.thisId&&(t=a.getThis(r.thisId))&&(n=t.config,i=t.elemColorBox.find("."+L),E(e.target).hasClass(l)||E(e.target).parents("."+l)[0]||E(e.target).hasClass(s.replace(/\./g,""))||E(e.target).parents(s)[0]||t.elemPicker&&(t.color?(e=w(T(t.color)),t.select(e.h,e.s,e.b)):t.elemColorBox.find("."+A).removeClass(S).addClass(N),i[0].style.background=t.color||"","function"==typeof n.cancel&&n.cancel(t.color),t.removePicker()))}),O.on("resize",function(){if(r.thisId){var e=a.getThis(r.thisId);if(e)return!(!e.elemPicker||!E(s)[0])&&void e.position()}}),a.that={},a.getThis=function(e){var t=a.that[e];return t||n.error(e?i+" instance with ID '"+e+"' not found":"ID argument required"),t},r.render=function(e){return e=new o(e),a.call(e)},e(i,r)}),layui.define("jquery",function(e){"use strict";function t(){this.config={}}var d=layui.$,u=(layui.hint(),layui.device()),c="element",f="layui-this",p="layui-show",l=".layui-tab-title",h=(t.prototype.set=function(e){return d.extend(!0,this.config,e),this},t.prototype.on=function(e,t){return layui.onevent.call(this,c,e,t)},t.prototype.tabAdd=function(e,t){var n,i=(o=d(".layui-tab[lay-filter="+e+"]")).children(l),a=i.children(".layui-tab-bar"),o=o.children(".layui-tab-content"),r="<li"+(n=[],layui.each(t,function(e,t){/^(title|content)$/.test(e)||n.push("lay-"+e+'="'+t+'"')}),0<n.length&&n.unshift(""),n.join(" "))+">"+(t.title||"unnaming")+"</li>";return a[0]?a.before(r):i.append(r),o.append('<div class="layui-tab-item" '+(t.id?'lay-id="'+t.id+'"':"")+">"+(t.content||"")+"</div>"),t.change&&this.tabChange(e,t.id),i.data("LAY_TAB_CHANGE",t.change),w.tabAuto(t.change?"change":null),this},t.prototype.tabDelete=function(e,t){return e=d(".layui-tab[lay-filter="+e+"]").children(l).find('>li[lay-id="'+t+'"]'),w.tabDelete(null,e),this},t.prototype.tabChange=function(e,t,n){return e=d(".layui-tab[lay-filter="+e+"]").children(l).find('>li[lay-id="'+t+'"]'),w.tabClick.call(e[0],{liElem:e,force:n}),this},t.prototype.tab=function(n){n=n||{},i.on("click",n.headerElem,function(e){var t=d(n.headerElem).index(d(this));w.tabClick.call(this,{index:t,options:n})})},t.prototype.progress=function(e,t){var n=(e=d("."+(n="layui-progress")+"[lay-filter="+e+"]").find("."+n+"-bar")).find("."+n+"-text");return e.css("width",function(){return/^.+\/.+$/.test(t)?100*new Function("return "+t)()+"%":t}).attr("lay-percent",t),n.text(t),this},".layui-nav"),y="layui-nav-item",m="layui-nav-bar",g="layui-nav-tree",v="layui-nav-child",x="layui-nav-more",b="layui-anim layui-anim-upbit",w={tabClick:function(e){var t=(e=e||{}).options||{},n=e.liElem||d(this),i=t.headerElem?n.parent():n.parents(".layui-tab").eq(0),t=t.bodyElem?d(t.bodyElem):i.children(".layui-tab-content").children(".layui-tab-item"),a="javascript:;"!==(a=n.find("a")).attr("href")&&"_blank"===a.attr("target"),o="string"==typeof n.attr("lay-unselect"),r=i.attr("lay-filter"),l=n.attr("lay-id"),s="index"in e?e.index:n.parent().children("li").index(n);if(!e.force){e=n.siblings("."+f);if(!1===layui.event.call(this,c,"tabBeforeChange("+r+")",{elem:i,from:{index:n.parent().children("li").index(e),id:e.attr("lay-id")},to:{index:s,id:l}}))return}a||o||(n.addClass(f).siblings().removeClass(f),(l?e=(e=t.filter('[lay-id="'+l+'"]')).length?e:t.eq(s):t.eq(s)).addClass(p).siblings().removeClass(p)),layui.event.call(this,c,"tab("+r+")",{elem:i,index:s,id:l})},tabDelete:function(e,t){var n=(t=t||d(this).parent()).parent().children("li").index(t),i=t.closest(".layui-tab"),a=i.children(".layui-tab-content").children(".layui-tab-item"),o=i.attr("lay-filter"),r=t.attr("lay-id");!1!==layui.event.call(t[0],c,"tabBeforeDelete("+o+")",{elem:i,index:n,id:r})&&(t.hasClass(f)&&(t.next()[0]&&t.next().is("li")?w.tabClick.call(t.next()[0],{index:n+1}):t.prev()[0]&&t.prev().is("li")&&w.tabClick.call(t.prev()[0],null,n-1)),t.remove(),(r?t=(t=a.filter('[lay-id="'+r+'"]')).length?t:a.eq(n):a.eq(n)).remove(),setTimeout(function(){w.tabAuto()},50),layui.event.call(this,c,"tabDelete("+o+")",{elem:i,index:n,id:r}))},tabAuto:function(a,e){var o="layui-tab-more",r="layui-tab-bar",l="layui-tab-close",s=this;(e||d(".layui-tab")).each(function(){var e=d(this),n=e.children(".layui-tab-title");e.children(".layui-tab-content").children(".layui-tab-item");var t=d('<span class="layui-unselect layui-tab-bar" '+(t='lay-stope="tabmore"')+"><i "+t+' class="layui-icon">&#xe61a;</i></span>'),i=(s===window&&u.ie,e.attr("lay-allowclose"));i&&"false"!==i&&n.find("li").each(function(){var e,t=d(this);t.find("."+l)[0]||"false"===t.attr("lay-allowclose")||((e=d('<i class="layui-icon layui-icon-close layui-unselect '+l+'"></i>')).on("click",w.tabDelete),t.append(e))}),"string"!=typeof e.attr("lay-unauto")&&(n.prop("scrollWidth")>n.outerWidth()+1||n.find("li").length&&n.height()>(i=n.find("li").eq(0).height())+i/2?("change"===a&&n.data("LAY_TAB_CHANGE")&&n.addClass(o),n.find("."+r)[0]||(n.append(t),e.attr("overflow",""),t.on("click",function(e){var t=n.hasClass(o);n[t?"removeClass":"addClass"](o)}))):(n.find("."+r).remove(),e.removeAttr("overflow")))})},hideTabMore:function(e){var t=d(".layui-tab-title");!0!==e&&"tabmore"===d(e.target).attr("lay-stope")||(t.removeClass("layui-tab-more"),t.find(".layui-tab-bar").attr("title",""))},clickThis:function(){var e=d(this),t=e.closest(h),n=t.attr("lay-filter"),i=e.parent(),a=e.siblings("."+v),o="string"==typeof i.attr("lay-unselect");if("javascript:;"!==e.attr("href")&&"_blank"===e.attr("target")||o||a[0]||(t.find("."+f).removeClass(f),i.addClass(f)),t.hasClass(g)){var o=y+"ed",r=!i.hasClass(o),l=function(){d(this).css({display:""}),t.children("."+m).css({opacity:0})};if(a.is(":animated"))return;a.removeClass(b),a[0]&&(r?(a.slideDown(200,l),i.addClass(o)):(i.removeClass(o),a.show().slideUp(200,l)),"string"!=typeof t.attr("lay-accordion")&&"all"!==t.attr("lay-shrink")||((r=i.siblings("."+o)).removeClass(o),r.children("."+v).show().stop().slideUp(200,l)))}layui.event.call(this,c,"nav("+n+")",e)},collapse:function(){var e=d(this),t=e.find(".layui-colla-icon"),n=e.siblings(".layui-colla-content"),i=e.parents(".layui-collapse").eq(0),a=i.attr("lay-filter"),o="none"===n.css("display");"string"==typeof i.attr("lay-accordion")&&((i=i.children(".layui-colla-item").children("."+p)).siblings(".layui-colla-title").children(".layui-colla-icon").html("&#xe602;"),i.removeClass(p)),n[o?"addClass":"removeClass"](p),t.html(o?"&#xe61a;":"&#xe602;"),layui.event.call(this,c,"collapse("+a+")",{title:e,content:n,show:o})}},n=(t.prototype.render=t.prototype.init=function(e,t){var n="string"==typeof t&&t?'[lay-filter="'+t+'"]':"",i={tab:function(e){w.tabAuto.call({},e)},nav:function(e){var r={},l={},s={},c="layui-nav-title";(e||d(h+n)).each(function(e){var t=d(this),n=d('<span class="'+m+'"></span>'),i=t.find("."+y),a=t.find("."+m)[0];a&&a.remove(),t.append(n),(t.hasClass(g)?i.find("dd,>."+c):i).off("mouseenter.lay_nav").on("mouseenter.lay_nav",function(){!function(e,t,n){var i,a=d(this),o=a.find("."+v);t.hasClass(g)?o[0]||(i=a.children("."+c),e.css({top:a.offset().top-t.offset().top,height:(i[0]?i:a).outerHeight(),opacity:1})):(o.addClass(b),o.hasClass("layui-nav-child-c")&&o.css({left:-(o.outerWidth()-a.width())/2}),o[0]?e.css({left:e.position().left+e.width()/2,width:0,opacity:0}):e.css({left:a.position().left+parseFloat(a.css("marginLeft")),top:a.position().top+a.height()-e.height()}),r[n]=setTimeout(function(){e.css({width:o[0]?0:a.width(),opacity:o[0]?0:1})},u.ie&&u.ie<10?0:200),clearTimeout(s[n]),"block"===o.css("display")&&clearTimeout(l[n]),l[n]=setTimeout(function(){o.addClass(p),a.find("."+x).addClass(x+"d")},300))}.call(this,n,t,e)}).off("mouseleave.lay_nav").on("mouseleave.lay_nav",function(){t.hasClass(g)?n.css({height:0,opacity:0}):(clearTimeout(l[e]),l[e]=setTimeout(function(){t.find("."+v).removeClass(p),t.find("."+x).removeClass(x+"d")},300))}),t.off("mouseleave.lay_nav").on("mouseleave.lay_nav",function(){clearTimeout(r[e]),s[e]=setTimeout(function(){t.hasClass(g)||n.css({width:0,left:n.position().left+n.width()/2,opacity:0})},200)}),i.find("a").each(function(){var e=d(this);e.parent(),e.siblings("."+v)[0]&&!e.children("."+x)[0]&&e.append('<i class="layui-icon layui-icon-down '+x+'"></i>'),e.off("click",w.clickThis).on("click",w.clickThis)})})},breadcrumb:function(e){(e||d(".layui-breadcrumb"+n)).each(function(){var e=d(this),t="lay-separator",n=e.attr(t)||"/",i=e.find("a");i.next("span["+t+"]")[0]||(i.each(function(e){e!==i.length-1&&d(this).after("<span "+t+">"+n+"</span>")}),e.css("visibility","visible"))})},progress:function(e){var i="layui-progress";(e||d("."+i+n)).each(function(){var e=d(this),t=e.find(".layui-progress-bar"),n=t.attr("lay-percent");t.css("width",function(){return/^.+\/.+$/.test(n)?100*new Function("return "+n)()+"%":n}),e.attr("lay-showpercent")&&setTimeout(function(){t.html('<span class="'+i+'-text">'+n+"</span>")},350)})},collapse:function(e){(e||d(".layui-collapse"+n)).each(function(){d(this).find(".layui-colla-item").each(function(){var e=(t=d(this)).find(".layui-colla-title"),t="none"===t.find(".layui-colla-content").css("display");e.find(".layui-colla-icon").remove(),e.append('<i class="layui-icon layui-colla-icon">'+(t?"&#xe602;":"&#xe61a;")+"</i>"),e.off("click",w.collapse).on("click",w.collapse)})})}};return e&&"object"==typeof t&&t instanceof d?i[e](t):i[e]?i[e]():layui.each(i,function(e,t){t()})},new t),i=d(document);d(function(){n.render()}),i.on("click",".layui-tab-title li",w.tabClick),d(window).on("resize",w.tabAuto),e(c,n)}),layui.define(["lay","layer"],function(e){"use strict";function i(){var t=this,e=t.config.id;return{upload:function(e){t.upload.call(t,e)},reload:function(e){t.reload.call(t,e)},config:(i.that[e]=t).config}}function w(e){this.index=++T.index,this.config=k.extend({},this.config,T.config,e),this.render()}var k=layui.$,a=layui.lay,t=layui.layer,C=layui.device(),n="upload",d="layui_upload_index",T={config:{},index:layui[n]?layui[n].index+1e4:0,set:function(e){return this.config=k.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,n,e,t)}},o="layui-upload-file",r="layui-upload-form",E="layui-upload-iframe",D="layui-upload-choose",S="UPLOADING";w.prototype.config={accept:"images",exts:"",auto:!0,bindAction:"",url:"",force:"",field:"file",acceptMime:"",method:"post",data:{},drag:!0,size:0,number:0,multiple:!1,text:{"cross-domain":"Cross-domain requests are not supported","data-format-error":"Please return JSON data format","check-error":"",error:"","limit-number":null,"limit-size":null}},w.prototype.reload=function(e){this.config=k.extend({},this.config,e),this.render(!0)},w.prototype.render=function(e){var t=this.config,n=k(t.elem);return 1<n.length?(layui.each(n,function(){T.render(k.extend({},t,{elem:this}))}),this):(k.extend(t,a.options(n[0],{attr:n.attr("lay-data")?"lay-data":null})),!e&&n[0]&&n.data(d)?(e=i.getThis(n.data(d)))?e.reload(t):void 0:(t.elem=k(t.elem),t.bindAction=k(t.bindAction),t.id="id"in t?t.id:n.attr("id")||this.index,this.file(),void this.events()))},w.prototype.file=function(){var e=this.config,t=this.elemFile=k(['<input class="'+o+'" type="file" accept="'+e.acceptMime+'" name="'+e.field+'"',e.multiple?" multiple":"",">"].join("")),n=e.elem.next();(n.hasClass(o)||n.hasClass(r))&&n.remove(),C.ie&&C.ie<10&&e.elem.wrap('<div class="layui-upload-wrap"></div>'),this.isFile()?(this.elemFile=e.elem,e.field=e.elem[0].name):e.elem.after(t),C.ie&&C.ie<10&&this.initIE()},w.prototype.initIE=function(){var n,e=this.config,t=k('<iframe id="'+E+'" class="'+E+'" name="'+E+'" frameborder="0"></iframe>'),i=k(['<form target="'+E+'" class="'+r+'" method="post" key="set-mine" enctype="multipart/form-data" action="'+e.url+'">',"</form>"].join(""));k("#"+E)[0]||k("body").append(t),e.elem.next().hasClass(r)||(this.elemFile.wrap(i),e.elem.next("."+r).append((n=[],layui.each(e.data,function(e,t){t="function"==typeof t?t():t,n.push('<input type="hidden" name="'+e+'" value="'+t+'">')}),n.join(""))))},w.prototype.msg=function(e){return t.msg(e,{icon:2,shift:6})},w.prototype.isFile=function(){var e=this.config.elem[0];if(e)return"input"===e.tagName.toLocaleLowerCase()&&"file"===e.type},w.prototype.preview=function(i){window.FileReader&&layui.each(this.chooseFiles,function(e,t){var n=new FileReader;n.readAsDataURL(t),n.onload=function(){i&&i(e,t,this.result)}})},w.prototype.upload=function(e,t){var n,i,a,o,r,s=this,c=s.config,d=c.text||{},l=s.elemFile[0],u=function(){return e||s.files||s.chooseFiles||l.files},f=function(){function a(){c.multiple&&o+r===s.fileLength&&"function"==typeof c.allDone&&c.allDone({total:s.fileLength,successful:o,failed:r})}function n(n){function t(e){n.unified?layui.each(l,function(e,t){delete t[S]}):delete e[S]}var i=new FormData;if(layui.each(c.data,function(e,t){t="function"==typeof t?n.unified?t():t(n.index,n.file):t,i.append(e,t)}),n.unified)layui.each(l,function(e,t){t[S]||(t[S]=!0,i.append(c.field,t))});else{if(n.file[S])return;i.append(c.field,n.file),n.file[S]=!0}var e={url:c.url,type:"post",data:i,dataType:c.dataType||"json",contentType:!1,processData:!1,headers:c.headers||{},success:function(e){c.unified?o+=s.fileLength:o++,y(n.index,e),a(n.index),t(n.file)},error:function(e){c.unified?r+=s.fileLength:r++,s.msg(d.error||["Upload failed, please try again.","status: "+(e.status||"")+" - "+(e.statusText||"error")].join("<br>")),m(n.index,e.responseText,e),a(n.index),t(n.file)}};"function"==typeof c.progress&&(e.xhr=function(){var e=k.ajaxSettings.xhr();return e.upload.addEventListener("progress",function(e){var t;e.lengthComputable&&(t=Math.floor(e.loaded/e.total*100),c.progress(t,(c.item||c.elem)[0],e,n.index))}),e}),k.ajax(e)}var o=0,r=0,l=u();c.unified?n({unified:!0,index:0}):layui.each(l,function(e,t){n({index:e,file:t})})},p=function(){var n=k("#"+E);s.elemFile.parent().submit(),clearInterval(w.timer),w.timer=setInterval(function(){var e,t=n.contents().find("body");try{e=t.text()}catch(e){s.msg(d["cross-domain"]),clearInterval(w.timer),m()}e&&(clearInterval(w.timer),t.html(""),y(0,e))},30)},h=function(e){if("json"===c.force&&"object"!=typeof e)try{return{status:"CONVERTED",data:JSON.parse(e)}}catch(e){return s.msg(d["data-format-error"]),{status:"FORMAT_ERROR",data:{}}}return{status:"DO_NOTHING",data:{}}},y=function(e,t){s.elemFile.next("."+D).remove(),l.value="";var n=h(t);switch(n.status){case"CONVERTED":t=n.data;break;case"FORMAT_ERROR":return}"function"==typeof c.done&&c.done(t,e||0,function(e){s.upload(e)})},m=function(e,t,n){c.auto&&(l.value="");var i=h(t);switch(i.status){case"CONVERTED":t=i.data;break;case"FORMAT_ERROR":return}"function"==typeof c.error&&c.error(e||0,function(e){s.upload(e)},t,n)},g=c.exts,v=(i=[],layui.each(e||s.chooseFiles,function(e,t){i.push(t.name)}),i),x={preview:function(e){s.preview(e)},upload:function(e,t){var n={};n[e]=t,s.upload(n)},pushFile:function(){return s.files=s.files||{},layui.each(s.chooseFiles,function(e,t){s.files[e]=t}),s.files},resetFile:function(e,t,n){t=new File([t],n),s.files=s.files||{},s.files[e]=t},getChooseFiles:function(){return s.chooseFiles}},b={file:"文件",images:"图片",video:"视频",audio:"音频"}[c.accept]||"文件";if(0!==(v=0===v.length?l.value.match(/[^\/\\]+\..+/g)||[]:v).length){switch(c.accept){case"file":layui.each(v,function(e,t){if(g&&!RegExp(".\\.("+g+")$","i").test(escape(t)))return n=!0});break;case"video":layui.each(v,function(e,t){if(!RegExp(".\\.("+(g||"avi|mp4|wma|rmvb|rm|flash|3gp|flv")+")$","i").test(escape(t)))return n=!0});break;case"audio":layui.each(v,function(e,t){if(!RegExp(".\\.("+(g||"mp3|wav|mid")+")$","i").test(escape(t)))return n=!0});break;default:layui.each(v,function(e,t){if(!RegExp(".\\.("+(g||"jpg|png|gif|bmp|jpeg|svg|webp")+")$","i").test(escape(t)))return n=!0})}return n?(s.msg(d["check-error"]||"选择的"+b+"中包含不支持的格式"),l.value=""):"choose"!==t&&!c.auto||(c.choose&&c.choose(x),"choose"!==t)?(s.fileLength=(a=0,b=u(),layui.each(b,function(){a++}),a),c.number&&s.fileLength>c.number?s.msg("function"==typeof d["limit-number"]?d["limit-number"](c,s.fileLength):"同时最多只能上传: "+c.number+" 个文件<br>您当前已经选择了: "+s.fileLength+" 个文件"):0<c.size&&!(C.ie&&C.ie<10)&&(layui.each(u(),function(e,t){t.size>1024*c.size&&(t=1<=(t=c.size/1024)?t.toFixed(2)+"MB":c.size+"KB",l.value="",o=t)}),o)?s.msg("function"==typeof d["limit-size"]?d["limit-size"](c,o):"文件大小不能超过 "+o):(r=function(){if(C.ie)return(9<C.ie?f:p)();f()},void("function"==typeof c.before?T.util.promiseLikeResolve(c.before(x)).then(function(e){!1!==e?r():c.auto&&(l.value="")},function(e){c.auto&&(l.value=""),void 0!==e&&layui.hint().error(e)}):r()))):void 0}},w.prototype.events=function(){function i(e){r.chooseFiles={},layui.each(e,function(e,t){var n=(new Date).getTime();r.chooseFiles[n+"-"+e]=t})}function a(e,t){var n=r.elemFile,e=(l.item||l.elem,1<e.length?e.length+"个文件":(e[0]||{}).name||n[0].value.match(/[^\/\\]+\..+/g)||[]);n.next().hasClass(D)&&n.next().remove(),r.upload(null,"choose"),r.isFile()||l.choose||n.after('<span class="layui-inline '+D+'">'+e+"</span>")}function o(e){var n;return(e=e||[]).length?r.files?(n=[],layui.each(e,function(e,t){s(t)&&n.push(c(t))}),n):c(e):[]}var r=this,l=r.config,s=function(n){var i=!0;return layui.each(r.files,function(e,t){if(!(i=!(t.name===n.name)))return!0}),i},c=function(e){function n(e){e.ext=e.name.substr(e.name.lastIndexOf(".")+1).toLowerCase(),e.sizes=T.util.parseSize(e.size)}return e instanceof FileList?layui.each(e,function(e,t){n(t)}):n(e),e};l.elem.off("upload.start").on("upload.start",function(){var e=k(this);r.config.item=e,r.elemFile[0].click()}),C.ie&&C.ie<10||l.elem.off("upload.over").on("upload.over",function(){k(this).attr("lay-over","")}).off("upload.leave").on("upload.leave",function(){k(this).removeAttr("lay-over")}).off("upload.drop").on("upload.drop",function(e,t){var n=k(this),t=o(t.originalEvent.dataTransfer.files);n.removeAttr("lay-over"),i(t),l.auto?r.upload():a(t)}),r.elemFile.on("change",function(){var e=o(this.files);0!==e.length&&(i(e),l.auto?r.upload():a(e))}),l.bindAction.off("upload.action").on("upload.action",function(){r.upload()}),l.elem.data(d)||(l.elem.on("click",function(){r.isFile()||k(this).trigger("upload.start")}),l.drag&&l.elem.on("dragover",function(e){e.preventDefault(),k(this).trigger("upload.over")}).on("dragleave",function(e){k(this).trigger("upload.leave")}).on("drop",function(e){e.preventDefault(),k(this).trigger("upload.drop",e)}),l.bindAction.on("click",function(){k(this).trigger("upload.action")}),l.elem.data(d,l.id))},T.util={parseSize:function(e,t){var n,i;return t=t||2,null!=e&&e?(n="string"==typeof e?parseFloat(e):e,i=Math.floor(Math.log(n)/Math.log(1024)),(e=(e=n/Math.pow(1024,i))%1==0?e:parseFloat(e.toFixed(t)))+["Bytes","Kb","Mb","Gb","Tb","Pb","Eb","Zb","Yb"][i]):"0"},promiseLikeResolve:function(e){var t=k.Deferred();return e&&"function"==typeof e.then?e.then(t.resolve,t.reject):t.resolve(e),t.promise()}},i.that={},i.getThis=function(e){var t=i.that[e];return t||hint.error(e?n+" instance with ID '"+e+"' not found":"ID argument required"),t},T.render=function(e){return e=new w(e),i.call(e)},e(n,T)}),layui.define(["lay","layer","util"],function(e){"use strict";var E=layui.$,p=layui.layer,D=layui.util,o=layui.hint(),S=(layui.device(),"form"),u=".layui-form",N="layui-this",L="layui-hide",A="layui-disabled",t=((n=function(){this.config={verify:{required:function(e){if(!/[\S]+/.test(e))return"必填项不能为空"},phone:function(e){if(e&&!/^1\d{10}$/.test(e))return"手机号格式不正确"},email:function(e){if(e&&!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(e))return"邮箱格式不正确"},url:function(e){if(e&&!/^(#|(http(s?)):\/\/|\/\/)[^\s]+\.[^\s]+$/.test(e))return"链接格式不正确"},number:function(e){if(e&&isNaN(e))return"只能填写数字"},date:function(e){if(e&&!/^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/.test(e))return"日期格式不正确"},identity:function(e){if(e&&!/(^\d{15}$)|(^\d{17}(x|X|\d)$)/.test(e))return"身份证号格式不正确"}},autocomplete:null}}).prototype.set=function(e){return E.extend(!0,this.config,e),this},n.prototype.verify=function(e){return E.extend(!0,this.config.verify,e),this},n.prototype.getFormElem=function(e){return E(u+(e?'[lay-filter="'+e+'"]':""))},n.prototype.on=function(e,t){return layui.onevent.call(this,S,e,t)},n.prototype.val=function(e,n){return this.getFormElem(e).each(function(e,t){var i=E(this);layui.each(n,function(e,t){var n;(e=i.find('[name="'+e+'"]'))[0]&&("checkbox"===(n=e[0].type)?e[0].checked=t:"radio"===n?e.each(function(){this.checked=this.value==t+""}):e.val(t))})}),r.render(null,e),this.getValue(e)},n.prototype.getValue=function(e,t){t=t||this.getFormElem(e);var i={},a={},e=t.find("input,select,textarea");return layui.each(e,function(e,t){var n;E(this),t.name=(t.name||"").replace(/^\s*|\s*&/,""),t.name&&(/^.*\[\]$/.test(t.name)&&(n=t.name.match(/^(.*)\[\]$/g)[0],i[n]=0|i[n],n=t.name.replace(/^(.*)\[\]$/,"$1["+i[n]+++"]")),/^(checkbox|radio)$/.test(t.type)&&!t.checked||(a[n||t.name]=t.value))}),a},n.prototype.render=function(e,t){var d=this,n=d.config,i=E(u+(t?'[lay-filter="'+t+'"]':"")),a={input:function(e){function t(e,t){var n=e.val(),i=Number(n),a=Number(e.attr("step"))||1,o=Number(e.attr("min")),r=Number(e.attr("max")),l=Number(e.attr("lay-precision")),s="click"!==t&&""===n,c="init"===t;isNaN(i)||("click"===t&&(i=E(this).index()?i-a:i+a),t=function(e){return((e.toString().match(/\.(\d+$)/)||[])[1]||"").length},l=0<=l?l:Math.max(t(a),t(n)),s||(c||r<=(i=i<=o?o:i)&&(i=r),0===l?i=parseInt(i):0<l&&(i=i.toFixed(l)),e.val(i)),e[(i<o||r<i)&&!s?"addClass":"removeClass"]("layui-input-number-out-of-range"),c)||((a={increment:e.next().find(".layui-icon-up"),decrement:e.next().find(".layui-icon-down")}).increment[r<=i&&!s?"addClass":"removeClass"](A),a.decrement[i<=o&&!s?"addClass":"removeClass"](A))}e=e||i.find("input,textarea"),n.autocomplete&&e.attr("autocomplete",n.autocomplete);i.find("input[lay-affix],textarea[lay-affix]").each(function(){function a(n){n=E.extend({},f[l]||{value:l},n,lay.options(r[0]));var i,t=E('<div class="'+d+'">'),e=layui.isArray(n.value)?n.value:[n.value],e=E((i=[],layui.each(e,function(e,t){i.push('<i class="layui-icon layui-icon-'+t+(n.disabled?" "+A:"")+'"></i>')}),i.join(""))),a=(t.append(e),n.split&&t.addClass("layui-input-split"),n.className&&t.addClass(n.className),r.next("."+d)),o=(a[0]&&a.remove(),r.parent().hasClass(s)||r.wrap('<div class="'+s+'"></div>'),r.next("."+c));o[0]?((a=o.find("."+d))[0]&&a.remove(),o.prepend(t),r.css("padding-right",function(){return(r.closest(".layui-input-group")[0]?0:o.outerWidth())+t.outerWidth()})):(t.addClass(c),r.after(t)),"auto"===n.show&&u(t,r.val()),"function"==typeof n.init&&n.init.call(this,r,n),r.on("input propertychange",function(){var e=this.value;"auto"===n.show&&u(t,e)}),r.on("blur",function(){"function"==typeof n.blur&&n.blur.call(this,r,n)}),e.on("click",function(){var e=r.attr("lay-filter");E(this).hasClass(A)||("function"==typeof n.click&&n.click.call(this,r,n),layui.event.call(this,S,"input-affix("+e+")",{elem:r[0],affix:l,options:n}))})}var r=E(this),l=r.attr("lay-affix"),s="layui-input-wrap",c="layui-input-suffix",d="layui-input-affix",e=r.is("[disabled]")||r.is("[readonly]"),u=function(e,t){(e=E(e))[0]&&e[E.trim(t)?"removeClass":"addClass"](L)},f={eye:{value:"eye-invisible",click:function(e,t){var n="LAY_FORM_INPUT_AFFIX_SHOW",i=e.data(n);e.attr("type",i?"password":"text").data(n,!i),a({value:i?"eye-invisible":"eye"})}},clear:{value:"clear",click:function(e){e.val("").focus(),u(E(this).parent(),null)},show:"auto",disabled:e},number:{value:["up","down"],split:!0,className:"layui-input-number",disabled:r.is("[disabled]"),init:function(e){t.call(this,e,"init")},click:function(e){t.call(this,e,"click")},blur:function(e){t.call(this,e,"blur")}}};a()})},select:function(e){function p(a,e,t,n,d,i){var o,u,r,l,s,c,f,p,h,y=E(this),m=e,g=m.find("input"),v=a.find("dl"),x=(v.children("dd"),v.children("dt")),b=this.selectedIndex,w="";t||(u="cs"===y.attr("lay-search")?{caseSensitive:!0}:lay.options(y,{attr:"lay-search"}),r=y.attr("lay-append-to")||"body",l=y.attr("lay-append-position"),s=!(!lay.ie||"10"!==lay.ie&&"11"!==lay.ie||!g.attr("placeholder")),c=function(){i&&(a.appendTo(r).css({width:m.width()+"px"}),(e=function(){lay.position(m[0],a[0],{position:l,allowBottomOut:!0,offset:[0,5]})})(),E(window).on("resize.lay_select_resize",e));var e=a.offset().top+a.outerHeight()+5-I.scrollTop(),t=v.outerHeight(),n=v.children("dd");b=y[0].selectedIndex,m.parent().addClass(k+"ed"),n.removeClass(L),x.removeClass(L),n.removeClass(N),0<=b&&n.eq(b).addClass(N),e+t>I.height()&&t<=e&&a.addClass(k+"up"),p(),s&&v.off("mousedown.lay_select_ieph").on("mousedown.lay_select_ieph",function(){g[0].__ieph=!0,setTimeout(function(){g[0].__ieph=!1},60)}),o=lay.onClickOutside((i?a:v)[0],function(){f(),w&&g.val(w)},{ignore:m})},f=function(e){m.parent().removeClass(k+"ed "+k+"up"),g.blur(),d&&v.children("."+T).remove(),o&&o(),i&&(a.detach(),E(window).off("resize.lay_select_resize")),e||h(g.val(),function(e){var t=y[0].selectedIndex;e&&(w=E(y[0].options[t]).html(),0===t&&w===g.attr("placeholder")&&(w=""),g.val(w||""))})},p=function(){var e,t,n=v.children("dd."+N);n[0]&&(e=n.position().top,t=v.height(),n=n.height(),t<e&&v.scrollTop(e+v.scrollTop()-t+n-5),e<0)&&v.scrollTop(e+v.scrollTop()-5)},m.on("click",function(e){(m.parent().hasClass(k+"ed")?f:c)(),v.find("."+C).remove()}),m.find(".layui-edge").on("click",function(){g.focus()}),g.on("keyup",function(e){9===e.keyCode&&c()}).on("keydown",function(a){function e(e){a.preventDefault();var t,n,i=v.children("dd:not(."+L+",."+A+")");i.length&&(t=i.length-1,n=-1,layui.each(i,function(e,t){if(E(t).hasClass(N))return n=e,!0}),e="prev"===e?n-1<0?t:n-1:t<n+1?0:n+1,i.eq(e).addClass(N).siblings().removeClass(N),p())}var t=a.keyCode;9===t&&f();38===t&&e("prev"),40===t&&e("next"),13===t&&(a.preventDefault(),v.children("dd."+N).trigger("click"))}).on("paste",function(){c()}),h=function(a,e,o){var r,l=0,t=v.children("dd"),s=!1,c=a;return u.caseSensitive||(a=a.toLowerCase()),u.fuzzy&&(r=function(e,t){for(var n={},i=["^"],a=(e=t?e:e.toLowerCase()).trim().split(""),o=0;o<a.length;o++){var r=a[o];n[r]=(n[r]||0)+1}for(r in n){for(i.push("(?=.*"),o=0;o<n[r];o++)i.push(r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")),o!==n[r]-1&&i.push(".*");i.push(")")}return i.push(".*"),new RegExp(i.join(""),t?void 0:"i")}(a,u.caseSensitive)),layui.each(t,function(){var e=E(this),t=e.text(),n=d&&e.hasClass(T),i=(d&&!n&&t===c&&(s=!0),u.caseSensitive||(t=t.toLowerCase()),u.fuzzy?!r.test(t):-1===t.indexOf(a));(""===a||"blur"===o?a!==t:i)&&l++,"keyup"===o&&e[(d?i&&!n:i)?"addClass":"removeClass"](L)}),"keyup"===o&&layui.each(x,function(){var e=E(this),t=e.nextUntil("dt").filter("dd");e[(t=d?t.not("."+T):t).length==t.filter("."+L).length?"addClass":"removeClass"](L)}),e(t=l===t.length,s),t},n&&g.on("input propertychange",layui.debounce(function(e){var a=this.value,t=e.keyCode;return 9!==t&&13!==t&&37!==t&&38!==t&&39!==t&&40!==t&&(s&&e.target.__ieph?e.target.__ieph=!1:(h(a,function(e,t){var n,i;d?t?v.children("."+T).remove():(t=v.children("."+T))[0]?t.attr("lay-value",a).html(D.escape(a)):(t=E("<dd>").addClass(T).attr("lay-value",a).html(D.escape(a)),i=(n=v.children().eq(0)).hasClass("layui-select-tips"),n[i?"after":"before"](t)):e?v.find("."+C)[0]||v.append('<p class="'+C+'">无匹配项</p>'):v.find("."+C).remove()},"keyup"),""===a&&(y.val(""),v.find("."+N).removeClass(N),(y[0].options[0]||{}).value||v.children("dd:eq(0)").addClass(N),v.find("."+C).remove(),d)&&v.children("."+T).remove(),void p()))},50)).on("blur",function(e){var t=y[0].selectedIndex;w=E(y[0].options[t]).text(),0===t&&w===g.attr("placeholder")&&(w=""),setTimeout(function(){h(g.val(),function(e){w||g.val("")},"blur")},200)}),v.on("click","dd",function(){var e,t=E(this),n=t.attr("lay-value"),i=y.attr("lay-filter");return t.hasClass(A)||(t.hasClass("layui-select-tips")?g.val(""):(g.val(t.text()),t.addClass(N)),d&&t.hasClass(T)&&(v.append(t.removeClass(T)),e=E("<option>").attr("value",n).text(t.text()),y.append(e)),t.siblings().removeClass(N),y.val(n).removeClass("layui-form-danger"),layui.event.call(this,S,"select("+i+")",{elem:y[0],value:n,othis:a}),f(!0)),!1}),v.on("mousedown pointerdown touchstart",function(e){layui.stope(e)}),a.find("dl>dt").on("click",function(e){return!1}),i&&e.on("_lay-select-destroy",function(){a.remove()}))}var k="layui-form-select",C="layui-select-none",T="layui-select-create-option",h="layui-select-panel-elem-data",e=e||i.find("select");E.event.special["_lay-select-destroy"]={remove:function(e){e.handler()}},e.each(function(e,t){var n=E(this),i=n.next("."+k),a=this.disabled,o=t.value,r=E(t.options[t.selectedIndex]),t=t.options[0];if("string"==typeof n.attr("lay-ignore"))return n.show();var l,s,c="string"==typeof n.attr("lay-search"),d="string"==typeof n.attr("lay-creatable")&&c,u="string"==typeof n.attr("lay-append-to"),t=t&&!t.value&&t.innerHTML||"请选择",f=E(['<div class="'+(c?"":"layui-unselect ")+k,(a?" layui-select-disabled":"")+'"></div>'].join("")),t=E(['<div class="layui-select-title">','<input type="text" placeholder="'+D.escape(E.trim(t))+'" value="'+D.escape(E.trim(o?r.html():""))+'"'+(!a&&c?"":" readonly")+' class="layui-input'+(c?"":" layui-unselect")+(a?" "+A:"")+'">','<i class="layui-edge"></i>',"</div>"].join("")),r=E(['<dl class="layui-anim layui-anim-upbit'+(n.find("optgroup")[0]?" layui-select-group":"")+'">',(r=n.find("*"),l=[],layui.each(r,function(e,t){var n=t.tagName.toLowerCase();0!==e||t.value||"optgroup"===n?l.push("optgroup"===n?"<dt>"+t.label+"</dt>":'<dd lay-value="'+D.escape(t.value)+'" class="'+(o===t.value?N:"")+(t.disabled?" "+A:"")+'">'+E.trim(t.innerHTML)+"</dd>"):l.push('<dd lay-value="" class="layui-select-tips">'+E.trim(t.innerHTML||"请选择")+"</dd>")}),0===l.length&&l.push('<dd lay-value="" class="'+A+'">没有选项</dd>'),l.join("")+"</dl>")].join(""));i[0]&&(u&&(s=i.data(h))&&s.remove(),i.remove()),u?(f.append(t),n.after(f),s=E('<div class="'+k+' layui-select-panel-wrap"></div>').append(r),f.data(h,s),p.call(this,s,t,a,c,d,u)):(f.append(t).append(r),n.after(f),p.call(this,f,t,a,c,d,u))})},checkbox:function(e){var c={checkbox:["layui-form-checkbox","layui-form-checked","checkbox"],switch:["layui-form-switch","layui-form-onswitch","switch"],SUBTRA:"layui-icon-indeterminate"};(e=e||i.find("input[type=checkbox]")).each(function(e,t){var n=E(this),i=n.attr("lay-skin")||"primary",a=D.escape(E.trim(t.title||(t.title=n.attr("lay-text")||""))),o=this.disabled,r=c[i]||c.checkbox,l=n.next("."+r[0]),s=(l[0]&&l.remove(),[]);if(n.next("[lay-checkbox]")[0]&&(a=(l=n.next()).html()||"",1<l[0].attributes.length)&&layui.each(l[0].attributes,function(e,t){"lay-checkbox"!==t.name&&s.push(t.name+'="'+t.value+'"')}),s=s.join(" "),a="switch"===i?a.split("|"):[a],"string"==typeof n.attr("lay-ignore"))return n.show();o=E(['<div class="layui-unselect '+r[0],t.checked?" "+r[1]:"",o?" layui-checkbox-disabled "+A:"",'"',i?' lay-skin="'+i+'"':"",">",(l={checkbox:[a[0]?"<div "+s+">"+a[0]+"</div>":"primary"===i?"":"<div></div>",'<i class="layui-icon '+("primary"===i&&!t.checked&&n.get(0).indeterminate?c.SUBTRA:"layui-icon-ok")+'"></i>'].join(""),switch:"<div>"+(!t.checked&&a[1]||a[0]||"")+"</div><i></i>"})[i]||l.checkbox,"</div>"].join("")),n.after(o),function(t,n){var i=E(this),a="switch"===(e=i.attr("lay-skin")||"primary"),e="primary"===e;t.on("click",function(){var e=i.attr("lay-filter");i[0].disabled||(i[0].indeterminate&&(i[0].indeterminate=!1),i[0].checked=!i[0].checked,layui.event.call(i[0],S,n[2]+"("+e+")",{elem:i[0],value:i[0].value,othis:t}))}),d.syncAppearanceOnPropChanged(this,"checked",function(){var e;a&&(e=(t.next("*[lay-checkbox]")[0]?t.next().html():i.attr("title")||"").split("|"),t.children("div").html(!this.checked&&e[1]||e[0])),t.toggleClass(n[1],this.checked)}),e&&d.syncAppearanceOnPropChanged(this,"indeterminate",function(){this.indeterminate?t.children(".layui-icon-ok").removeClass("layui-icon-ok").addClass(c.SUBTRA):t.children("."+c.SUBTRA).removeClass(c.SUBTRA).addClass("layui-icon-ok")})}.call(this,o,r)})},radio:function(e){var s="layui-form-radio",c=["layui-icon-radio","layui-icon-circle"];(e=e||i.find("input[type=radio]")).each(function(e,t){var n=E(this),i=n.next("."+s),a=this.disabled,o=n.attr("lay-skin");if("string"==typeof n.attr("lay-ignore"))return n.show();i[0]&&i.remove();var i=D.escape(t.title||""),r=[],l=(n.next("[lay-radio]")[0]&&(i=(l=n.next()).html()||"",1<l[0].attributes.length)&&layui.each(l[0].attributes,function(e,t){"lay-radio"!==t.name&&r.push(t.name+'="'+t.value+'"')}),r=r.join(" "),E(['<div class="layui-unselect '+s,t.checked?" "+s+"ed":"",(a?" layui-radio-disabled "+A:"")+'"',o?' lay-skin="'+o+'"':"",">",'<i class="layui-anim layui-icon '+c[t.checked?0:1]+'"></i>',"<div "+r+">"+i+"</div>","</div>"].join("")));n.after(l),function(n){var i=E(this),a="layui-anim-scaleSpring";n.on("click",function(){var e=i.attr("lay-filter");i[0].disabled||(i[0].checked=!0,layui.event.call(i[0],S,"radio("+e+")",{elem:i[0],value:i[0].value,othis:n}))}),d.syncAppearanceOnPropChanged(this,"checked",function(){var e,t=this;t.checked?(n.addClass(s+"ed"),n.children(".layui-icon").addClass(a+" "+c[0]),e=i.parents(u).find("input[name="+t.name.replace(/(\.|#|\[|\])/g,"\\$1")+"]"),layui.each(e,function(){t!==this&&(this.checked=!1)})):(n.removeClass(s+"ed"),n.children(".layui-icon").removeClass(a+" "+c[0]).addClass(c[1]))})}.call(this,l)})}},t=function(){layui.each(a,function(e,t){t()})};return"object"===layui.type(e)?E(e).is(u)?(i=E(e),t()):e.each(function(e,t){var n=E(t);n.closest(u).length&&("SELECT"===t.tagName?a.select(n):"INPUT"===t.tagName&&("checkbox"===(t=t.type)||"radio"===t?a[t](n):a.input(n)))}):e?a[e]?a[e]():o.error('不支持的 "'+e+'" 表单渲染'):t(),d},n.prototype.syncAppearanceOnPropChanged=function(e,t,n){var i=Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,t);Object.defineProperty(e,t,lay.extend({},i,{get:function(){return i.get.call(this)},set:function(e){i.set.call(this,e),n.call(this)}}))},n.prototype.validate=function(e){var d,u=this.config.verify,f="layui-form-danger";return!(e=E(e))[0]||(void 0!==e.attr("lay-verify")||!1!==this.validate(e.find("*[lay-verify]")))&&(layui.each(e,function(e,r){var l=E(this),t=(l.attr("lay-verify")||"").split("|"),s=l.attr("lay-vertype"),c=E.trim(l.val());if(l.removeClass(f),layui.each(t,function(e,t){var n="",i=u[t];if(i){var a="function"==typeof i?n=i(c,r):!i[0].test(c),o="select"===r.tagName.toLowerCase()||/^(checkbox|radio)$/.test(r.type),n=n||i[1];if("required"===t&&(n=l.attr("lay-reqtext")||n),a)return"tips"===s?p.tips(n,"string"!=typeof l.attr("lay-ignore")&&o?l.next():l,{tips:1}):"alert"===s?p.alert(n,{title:"提示",shadeClose:!0}):/\b(string|number)\b/.test(typeof n)&&p.msg(n,{icon:5,shift:6}),setTimeout(function(){(o?l.next().find("input"):r).focus()},7),l.addClass(f),d=!0}}),d)return d}),!d)},n.prototype.submit=function(e,t){var n,i=E(this),e="string"==typeof e?e:i.attr("lay-filter"),a=this.getFormElem?this.getFormElem(e):i.parents(u).eq(0),o=a.find("*[lay-verify]");return!!r.validate(o)&&(n=r.getValue(null,a),o={elem:this.getFormElem?window.event&&window.event.target:this,form:(this.getFormElem?a:i.parents("form"))[0],field:n},"function"==typeof t&&t(o),layui.event.call(this,S,"submit("+e+")",o))}),r=new n,n=E(document),I=E(window);E(function(){r.render()}),n.on("reset",u,function(){var e=E(this).attr("lay-filter");setTimeout(function(){r.render(null,e)},50)}),n.on("submit",u,t).on("click","*[lay-submit]",t),e(S,r)}),layui.define(["lay","laytpl","laypage","form","util"],function(c){"use strict";function h(){var n=this,e=n.config,i=e.id||e.index;return{config:e,reload:function(e,t){n.reload.call(n,e,t)},reloadData:function(e,t){k.reloadData(i,e,t)},setColsWidth:function(){n.setColsWidth.call(n)},resize:function(){n.resize.call(n)}}}function y(e){var t=h.that[e];return t||p.error(e?"The table instance with ID '"+e+"' not found":"ID argument required"),t||null}function a(e){var t=h.config[e];return t||p.error(e?"The table instance with ID '"+e+"' not found":"ID argument required"),t||null}function m(e){var t=this.config||{},n=(e=e||{}).item3,i=e.content;return"numbers"===n.type&&(i=e.tplData[k.config.numbersName]),("escape"in n?n:t).escape&&(i=b.escape(i)),(t=e.text&&n.exportTemplet||n.templet||n.toolbar)&&(i="function"==typeof t?t.call(n,e.tplData,e.obj):v(function(t){try{return d(t).html()}catch(e){return t}}(t)||String(i)).render(g.extend({LAY_COL:n},e.tplData))),e.text?g("<div>"+i+"</div>").text():i}function e(e){return['<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{=d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{=d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>',"<thead>","{{# layui.each(d.data.cols, function(i1, item1){ }}","<tr>","{{# layui.each(item1, function(i2, item2){ }}",'{{# if(item2.fixed && item2.fixed !== "right"){ left = true; } }}','{{# if(item2.fixed === "right"){ right = true; } }}',(e=e||{}).fixed&&"right"!==e.fixed?'{{# if(item2.fixed && item2.fixed !== "right"){ }}':"right"===e.fixed?'{{# if(item2.fixed === "right"){ }}':"","{{# var isSort = !(item2.colGroup) && item2.sort; }}",'<th data-field="{{= item2.field||i2 }}" data-key="{{=d.index}}-{{=i1}}-{{=i2}}" {{# if( item2.parentKey){ }}data-parentkey="{{= item2.parentKey }}"{{# } }} {{# if(item2.minWidth){ }}data-minwidth="{{=item2.minWidth}}"{{# } }} {{# if(item2.maxWidth){ }}data-maxwidth="{{=item2.maxWidth}}"{{# } }} {{# if(item2.style){ }}style="{{=item2.style}}"{{# } }} {{#var colspan = layui.type(item2.colspan2) === \'number\' ? item2.colspan2 : item2.colspan; if(colspan){}} colspan="{{=colspan}}"{{#} if(item2.rowspan){}} rowspan="{{=item2.rowspan}}"{{#}}} {{# if(item2.unresize || item2.colGroup){ }}data-unresize="true"{{# } }} class="{{# if(item2.hide){ }}layui-hide{{# } }}{{# if(isSort){ }} layui-unselect{{# } }}{{# if(!item2.field){ }} layui-table-col-special{{# } }}"{{# if(item2.title){ }} title="{{ layui.$(\'<div>\' + item2.title + \'</div>\').text() }}"{{# } }}>','<div class="layui-table-cell laytable-cell-',"{{# if(item2.colGroup){ }}","group","{{# } else { }}","{{=d.index}}-{{=i1}}-{{=i2}}",'{{# if(item2.type !== "normal"){ }}'," laytable-cell-{{= item2.type }}","{{# } }}","{{# } }}",'" {{#if(item2.align){}}align="{{=item2.align}}"{{#}}}>','{{# if(item2.type === "checkbox"){ }}','<input type="checkbox" name="layTableCheckbox" lay-skin="primary" lay-filter="layTableAllChoose" {{# if(item2[d.data.checkName]){ }}checked{{# }; }}>',"{{# } else { }}",'<span>{{-item2.title||""}}</span>',"{{# if(isSort){ }}",'<span class="layui-table-sort layui-inline"><i class="layui-edge layui-table-sort-asc" title="升序"></i><i class="layui-edge layui-table-sort-desc" title="降序"></i></span>',"{{# } }}","{{# } }}","</div>","</th>",e.fixed?"{{# }; }}":"","{{# }); }}","</tr>","{{# }); }}","</thead>","</table>"].join("")}function t(e){this.index=++k.index,this.config=g.extend({},this.config,k.config,e),this.render()}var g=layui.$,d=layui.lay,v=layui.laytpl,R=layui.laypage,x=layui.layer,i=layui.form,b=layui.util,p=layui.hint(),w=layui.device(),k={config:{checkName:"LAY_CHECKED",indexName:"LAY_INDEX",numbersName:"LAY_NUM",disabledName:"LAY_DISABLED"},cache:{},index:layui.table?layui.table.index+1e4:0,set:function(e){return this.config=g.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,C,e,t)}},C="table",T="lay-"+C+"-id",n=".layui-table",E="layui-hide",f="layui-hide-v",D="layui-none",S="layui-table-view",r=".layui-table-header",N=".layui-table-body",P=".layui-table-fixed-r",q=".layui-table-pageview",L=".layui-table-sort",A="layui-table-checked",I="layui-table-edit",M="layui-table-hover",u="laytable-cell-group",j="layui-table-col-special",_="layui-table-tool-panel",H="layui-table-expanded",B="layui-table-disabled-transition",F="LAY_TABLE_MOVE_DICT",o=['<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{=d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{=d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>',"<tbody></tbody>","</table>"].join(""),z=[,"{{# if(d.data.toolbar){ }}",'<div class="layui-table-tool">','<div class="layui-table-tool-temp"></div>','<div class="layui-table-tool-self"></div>',"</div>","{{# } }}",'<div class="layui-table-box">',"{{# if(d.data.loading){ }}",'<div class="layui-table-init">','<div class="layui-table-loading-icon">','{{# if(typeof d.data.loading === "string"){ }}',"{{- d.data.loading}}","{{# } else{ }}",'<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>',"{{# } }}","</div>","</div>","{{# } }}","{{# var left, right; }}",'<div class="layui-table-header">',e(),"</div>",'<div class="layui-table-body layui-table-main">',o,"</div>","{{# if(left){ }}",'<div class="layui-table-fixed layui-table-fixed-l">','<div class="layui-table-header">',e({fixed:!0}),"</div>",'<div class="layui-table-body">',o,"</div>","</div>","{{# }; }}","{{# if(right){ }}",'<div class="layui-table-fixed layui-table-fixed-r layui-hide">','<div class="layui-table-header">',e({fixed:"right"}),'<div class="layui-table-mend"></div>',"</div>",'<div class="layui-table-body">',o,"</div>","</div>","{{# }; }}","</div>","{{# if(d.data.totalRow){ }}",'<div class="layui-table-total">','<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{=d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{=d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>','<tbody><tr><td><div class="layui-table-cell" style="visibility: hidden;">Total</div></td></tr></tbody>',"</table>","</div>","{{# } }}",'<div class="layui-table-column layui-table-page layui-hide">','<div class="layui-inline layui-table-pageview" id="layui-table-page{{=d.index}}"></div>',"</div>"].join(""),l=g(window),O=g(document),s=(t.prototype.config={limit:10,loading:!0,escape:!0,cellMinWidth:60,cellMaxWidth:Number.MAX_VALUE,editTrigger:"click",defaultToolbar:["filter","exports","print"],defaultContextmenu:!0,autoSort:!0,text:{none:"无数据"},cols:[]},t.prototype.render=function(e){var t=this,n=t.config,i=(n.elem=g(n.elem),n.where=n.where||{},n.id="id"in n?n.id:n.elem.attr("id")||t.index);if(h.that[i]=t,(h.config[i]=n).request=g.extend({pageName:"page",limitName:"limit"},n.request),n.response=g.extend({statusName:"code",statusCode:0,msgName:"msg",dataName:"data",totalRowName:"totalRow",countName:"count"},n.response),null!==n.page&&"object"==typeof n.page&&(n.limit=n.page.limit||n.limit,n.limits=n.page.limits||n.limits,t.page=n.page.curr=n.page.curr||1,delete n.page.elem,delete n.page.jump),!n.elem[0])return t;if(n.elem.attr("lay-filter")||n.elem.attr("lay-filter",n.id),"reloadData"===e)return t.pullData(t.page,{type:"reloadData"});n.index=t.index,t.key=n.id||n.index,t.setInit(),n.height&&/^full-.+$/.test(n.height)?(t.fullHeightGap=n.height.split("-")[1],n.height=l.height()-(parseFloat(t.fullHeightGap)||0)):n.height&&/^#\w+\S*-.+$/.test(n.height)?(i=n.height.split("-"),t.parentHeightGap=i.pop(),t.parentDiv=i.join("-"),n.height=g(t.parentDiv).height()-(parseFloat(t.parentHeightGap)||0)):"function"==typeof n.height&&(t.customHeightFunc=n.height,n.height=t.customHeightFunc());var a,i=(e=n.elem).next("."+S),o=t.elem=g("<div></div>");o.addClass((a=[S,S+"-"+t.index,"layui-form","layui-border-box"],n.className&&a.push(n.className),a.join(" "))).attr(((a={"lay-filter":"LAY-TABLE-FORM-DF-"+t.index,style:(a=[],n.width&&a.push("width:"+n.width+"px;"),a.join(""))})[T]=n.id,a)).html(v(z,{open:"{{",close:"}}"}).render({data:n,index:t.index})),t.renderStyle(),i[0]&&i.remove(),e.after(o),t.layTool=o.find(".layui-table-tool"),t.layBox=o.find(".layui-table-box"),t.layHeader=o.find(r),t.layMain=o.find(".layui-table-main"),t.layBody=o.find(N),t.layFixed=o.find(".layui-table-fixed"),t.layFixLeft=o.find(".layui-table-fixed-l"),t.layFixRight=o.find(P),t.layTotal=o.find(".layui-table-total"),t.layPage=o.find(".layui-table-page"),t.renderToolbar(),t.renderPagebar(),t.fullSize(),t.setColsWidth(),t.pullData(t.page),t.events()},t.prototype.initOpts=function(e){this.config,e.checkbox&&(e.type="checkbox"),e.space&&(e.type="space"),e.type||(e.type="normal"),"normal"!==e.type&&(e.unresize=!0,e.width=e.width||{checkbox:50,radio:50,space:30,numbers:60}[e.type])},t.prototype.setInit=function(e){var i,n,l=this,s=l.config;if(s.clientWidth=s.width||(i=function(e){var t,n;e=e||s.elem.parent(),n=window.getComputedStyle?"border-box"!==(n=l.getElementSize(e[0])).boxSizing||d.ie?n.width:n.width-n.paddingLeft-n.paddingRight-n.borderLeftWidth-n.borderRightWidth:e.width();try{t="none"===e.css("display")}catch(e){}return!e[0]||d.isTopElem(e[0])||n&&!t?n:i(e.parent())})(),"width"===e)return s.clientWidth;s.height=s.maxHeight||s.height,s.css&&-1===s.css.indexOf(S)&&(n=s.css.split("}"),layui.each(n,function(e,t){t&&(n[e]="."+S+"-"+l.index+" "+t)}),s.css=n.join("}"));var c=function(n,e,i,a){var o,r;a?(a.key=[s.index,n,i].join("-"),a.colspan=a.colspan||0,a.rowspan=a.rowspan||0,l.initOpts(a),(o=n+(parseInt(a.rowspan)||1))<s.cols.length?(a.colGroup=!0,r=0,layui.each(s.cols[o],function(e,t){t.HAS_PARENT||1<=r&&r==(a.colspan||1)||(t.HAS_PARENT=!0,t.parentKey=[s.index,n,i].join("-"),r+=parseInt(1<t.colspan?t.colspan:1),c(o,s.cols[o],e,t))})):a.colGroup=!1,a.hide=a.hide&&!a.colGroup||!1):e.splice(i,1)};layui.each(s.cols,function(n,i){layui.each(i,function(e,t){n?delete t.HAS_PARENT:c(n,i,e,t)})})},t.prototype.renderStyle=function(){var e,n,t,i,a=this.config,o=this.index,r=[];layui.each(a.cols,function(n,e){layui.each(e,function(e,t){e=[o,n,e].join("-"),t=["width: ",t.width||a.cellMinWidth,"px"].join(""),r.push(".laytable-cell-"+e+"{"+t+"}")})}),(e=a.lineStyle)&&(n=".layui-table-view-"+o+" .layui-table-body .layui-table tr",t=e.split(";"),i="none",layui.each(t,function(e,t){if("height"===(t=t.split(":"))[0])return t=parseFloat(t[1]),isNaN(t)||(i=t-1+"px"),!0}),layui.each(["{"+e+"}",".layui-table-cell{height: auto; max-height: "+i+"; white-space: normal; text-overflow: clip;}","> td:hover > .layui-table-cell{overflow: auto;}"].concat(w.ie?[".layui-table-edit{height: "+i+";}","td[data-edit]:hover:after{height: "+i+";}"]:[]),function(e,t){t&&r.push(n+" "+t)})),a.css&&r.push(a.css),d.style({target:this.elem[0],text:r.join(""),id:"DF-table-"+o})},t.prototype.renderToolbar=function(){var a,r=this,e=r.config,l=e.elem.attr("lay-filter"),t=['<div class="layui-inline" lay-event="add"><i class="layui-icon layui-icon-add-1"></i></div>','<div class="layui-inline" lay-event="update"><i class="layui-icon layui-icon-edit"></i></div>','<div class="layui-inline" lay-event="delete"><i class="layui-icon layui-icon-delete"></i></div>'].join(""),n=r.layTool.find(".layui-table-tool-temp"),o=("default"===e.toolbar?n.html(t):"string"==typeof e.toolbar&&(t=g(e.toolbar).html()||"")&&n.html(v(t).render(e)),{filter:{title:"筛选列",layEvent:"LAYTABLE_COLS",icon:"layui-icon-cols",onClick:function(e){var n,o=e.config;(0,e.openPanel)({list:(n=[],r.eachCols(function(e,t){t.field&&"normal"==t.type&&n.push('<li><input type="checkbox" name="'+t.field+'" data-key="'+t.key+'" data-parentkey="'+(t.parentKey||"")+'" lay-skin="primary" '+(t.hide?"":"checked")+' title="'+b.escape(g("<div>"+(t.fieldTitle||t.title||t.field)+"</div>").text())+'" lay-filter="LAY_TABLE_TOOL_COLS"></li>')}),n.join("")),done:function(){i.on("checkbox(LAY_TABLE_TOOL_COLS)",function(e){var e=g(e.elem),t=this.checked,n=e.data("key"),i=r.col(n),a=i.hide,e=e.data("parentkey");i.key&&(i.hide=!t,r.elem.find('*[data-key="'+n+'"]')[t?"removeClass":"addClass"](E),a!=i.hide&&r.setParentCol(!t,e),r.resize(),layui.event.call(this,C,"colToggled("+l+")",{col:i,config:o}))})}})}},exports:{title:"导出",layEvent:"LAYTABLE_EXPORT",icon:"layui-icon-export",onClick:function(e){var t=e.data,n=e.config,i=e.openPanel,e=e.elem;if(!t.length)return x.tips("当前表格无数据",e,{tips:3});w.ie?x.tips("导出功能不支持 IE，请用 Chrome 等高级浏览器导出",e,{tips:3}):i({list:['<li data-type="csv">导出 CSV 文件</li>'].join(""),done:function(e,t){t.on("click",function(){var e=g(this).data("type");k.exportFile.call(r,n.id,null,e)})}})}},print:{title:"打印",layEvent:"LAYTABLE_PRINT",icon:"layui-icon-print",onClick:function(e){var t=e.data,e=(e.config,e.elem);if(!t.length)return x.tips("当前表格无数据",e,{tips:3});var t=window.open("about:blank","_blank"),e=["<style>","body{font-size: 12px; color: #5F5F5F;}","table{width: 100%; border-collapse: collapse; border-spacing: 0;}","th,td{line-height: 20px; padding: 9px 15px; border: 1px solid #ccc; text-align: left; font-size: 12px; color: #5F5F5F;}","a{color: #5F5F5F; text-decoration:none;}","img{max-height: 100%;}","*.layui-hide{display: none}","</style>"].join(""),n=g(r.layHeader.html());n.append(r.layMain.find("table").html()),n.append(r.layTotal.find("table").html()),n.find("th.layui-table-patch").remove(),n.find("thead>tr>th."+j).filter(function(e,t){return!g(t).children("."+u).length}).remove(),n.find("tbody>tr>td."+j).remove(),t.document.write(e+n.prop("outerHTML")),t.document.close(),layui.device("edg").edg?(t.onafterprint=t.close,t.print()):(t.print(),t.close())}}});"object"==typeof e.defaultToolbar&&(a=[],e.defaultToolbar=g.map(e.defaultToolbar,function(e,t){var n="string"==typeof e,i=n?o[e]:e;return i&&(!(i=i.name&&o[i.name]?g.extend({},o[i.name],i):i).name&&n&&(i.name=e),a.push('<div class="layui-inline" title="'+i.title+'" lay-event="'+i.layEvent+'"><i class="layui-icon '+i.icon+'"></i></div>')),i}),r.layTool.find(".layui-table-tool-self").html(a.join("")))},t.prototype.renderPagebar=function(){var e,t=this.config,n=this.layPagebar=g('<div class="layui-inline layui-table-pagebar"></div>');t.pagebar&&((e=g(t.pagebar).html()||"")&&n.append(v(e).render(t)),this.layPage.append(n))},t.prototype.setParentCol=function(e,t){var n=this.config,i=this.layHeader.find('th[data-key="'+t+'"]'),a=parseInt(i.attr("colspan"))||0;i[0]&&(t=t.split("-"),t=n.cols[t[1]][t[2]],e?a--:a++,i.attr("colspan",a),i[a?"removeClass":"addClass"](E),t.colspan2=a,t.hide=a<1,n=i.data("parentkey"))&&this.setParentCol(e,n)},t.prototype.setColsPatch=function(){var n=this,e=n.config;layui.each(e.cols,function(e,t){layui.each(t,function(e,t){t.hide&&n.setParentCol(t.hide,t.parentKey)})})},t.prototype.setGroupWidth=function(i){var e,a=this;a.config.cols.length<=1||((e=a.layHeader.find((i?"th[data-key="+i.data("parentkey")+"]>":"")+"."+u)).css("width",0),layui.each(e.get().reverse(),function(){var e=g(this),t=e.parent().data("key"),n=0;a.layHeader.eq(0).find("th[data-parentkey="+t+"]").width(function(e,t){g(this).hasClass(E)||0<t&&(n+=t)}),n&&e.css("max-width",n-1),i&&e.parent().data("parentkey")&&a.setGroupWidth(e.parent())}),e.css("width","auto"))},t.prototype.setColsWidth=function(){var o,r=this,l=r.config,n=0,s=0,c=0,d=0,u=r.setInit("width"),i=parseFloat(layui.getStyle(r.elem[0],"border-left-width")),e=(r.eachCols(function(e,t){t.hide||(n++,t.width)||"normal"!==t.type||(o=t)}),u=u-("line"===l.skin||"nob"===l.skin?2:n+1)*i-r.getScrollWidth(r.layMain[0]),function(r){layui.each(l.cols,function(e,o){layui.each(o,function(e,t){var n=0,i=t.minWidth||l.cellMinWidth,a=t.maxWidth||l.cellMaxWidth;t?t.colGroup||t.hide||(r?c&&c<i?(s--,n=i):c&&a<c&&(s--,n=a):(n=t.width||0,/\d+%$/.test(n)?a<(n=(n=parseFloat(n)/100*u)<i?i:n)&&(n=a):n?"normal"===t.type&&(n<i&&(t.width=n=i),a<n)&&(t.width=n=a):(t.width=n=0,s++)),t.hide&&(n=0),d+=n):o.splice(e,1)})}),d<u&&0<s&&(c=(u-d)/s)}),f=(e(),e(!0),r.autoColNums=s=0<s?s:0,r.layMain.find("tbody").is(":empty")?(e=r.layHeader.first().children("table").width(),r.layMain.find("table").width(e)):r.layMain.find("table").width("auto"),u);r.eachCols(function(e,n){var i=n.minWidth||l.cellMinWidth,a=n.maxWidth||l.cellMaxWidth;n.colGroup||n.hide||o&&o.key===n.key||(0===n.width?r.cssRules(n.key,function(e){var t=Math.round(c<i?i:a<c?a:c);e.style.width=t+"px",f-=t}):/\d+%$/.test(n.width)?r.cssRules(n.key,function(e){var t=Math.round(parseFloat(n.width)/100*u);e.style.width=(t=a<(t=t<i?i:t)?a:t)+"px",f-=t}):r.cssRules(n.key,function(e){e.style.width=n.width+"px",f-=n.width}))}),o&&r.cssRules(o.key,function(e){var t=o.minWidth||l.cellMinWidth,n=o.maxWidth||l.cellMaxWidth,n=Math.max(Math.min(f,n),t);e.style.width=n+"px",r.layMain.prop("offsetHeight")>r.layMain.prop("clientHeight")&&(e.style.width=parseFloat(e.style.width)-i+"px")}),r.setGroupWidth()},t.prototype.resize=function(){this.layMain&&(this.fullSize(),this.setColsWidth(),this.scrollPatch())},t.prototype.reload=function(e,t,n){var i=this;e=e||{},delete i.haveInit,layui.each(e,function(e,t){"array"===layui.type(t)&&delete i.config[e]}),i.config=g.extend(t,{},i.config,e),"reloadData"!==n&&(layui.each(i.config.cols,function(e,t){layui.each(t,function(e,t){delete t.colspan2})}),delete i.config.HAS_SET_COLS_PATCH),i.render(n)},t.prototype.errorView=function(e){var t=this,n=t.layMain.find("."+D),e=g('<div class="'+D+'">'+(e||"Error")+"</div>");n[0]&&(t.layNone.remove(),n.remove()),t.layFixed.addClass(E),t.layMain.find("tbody").html(""),t.layMain.append(t.layNone=e),t.layTotal.addClass(f),t.layPage.find(q).addClass(f),k.cache[t.key]=[],t.syncCheckAll(),t.renderForm(),t.setColsWidth(),t.loading(!1)},t.prototype.page=1,t.prototype.pullData=function(i,a){function o(){"object"==typeof s.initSort&&l.sort({field:s.initSort.field,type:s.initSort.type,reloadType:a.type})}function r(e,t){l.setColsWidth(),l.loading(!1),"function"==typeof s.done&&s.done(e,i,e[c.countName],t)}var e,t,l=this,s=l.config,n=(s.HAS_SET_COLS_PATCH||l.setColsPatch(),s.HAS_SET_COLS_PATCH=!0,s.request),c=s.response;a=a||{},"function"==typeof s.before&&s.before(s),l.startTime=(new Date).getTime(),a.renderData?((e={})[c.dataName]=k.cache[l.key],e[c.countName]=s.url?"object"===layui.type(s.page)?s.page.count:e[c.dataName].length:s.data.length,"object"==typeof s.totalRow&&(e[c.totalRowName]=g.extend({},l.totalRow)),l.renderData({res:e,curr:i,count:e[c.countName],type:a.type,sort:!0}),r(e,"renderData")):s.url?(t={},s.page&&(t[n.pageName]=i,t[n.limitName]=s.limit),n=g.extend(t,s.where),s.contentType&&0==s.contentType.indexOf("application/json")&&(n=JSON.stringify(n)),l.loading(!0),g.ajax({type:s.method||"get",url:s.url,contentType:s.contentType,data:n,dataType:s.dataType||"json",jsonpCallback:s.jsonpCallback,headers:s.headers||{},complete:"function"==typeof s.complete?s.complete:void 0,success:function(e){var t,n;(e="function"==typeof s.parseData&&s.parseData(e)||e)[c.statusName]!=c.statusCode?l.errorView(e[c.msgName]||'返回的数据不符合规范，正确的成功状态码应为："'+c.statusName+'": '+c.statusCode):(t=e[c.countName],(n=Math.ceil(t/s.limit)||1)<i&&(i=n),l.totalRow=e[c.totalRowName],l.renderData({res:e,curr:i,count:t,type:a.type}),o(),s.time=(new Date).getTime()-l.startTime+" ms"),r(e,a.type)},error:function(e,t){l.errorView("请求异常，错误提示："+t),"function"==typeof s.error&&s.error(e,t)}})):"array"===layui.type(s.data)&&(e={},t=i*s.limit-s.limit,n=s.data.concat(),e[c.dataName]=s.page?n.splice(t,s.limit):n,e[c.countName]=s.data.length,"object"==typeof s.totalRow&&(e[c.totalRowName]=g.extend({},s.totalRow)),l.totalRow=e[c.totalRowName],l.renderData({res:e,curr:i,count:e[c.countName],type:a.type}),o(),r(e,a.type))},t.prototype.eachCols=function(e){return k.eachCols(null,e,this.config.cols),this},t.prototype.col=function(e){try{return e=e.split("-"),this.config.cols[e[1]][e[2]]||{}}catch(e){return p.error(e),{}}},t.prototype.getTrHtml=function(t,n,a,e){var d=this,u=d.config,o=e&&e.trs||[],f=e&&e.trs_fixed||[],p=e&&e.trs_fixed_r||[];return a=a||1,layui.each(t,function(e,r){var i=[],l=[],s=[],c=e+u.limit*(a-1)+1;if("object"!=typeof r){t[e]=r={LAY_KEY:r};try{k.cache[d.key][e]=r}catch(e){}}"array"===layui.type(r)&&0===r.length||(r[k.config.numbersName]=c,n||(r[k.config.indexName]=e),d.eachCols(function(e,a){var t,e=a.field||e,n=a.key,o=r[e];null==o&&(o=""),a.colGroup||(e=['<td data-field="'+e+'" data-key="'+n+'" '+(e=[],(t="function"==typeof a.edit?a.edit(r):a.edit)&&e.push('data-edit="'+t+'"'),a.templet&&e.push('data-content="'+b.escape(o)+'"'),a.toolbar&&e.push('data-off="true"'),a.event&&e.push('lay-event="'+a.event+'"'),a.minWidth&&e.push('data-minwidth="'+a.minWidth+'"'),a.maxWidth&&e.push('data-maxwidth="'+a.maxWidth+'"'),a.style&&e.push('style="'+a.style+'"'),e.join(" "))+' class="'+(t=[],a.hide&&t.push(E),a.field||t.push(j),t.join(" "))+'">','<div class="layui-table-cell laytable-cell-'+("normal"===a.type?n:n+" laytable-cell-"+a.type)+'"'+(a.align?' align="'+a.align+'"':"")+">"+function(){var e,t=g.extend(!0,{LAY_COL:a},r),n=k.config.checkName,i=k.config.disabledName;switch(a.type){case"checkbox":return'<input type="checkbox" name="layTableCheckbox" lay-skin="primary" '+(e=[],a[n]&&(r[n]=a[n],a[n])&&(e[0]="checked"),t[n]&&(e[0]="checked"),t[i]&&e.push("disabled"),e.join(" "))+' lay-type="layTableCheckbox">';case"radio":return'<input type="radio" name="layTableRadio_'+u.index+'" '+(e=[],t[n]&&(e[0]="checked"),t[i]&&e.push("disabled"),e.join(" "))+' lay-type="layTableRadio">';case"numbers":return c}return a.toolbar?v(g(a.toolbar).html()||"").render(t):m.call(d,{item3:a,content:o,tplData:t})}(),"</div></td>"].join(""),i.push(e),a.fixed&&"right"!==a.fixed&&l.push(e),"right"===a.fixed&&s.push(e))}),e=['data-index="'+e+'"'],r[k.config.checkName]&&e.push('class="'+A+'"'),e=e.join(" "),o.push("<tr "+e+">"+i.join("")+"</tr>"),f.push("<tr "+e+">"+l.join("")+"</tr>"),p.push("<tr "+e+">"+s.join("")+"</tr>"))}),{trs:o,trs_fixed:f,trs_fixed_r:p}},k.getTrHtml=function(e,t){return(e=y(e)).getTrHtml(t,null,e.page)},t.prototype.renderData=function(e){function t(){if(!l&&n.sortKey)return n.sort({field:n.sortKey.field,type:n.sortKey.sort,pull:!0,reloadType:e.type});n.getTrHtml(s,l,o,{trs:c,trs_fixed:d,trs_fixed_r:u}),"fixed"===i.scrollPos&&"reloadData"===e.type||n.layBody.scrollTop(0),"reset"===i.scrollPos&&n.layBody.scrollLeft(0),n.layMain.find("."+D).remove(),n.layMain.find("tbody").html(c.join("")),n.layFixLeft.find("tbody").html(d.join("")),n.layFixRight.find("tbody").html(u.join("")),n.syncCheckAll(),n.renderForm(),n.fullSize(),n.haveInit?n.scrollPatch():setTimeout(function(){n.scrollPatch()},50),n.haveInit=!0,x.close(n.tipsIndex)}var n=this,i=n.config,a=e.res,o=e.curr,r=n.count=e.count,l=e.sort,s=a[i.response.dataName]||[],a=a[i.response.totalRowName],c=[],d=[],u=[];return k.cache[n.key]=s,n.layTotal[0==s.length?"addClass":"removeClass"](f),n.layPage[i.page||i.pagebar?"removeClass":"addClass"](E),n.layPage.find(q)[!i.page||0==r||0===s.length&&1==o?"addClass":"removeClass"](f),0===s.length?n.errorView(i.text.none):(n.layFixLeft.removeClass(E),l?t():(t(),n.renderTotal(s,a),n.layTotal&&n.layTotal.removeClass(E),void(i.page&&(i.page=g.extend({elem:"layui-table-page"+i.index,count:r,limit:i.limit,limits:i.limits||[10,20,30,40,50,60,70,80,90],groups:3,layout:["prev","page","next","skip","count","limit"],prev:'<i class="layui-icon">&#xe603;</i>',next:'<i class="layui-icon">&#xe602;</i>',jump:function(e,t){t||(n.page=e.curr,i.limit=e.limit,n.pullData(e.curr))}},i.page),i.page.count=r,R.render(i.page)))))},k.renderData=function(e){(e=y(e))&&e.pullData(e.page,{renderData:!0,type:"reloadData"})},t.prototype.renderTotal=function(e,r){var l,s=this,c=s.config,d={};c.totalRow&&(layui.each(e,function(e,i){"array"===layui.type(i)&&0===i.length||s.eachCols(function(e,t){var e=t.field||e,n=i[e];t.totalRow&&(d[e]=(d[e]||0)+(parseFloat(n)||0))})}),s.dataTotal=[],l=[],s.eachCols(function(e,t){var n,e=t.field||e,i=r&&r[t.field],a="totalRowDecimals"in t?t.totalRowDecimals:2,a=d[e]?parseFloat(d[e]||0).toFixed(a):"",a=(n=t.totalRowText||"",(o={LAY_COL:t})[e]=a,o=t.totalRow&&m.call(s,{item3:t,content:a,tplData:o})||n,i||o),o=(t.field&&s.dataTotal.push({field:t.field,total:g("<div>"+a+"</div>").text()}),['<td data-field="'+e+'" data-key="'+t.key+'" '+(n=[],t.minWidth&&n.push('data-minwidth="'+t.minWidth+'"'),t.maxWidth&&n.push('data-maxwidth="'+t.maxWidth+'"'),t.style&&n.push('style="'+t.style+'"'),n.join(" "))+' class="'+(o=[],t.hide&&o.push(E),t.field||o.push(j),o.join(" "))+'">','<div class="layui-table-cell laytable-cell-'+(n=t.key,"normal"===t.type?n:n+" laytable-cell-"+t.type)+'"'+(o=[],t.align&&o.push('align="'+t.align+'"'),o.join(" "))+">"+("string"==typeof(n=t.totalRow||c.totalRow)?v(n).render(g.extend({TOTAL_NUMS:i||d[e],TOTAL_ROW:r||{},LAY_COL:t},t)):a),"</div></td>"].join(""));l.push(o)}),e=s.layTotal.find(".layui-table-patch"),s.layTotal.find("tbody").html("<tr>"+l.join("")+(e.length?e.get(0).outerHTML:"")+"</tr>"))},t.prototype.getColElem=function(e,t){return e.eq(0).find(".laytable-cell-"+t+":eq(0)")},t.prototype.renderForm=function(e){this.config;var t=this.elem.attr("lay-filter");i.render(e,t)},t.prototype.renderFormByElem=function(n){layui.each(["input","select"],function(e,t){i.render(n.find(t))})},t.prototype.syncCheckAll=function(){var n,i=this.config,e=this.layHeader.find('input[name="layTableCheckbox"]'),t=k.checkStatus(this.key);e[0]&&(n=t.isAll,this.eachCols(function(e,t){"checkbox"===t.type&&(t[i.checkName]=n)}),e.prop({checked:t.isAll,indeterminate:!t.isAll&&t.data.length}))},t.prototype.setRowActive=function(e,t,n){if(this.config,e=this.layBody.find('tr[data-index="'+e+'"]'),t=t||"layui-table-click",n)return e.removeClass(t);e.addClass(t),e.siblings("tr").removeClass(t)},t.prototype.setRowChecked=function(i){var n,e,a,t,o,r,l,s=this,c=s.config,d="all"===i.index,u="array"===layui.type(i.index),f=d||u;f&&(s.layBox.addClass(B),"radio"===i.type)||(u&&(n={},layui.each(i.index,function(e,t){n[t]=!0}),i.index=n),e=s.layBody.children(".layui-table").children("tbody"),l=f?"tr":'tr[data-index="'+i.index+'"]',l=e.children(l),e=d?l:l.filter(u?function(){var e=g(this).data("index");return i.index[e]}:'[data-index="'+i.index+'"]'),i=g.extend({type:"checkbox"},i),a=k.cache[s.key],t="checked"in i,o=function(e){return"radio"===i.type||(t?i.checked:!e)},e.each(function(){var e=g(this),t=e.attr("data-index"),n=a[t];t&&"array"!==layui.type(n)&&!n[c.disabledName]&&(n=n[c.checkName]=o(e.hasClass(A)),e.toggleClass(A,!!n),"radio"===i.type)&&(r=t,e.siblings().removeClass(A))}),r&&layui.each(a,function(e,t){Number(r)!==Number(e)&&delete t[c.checkName]}),l=(u=(d=e.children("td").children(".layui-table-cell").children('input[lay-type="'+({radio:"layTableRadio",checkbox:"layTableCheckbox"}[i.type]||"checkbox")+'"]:not(:disabled)')).last()).closest(P),("radio"===i.type&&l.hasClass(E)?d.first():d).prop("checked",o(u.prop("checked"))),s.syncCheckAll(),f&&setTimeout(function(){s.layBox.removeClass(B)},100))},t.prototype.sort=function(a){var e,t=this,n={},i=t.config,o=i.elem.attr("lay-filter"),r=k.cache[t.key];"string"==typeof(a=a||{}).field&&(l=a.field,t.layHeader.find("th").each(function(e,t){var n=g(this),i=n.data("field");if(i===a.field)return a.field=n,l=i,!1}));try{var l=l||a.field.data("field"),s=a.field.data("key");if(t.sortKey&&!a.pull&&l===t.sortKey.field&&a.type===t.sortKey.sort)return;var c=t.layHeader.find("th .laytable-cell-"+s).find(L);t.layHeader.find("th").find(L).removeAttr("lay-sort"),c.attr("lay-sort",a.type||null),t.layFixed.find("th")}catch(e){p.error("Table modules: sort field '"+l+"' not matched")}t.sortKey={field:l,sort:a.type},i.autoSort&&("asc"===a.type?e=layui.sort(r,l,null,!0):"desc"===a.type?e=layui.sort(r,l,!0,!0):(e=layui.sort(r,k.config.indexName,null,!0),delete t.sortKey,delete i.initSort)),n[i.response.dataName]=e||r,t.renderData({res:n,curr:t.page,count:t.count,sort:!0,type:a.reloadType}),a.fromEvent&&(i.initSort={field:l,type:a.type},layui.event.call(a.field,C,"sort("+o+")",g.extend({config:i},i.initSort)))},t.prototype.loading=function(e){this.config.loading&&this.layBox.find(".layui-table-init").toggleClass(E,!e)},t.prototype.cssRules=function(t,n){var e=this.elem.children("style")[0];d.getStyleRules(e,function(e){if(e.selectorText===".laytable-cell-"+t)return n(e),!0})},t.prototype.fullSize=function(){var e,n,i=this,t=i.config,a=t.height;i.fullHeightGap?(a=l.height()-i.fullHeightGap)<135&&(a=135):i.parentDiv&&i.parentHeightGap?(a=g(i.parentDiv).height()-i.parentHeightGap)<135&&(a=135):i.customHeightFunc&&(a=i.customHeightFunc())<135&&(a=135),1<t.cols.length&&(e=i.layFixed.find(r).find("th"),n=i.layHeader.first(),layui.each(e,function(e,t){(t=g(t)).height(n.find('th[data-key="'+t.attr("data-key")+'"]').height()+"px")})),a&&(e=parseFloat(a)-(i.layHeader.outerHeight()||39),t.toolbar&&(e-=i.layTool.outerHeight()||51),t.totalRow&&(e-=i.layTotal.outerHeight()||40),(t.page||t.pagebar)&&(e-=i.layPage.outerHeight()||43),t.maxHeight?layui.each({elem:a,layMain:e},function(e,t){i[e].css({height:"auto",maxHeight:t+"px"})}):i.layMain.outerHeight(e))},t.prototype.getScrollWidth=function(e){var t;return e?t=e.offsetWidth-e.clientWidth:((e=document.createElement("div")).style.width="100px",e.style.height="100px",e.style.overflowY="scroll",document.body.appendChild(e),t=e.offsetWidth-e.clientWidth,document.body.removeChild(e)),t},t.prototype.scrollPatch=function(){var e=this,t=e.layMain.children("table"),n=e.layMain.width()-e.layMain.prop("clientWidth"),i=e.layMain.height()-e.layMain.prop("clientHeight"),a=(e.getScrollWidth(e.layMain[0]),t.outerWidth()-e.layMain.width()),o=function(e){var t;n&&i?(e=e.eq(0)).find(".layui-table-patch")[0]||((t=g('<th class="layui-table-patch"><div class="layui-table-cell"></div></th>')).find("div").css({width:n}),e.find("tr").append(t)):e.find(".layui-table-patch").remove()};o(e.layHeader),o(e.layTotal),o=e.layMain.height()-i,e.layFixed.find(N).css("height",t.height()>=o?o:"auto").scrollTop(e.layMain.scrollTop()),e.layFixRight[k.cache[e.key]&&k.cache[e.key].length&&0<a?"removeClass":"addClass"](E),e.layFixRight.css("right",n-1)},t.prototype.updateRow=function(e,i){var s=this,e="array"===layui.type(e)?e:[e],c=k.cache[s.key]||[];layui.each(e,function(e,t){var n=t.index,a=t.data,o=t.related,r=c[n]||{},l=s.layBody.find('tr[data-index="'+n+'"]');layui.each(a,function(e,t){r[e]=t,i&&i(e,t)}),s.eachCols(function(e,t){var n,i=String(t.field||e);(i in a||("function"==typeof o?o(i,e):o)&&(t.templet||t.toolbar))&&(i=(e=l.children('td[data-field="'+i+'"]')).children(".layui-table-cell"),n=r[t.field],i.html(m.call(s,{item3:t,content:n,tplData:g.extend({LAY_COL:t},r)})),e.data("content",n),s.renderFormByElem(i))})})},k.updateRow=function(e,t){return y(e).updateRow(t)},t.prototype.events=function(){function o(e,t){var n,i,a;(e=g(e)).data("off")||(a=e.data("field"),i=e.data("key"),i=c.col(i),n=e.closest("tr").data("index"),n=k.cache[c.key][n],e.children(u),(i="function"==typeof i.edit?i.edit(n):i.edit)&&((i=g("textarea"===i?'<textarea class="layui-input '+I+'" lay-unrow></textarea>':'<input class="layui-input '+I+'" lay-unrow>'))[0].value=null==(a=e.data("content")||n[a])?"":a,e.find("."+I)[0]||e.append(i),i.focus(),t)&&layui.stope(t))}function t(e,t){var n=g(this),i=n.parent(),a=i.data("key"),o=c.col(a),r=i.parent().data("index"),l=i.children(u),s=g('<i class="layui-icon layui-icon-up '+(i="layui-table-cell-c")+'">');"tips"===(t=t||o.expandedMode||d.cellExpandedMode)?c.tipsIndex=x.tips(['<div class="layui-table-tips-main" style="margin-top: -'+(l.height()+23)+"px;"+("sm"===d.size?"padding: 4px 15px; font-size: 12px;":"lg"===d.size?"padding: 14px 15px;":"")+'">',l.html(),"</div>",'<i class="layui-icon layui-table-tips-c layui-icon-close"></i>'].join(""),l[0],{tips:[3,""],time:-1,anim:-1,maxWidth:w.ios||w.android?300:c.elem.width()/2,isOutAnim:!1,skin:"layui-table-tips",success:function(e,t){e.find(".layui-table-tips-c").on("click",function(){x.close(t)})}}):(c.elem.find("."+i).trigger("click"),c.cssRules(a,function(e){var t=e.style.width,n=o.expandedWidth||d.cellExpandedWidth;n<parseFloat(t)&&(n=parseFloat(t)),s.data("cell-width",t),e.style.width=n+"px",setTimeout(function(){c.scrollPatch()})}),c.setRowActive(r,H),l.next("."+i)[0]||l.after(s),s.on("click",function(){var t=g(this);c.setRowActive(r,[H,M].join(" "),!0),c.cssRules(a,function(e){e.style.width=t.data("cell-width"),setTimeout(function(){c.resize()})}),t.remove(),l.scrollTop(0),l.scrollLeft(0)})),n.remove(),layui.stope(e)}function n(e){var t=g(this),n=t.closest("td"),i=t.parents("tr").eq(0).data("index");c.setRowActive(i),layui.event.call(this,C,(e||"tool")+"("+r+")",f.call(this,{event:t.attr("lay-event"),getCol:function(){return c.col(n.data("key"))}}))}var c=this,d=c.config,r=d.elem.attr("lay-filter"),e=c.layHeader.find("th"),u=".layui-table-cell",l=g("body"),s={},f=(c.layTool.on("click","*[lay-event]",function(e){function n(e){var t=g(e.list),n=g('<ul class="'+_+'"></ul>');n.html(t),d.height&&n.css("max-height",d.height-(c.layTool.outerHeight()||50)),i.find("."+_)[0]||i.append(n),c.renderForm(),n.on("click",function(e){layui.stope(e)}),e.done&&e.done(n,t)}var i=g(this),a=i.attr("lay-event"),o=k.cache[d.id];layui.stope(e),O.trigger("table.tool.panel.remove"),x.close(c.tipsIndex),layui.each(d.defaultToolbar,function(e,t){if(t.layEvent===a)return"function"==typeof t.onClick&&t.onClick({data:o,config:d,openPanel:n,elem:i}),!0}),layui.event.call(this,C,"toolbar("+r+")",g.extend({event:a,config:d},{}))}),c.layHeader.on("click","*[lay-event]",function(e){var t=(n=g(this)).attr("lay-event"),n=n.closest("th").data("key"),n=c.col(n);layui.event.call(this,C,"colTool("+r+")",g.extend({event:t,config:d,col:n},{}))}),c.layPagebar.on("click","*[lay-event]",function(e){var t=g(this).attr("lay-event");layui.event.call(this,C,"pagebar("+r+")",g.extend({event:t,config:d},{}))}),e.on("mousemove",function(e){var t=g(this),n=t.offset().left,e=e.clientX-n;t.data("unresize")||h.eventMoveElem||(s.allowResize=t.width()-e<=10,l.css("cursor",s.allowResize?"col-resize":""))}).on("mouseleave",function(){g(this),h.eventMoveElem||(s.allowResize=!1,l.css("cursor",""))}).on("mousedown",function(e){var t,n=g(this);s.allowResize&&(t=n.data("key"),e.preventDefault(),s.offset=[e.clientX,e.clientY],c.cssRules(t,function(e){var t=e.style.width||n.outerWidth();s.rule=e,s.ruleWidth=parseFloat(t),s.minWidth=n.data("minwidth")||d.cellMinWidth,s.maxWidth=n.data("maxwidth")||d.cellMaxWidth}),n.data(F,s),h.eventMoveElem=n)}),h.docEvent||O.on("mousemove",function(e){var t,n;h.eventMoveElem&&(t=h.eventMoveElem.data(F)||{},h.eventMoveElem.data("resizing",1),e.preventDefault(),t.rule)&&(e=t.ruleWidth+e.clientX-t.offset[0],n=h.eventMoveElem.closest("."+S).attr(T),n=y(n))&&((e=e<t.minWidth?t.minWidth:e)>t.maxWidth&&(e=t.maxWidth),t.rule.style.width=e+"px",n.setGroupWidth(h.eventMoveElem),x.close(c.tipsIndex))}).on("mouseup",function(e){var t,n,i,a,o;h.eventMoveElem&&(i=(t=h.eventMoveElem).closest("."+S).attr(T),n=y(i))&&(i=t.data("key"),a=n.col(i),o=n.config.elem.attr("lay-filter"),s={},l.css("cursor",""),n.scrollPatch(),t.removeData(F),delete h.eventMoveElem,n.cssRules(i,function(e){a.width=parseFloat(e.style.width),layui.event.call(t[0],C,"colResized("+o+")",{col:a,config:n.config})}))}),h.docEvent=!0,e.on("click",function(e){var t=g(this),n=t.find(L),i=n.attr("lay-sort");if(!n[0]||1===t.data("resizing"))return t.removeData("resizing");c.sort({field:t,type:"asc"===i?"desc":"desc"===i?null:"asc",fromEvent:!0})}).find(L+" .layui-edge ").on("click",function(e){var t=(n=g(this)).index(),n=n.parents("th").eq(0).data("field");layui.stope(e),0===t?c.sort({field:n,type:"asc",fromEvent:!0}):c.sort({field:n,type:"desc",fromEvent:!0})}),c.commonMember=function(e){var n=g(this).parents("tr").eq(0).data("index"),t=c.layBody.find('tr[data-index="'+n+'"]'),i=(k.cache[c.key]||[])[n]||{},a={tr:t,config:d,data:k.clearCacheKey(i),dataCache:i,index:n,del:function(){k.cache[c.key][n]=[],t.remove(),c.scrollPatch()},update:function(e,t){c.updateRow({index:n,data:e=e||{},related:t},function(e,t){a.data[e]=t})},setRowChecked:function(e){c.setRowChecked(g.extend({index:n},e))}};return g.extend(a,e)}),i=(c.elem.on("click",'input[name="layTableCheckbox"]+',function(e){var t=(n=g(this)).closest("td"),n=n.prev(),i=(c.layBody.find('input[name="layTableCheckbox"]'),n.parents("tr").eq(0).data("index")),a=n[0].checked,o="layTableAllChoose"===n.attr("lay-filter");n[0].disabled||(o?c.setRowChecked({index:"all",checked:a}):c.setRowChecked({index:i,checked:a}),layui.stope(e),layui.event.call(n[0],C,"checkbox("+r+")",f.call(n[0],{checked:a,type:o?"all":"one",getCol:function(){return c.col(t.data("key"))}})))}),c.elem.on("click",'input[lay-type="layTableRadio"]+',function(e){var t=g(this),n=t.closest("td"),i=(t=t.prev())[0].checked,a=t.parents("tr").eq(0).data("index");if(layui.stope(e),t[0].disabled)return!1;c.setRowChecked({type:"radio",index:a}),layui.event.call(t[0],C,"radio("+r+")",f.call(t[0],{checked:i,getCol:function(){return c.col(n.data("key"))}}))}),c.layBody.on("mouseenter","tr",function(){var e=g(this),t=e.index();e.data("off")||c.layBody.find("tr:eq("+t+")").addClass(M)}).on("mouseleave","tr",function(){var e=g(this),t=e.index();e.data("off")||c.layBody.find("tr:eq("+t+")").removeClass(M)}).on("click","tr",function(e){i.call(this,"row",e)}).on("dblclick","tr",function(e){i.call(this,"rowDouble",e)}).on("contextmenu","tr",function(e){d.defaultContextmenu||e.preventDefault(),i.call(this,"rowContextmenu",e)}),function(e,t){var n=g(this);if(!n.data("off")){if("rowContextmenu"!==e){var i=[".layui-form-checkbox",".layui-form-switch",".layui-form-radio","[lay-unrow]"].join(",");if(g(t.target).is(i)||g(t.target).closest(i)[0])return}layui.event.call(this,C,e+"("+r+")",f.call(n.children("td")[0],{e:t}))}}),a=(c.layBody.on("change","."+I,function(){var e=(i=g(this)).parent(),t=this.value,n=i.parent().data("field"),i=i.closest("tr").data("index"),i=k.cache[c.key][i],a=f.call(e[0],{value:t,field:n,oldValue:i[n],td:e,reedit:function(){setTimeout(function(){o(a.td);var e={};e[n]=a.oldValue,a.update(e)})},getCol:function(){return c.col(e.data("key"))}});(i={})[n]=t,a.update(i),layui.event.call(e[0],C,"edit("+r+")",a)}).on("blur","."+I,function(){g(this).remove()}),c.layBody.on(d.editTrigger,"td",function(e){o(this,e)}).on("mouseenter","td",function(){p.call(this)}).on("mouseleave","td",function(){p.call(this,"hide")}),c.layTotal.on("mouseenter","td",function(){p.call(this)}).on("mouseleave","td",function(){p.call(this,"hide")}),"layui-table-grid-down"),p=function(e){var t=g(this),n=t.children(u);t.data("off")||t.parent().hasClass(H)||(e?t.find(".layui-table-grid-down").remove():!(n.prop("scrollWidth")>n.prop("clientWidth")||0<n.find("br").length)||d.lineStyle||n.find("."+a)[0]||t.append('<div class="'+a+'"><i class="layui-icon layui-icon-down"></i></div>'))};c.layBody.on("click","."+a,function(e){t.call(this,e)}),c.layTotal.on("click","."+a,function(e){t.call(this,e,"tips")});c.layBody.on("click","*[lay-event]",function(e){n.call(this),layui.stope(e)}).on("dblclick","*[lay-event]",function(e){n.call(this,"toolDouble"),layui.stope(e)}),c.layMain.on("scroll",function(){var e=(t=g(this)).scrollLeft(),t=t.scrollTop();c.layHeader.scrollLeft(e),c.layTotal.scrollLeft(e),c.layFixed.find(N).scrollTop(t),x.close(c.tipsIndex)}),c.layFixed.find(N).on("mousewheel DOMMouseScroll",function(e){var t=e.originalEvent.wheelDelta||-e.originalEvent.detail,n=c.layMain.scrollTop();e.preventDefault(),c.layMain.scrollTop(n+(0<t?-30:30))})},t.prototype.getElementSize=function(e){if(window.getComputedStyle)return e=window.getComputedStyle(e,null),{height:parseFloat(e.height||"0"),width:parseFloat(e.width||"0"),borderTopWidth:parseFloat(e.borderTopWidth||"0"),borderRightWidth:parseFloat(e.borderRightWidth||"0"),borderBottomWidth:parseFloat(e.borderBottomWidth||"0"),borderLeftWidth:parseFloat(e.borderLeftWidth||"0"),paddingTop:parseFloat(e.paddingTop||"0"),paddingRight:parseFloat(e.paddingRight||"0"),paddingBottom:parseFloat(e.paddingBottom||"0"),paddingLeft:parseFloat(e.paddingLeft||"0"),marginTop:parseFloat(e.marginTop||"0"),marginRight:parseFloat(e.marginRight||"0"),marginBottom:parseFloat(e.marginBottom||"0"),marginLeft:parseFloat(e.marginLeft||"0"),boxSizing:e.boxSizing}},l.on("resize",function(){layui.each(h.that,function(){this.resize()})}),O.on("click",function(){O.trigger("table.remove.tool.panel")}),O.on("table.remove.tool.panel",function(){g("."+_).remove()}),k.init=function(i,r){r=r||{};var e="object"==typeof i?i:g("string"==typeof i?'table[lay-filter="'+i+'"]':n+"[lay-data], "+n+"[lay-options]"),l="Table element property lay-data configuration item has a syntax error: ";return e.each(function(){var a,e=g(this),t=e.attr("lay-data"),t=d.options(this,{attr:t?"lay-data":null,errorText:l+(t||e.attr("lay-options"))}),o=g.extend({elem:this,cols:[],data:[],skin:e.attr("lay-skin"),size:e.attr("lay-size"),even:"string"==typeof e.attr("lay-even")},k.config,r,t),n=(i&&e.hide(),e.find("thead>tr").each(function(i){o.cols[i]=[],g(this).children().each(function(e){var t=(n=g(this)).attr("lay-data"),t=d.options(this,{attr:t?"lay-data":null,errorText:l+(t||n.attr("lay-options"))}),n=g.extend({title:n.text(),colspan:parseInt(n.attr("colspan"))||1,rowspan:parseInt(n.attr("rowspan"))||1},t);o.cols[i].push(n)})}),e.find("tbody>tr")),t=k.render(o);!n.length||r.data||t.config.url||(a=0,k.eachCols(t.config.id,function(e,i){n.each(function(e){o.data[e]=o.data[e]||{};var t=g(this),n=i.field;o.data[e][n]=t.children("td").eq(a).html()}),a++}),t.reloadData({data:o.data}))}),this},h.that={},h.config={},function(n,i,e,a){var o,r;a.colGroup&&(o=0,n++,a.CHILD_COLS=[],r=e+(parseInt(a.rowspan)||1),layui.each(i[r],function(e,t){t.parentKey?t.parentKey===a.key&&(t.PARENT_COL_INDEX=n,a.CHILD_COLS.push(t),s(n,i,r,t)):t.PARENT_COL_INDEX||1<=o&&o==(a.colspan||1)||(t.PARENT_COL_INDEX=n,a.CHILD_COLS.push(t),o+=parseInt(1<t.colspan?t.colspan:1),s(n,i,r,t))}))});k.eachCols=function(e,n,i){var e=h.config[e]||{},a=[],o=(i=g.extend(!0,[],i||e.cols),layui.each(i,function(n,e){if(n)return!0;layui.each(e,function(e,t){s(0,i,n,t),t.PARENT_COL_INDEX||a.push(t)})}),function(e){layui.each(e||a,function(e,t){if(t.CHILD_COLS)return o(t.CHILD_COLS);"function"==typeof n&&n(e,t)})});o()},k.checkStatus=function(e){var n=0,i=[],a=[],e=k.cache[e]||[];return layui.each(e,function(e,t){"array"===layui.type(t)||t[k.config.disabledName]?n++:t[k.config.checkName]&&(i.push(k.clearCacheKey(t)),a.push(t))}),{data:i,dataCache:a,isAll:!(!e.length||!i.length)&&i.length===e.length-n}},k.setRowChecked=function(e,t){(e=y(e))&&e.setRowChecked(t)},k.getData=function(e){var n=[],e=k.cache[e]||[];return layui.each(e,function(e,t){"array"!==layui.type(t)&&n.push(k.clearCacheKey(t))}),n},k.resize=function(e){e?a(e)&&y(e).resize():layui.each(h.that,function(){this.resize()})},k.exportFile=function(e,t,n){t=t||k.clearCacheKey(k.cache[e]);var i=(n="object"==typeof n?n:(i={},n&&(i.type=n),i)).type||"csv",r=h.that[e],a=h.config[e]||{},o={csv:"text/csv",xls:"application/vnd.ms-excel"}[i],l=document.createElement("a");if(w.ie)return p.error("IE_NOT_SUPPORT_EXPORTS");var s,c,d,u,f=a.tree&&a.tree.view;if(f)try{t=g.extend(!0,[],k.cache[e]),t=function i(e){return e.reduce(function(e,t){var n=t.children||[];return delete t.children,e.concat(t,i(n))},[])}(Array.from(t))}catch(e){}l.href="data:"+o+";charset=utf-8,\ufeff"+encodeURIComponent((s=[],c=[],d=[],u={},layui.each(t,function(i,a){var o=[];"object"==typeof e?(layui.each(e,function(e,t){0==i&&s.push(t||"")}),layui.each(layui.isArray(a)?g.extend([],a):k.clearCacheKey(a),function(e,t){o.push('"'+(t||"")+'"')})):k.eachCols(e,function(e,t){var n;!1===t.ignoreExport||t.field&&"normal"==t.type?t.hide&&!1!==t.ignoreExport||!0===t.ignoreExport?0==i&&(u[t.field]=!0):(null==(n=a[t.field])&&(n=""),0==i&&s.push(t.fieldTitle||t.title||t.field||""),n=(n=m.call(r,{item3:t,content:n,tplData:a,text:"text",obj:{td:function(e){return f&&(i=a.LAY_DATA_INDEX),r.layBody.find('tr[data-index="'+i+'"]>td').filter('[data-field="'+e+'"]')}}})).replace(/"/g,'""'),o.push(n='"'+n+'"')):t.field&&"normal"!==t.type&&0==i&&(u[t.field]=!0)}),c.push(o.join(","))}),r&&layui.each(r.dataTotal,function(e,t){u[t.field]||d.push('"'+(t.total||"")+'"')}),s.join(",")+"\r\n"+c.join("\r\n")+"\r\n"+d.join(","))),l.download=(n.title||a.title||"table_"+(a.index||""))+"."+i,document.body.appendChild(l),l.click(),document.body.removeChild(l)},k.getOptions=a,k.hideCol=function(e,a){var o=y(e);o&&("boolean"===layui.type(a)?o.eachCols(function(e,t){var n=t.key,i=o.col(n),t=t.parentKey;i.hide!=a&&(i=i.hide=a,o.elem.find('*[data-key="'+n+'"]')[i?"addClass":"removeClass"](E),o.setParentCol(i,t))}):(a=layui.isArray(a)?a:[a],layui.each(a,function(e,a){o.eachCols(function(e,t){var n,i;a.field===t.field&&(n=t.key,i=o.col(n),t=t.parentKey,"hide"in a)&&i.hide!=a.hide&&(i=i.hide=!!a.hide,o.elem.find('*[data-key="'+n+'"]')[i?"addClass":"removeClass"](E),o.setParentCol(i,t))})})),g("."+_).remove(),o.resize())},k.reload=function(e,t,n,i){if(a(e))return(e=y(e)).reload(t,n,i),h.call(e)},k.reloadData=function(){var n=g.extend([],arguments),i=(n[3]="reloadData",new RegExp("^("+["elem","id","cols","width","height","maxHeight","toolbar","defaultToolbar","className","css","pagebar"].join("|")+")$"));return layui.each(n[1],function(e,t){i.test(e)&&delete n[1][e]}),k.reload.apply(null,n)},k.render=function(e){return e=new t(e),h.call(e)},k.clearCacheKey=function(e){return delete(e=g.extend({},e))[k.config.checkName],delete e[k.config.indexName],delete e[k.config.numbersName],delete e[k.config.disabledName],e},g(function(){k.init()}),c(C,k)}),layui.define(["table"],function(e){"use strict";function M(e){var t=r.that[e];return t||p.error(e?"The treeTable instance with ID '"+e+"' not found":"ID argument required"),t||null}function t(e){this.index=++O.index,this.config=H.extend(!0,{},this.config,O.config,e),this.init(),this.render()}function a(r,n,e){var l=M(r),s=("reloadData"!==e&&(l.status={expand:{}}),H.extend(!0,{},l.getOptions(),n)),i=s.tree,c=i.customName.children,a=i.customName.id,o=(delete n.hasNumberCol,delete n.hasChecboxCol,delete n.hasRadioCol,F.eachCols(null,function(e,t){"numbers"===t.type?n.hasNumberCol=!0:"checkbox"===t.type?n.hasChecboxCol=!0:"radio"===t.type&&(n.hasRadioCol=!0)},s.cols),n.parseData),d=n.done;"reloadData"===e&&"fixed"===s.scrollPos&&(l.scrollTopCache=l.config.elem.next().find(u).scrollTop()),s.url?e&&(!o||o.mod)||(n.parseData=function(){var e=(t=arguments)[0],t=("function"===layui.type(o)&&(e=o.apply(this,t)||t[0]),this.response.dataName);return i.data.isSimpleData&&!i.async.enable&&(e[t]=l.flatToTree(e[t])),w(e[t],function(e){e[Y]=Y in e?e[Y]:void 0!==e[a]&&l.status.expand[e[a]]},c),this.autoSort&&this.initSort&&this.initSort.type&&layui.sort(e[t],this.initSort.field,"desc"===this.initSort.type,!0),l.initData(e[t]),e},n.parseData.mod=!0):void 0!==n.data&&(n.data=n.data||[],i.data.isSimpleData&&(n.data=l.flatToTree(n.data)),l.initData(n.data)),e&&(!d||d.mod)||(n.done=function(){var e,t=arguments,n=t[3],i="renderData"===n,a=(i||delete l.isExpandAll,this.elem.next()),o=(l.updateStatus(null,{LAY_HAS_EXPANDED:!1}),x(r,c),a.find('[name="layTableCheckbox"][lay-filter="layTableAllChoose"]'));if(o.length&&(e=O.checkStatus(r),o.prop({checked:e.isAll&&e.data.length,indeterminate:!e.isAll&&e.data.length})),!i&&s.autoSort&&s.initSort&&s.initSort.type&&O.sort(r),l.renderTreeTable(a),"reloadData"===n&&"fixed"===this.scrollPos&&a.find(u).scrollTop(l.scrollTopCache),"function"===layui.type(d))return d.apply(this,t)},n.done.mod=!0),n&&n.tree&&n.tree.view&&layui.each(f,function(e,t){void 0!==n.tree.view[t]&&(n.tree.view[t]=l.normalizedIcon(n.tree.view[t]))})}function j(e,t,n){return o[e]||(o[e]=layui.debounce(t,n)),o[e]}function _(t,n,i,a,o){var e=t.trElem,r=t.tableViewElem||e.closest(".layui-table-view"),l=t.tableId||r.attr(R),s=t.options||F.getOptions(l),e=t.dataIndex||e.attr("lay-data-index"),c=M(l),d=s.tree||{},u=d.customName||{},f=u.isParent,p=c.getNodeDataByIndex(e),h="boolean"!==layui.type(n),y=h?!p[Y]:n,m=p[f]?y:null;if(o&&y!=p[Y]&&(!p[X]||"local"===p[X])){var g=d.callback.beforeExpand;if("function"===layui.type(g)&&!1===g(l,p,n))return m}var g=p[$],v=(I=r.find('tr[lay-data-index="'+e+'"]')).find(".layui-table-tree-flexIcon"),x=(c.updateNodeIcon({scopeEl:I,isExpand:y,isParent:p[f]}),p[Y]=y,p[u.id]);if(void 0!==x&&(c.status.expand[x]=y),null!==m){if(x=p[u.children]||[],y)if(g){if(!x.length)return;I.nextAll(x.map(function(e,t,n){return'tr[lay-data-index="'+e[W]+'"]'}).join(",")).removeClass(P),layui.each(x,function(e,t){t[f]&&(!i||h||t[Y]?t[Y]&&_({dataIndex:t[W],trElem:r.find('tr[lay-data-index="'+t[W]+'"]').first(),tableViewElem:r,tableId:l,options:s},!0):_({dataIndex:t[W],trElem:r.find('tr[lay-data-index="'+t[W]+'"]').first(),tableViewElem:r,tableId:l,options:s},n,i,a,o))})}else{var b,w,k,C,T,E,D,S,N,L,A,I=(g=d.async||{}).url||s.url;if(g.enable&&p[f]&&!p[X])return p[X]="loading",v.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-loop layui-anim-rotate"></i>'),b=function(e){p[X]="success",p[u.children]=e,c.initData(p[u.children],p[W]),_(t,!0,!h&&i,a,o)},k=g.format,"function"===layui.type(k)?k(p,s,b):(w=H.extend({},g.where||s.where),k=g.autoParam,layui.each(k,function(e,t){t=t.split("="),w[t[0].trim()]=p[(t[1]||t[0]).trim()]}),(k=g.contentType||s.contentType)&&0==k.indexOf("application/json")&&(w=JSON.stringify(w)),N=g.method||s.method,C=g.dataType||s.dataType,T=g.jsonpCallback||s.jsonpCallback,E=g.headers||s.headers,D=g.parseData||s.parseData,S=g.response||s.response,H.ajax({type:N||"get",url:I,contentType:k,data:w,dataType:C||"json",jsonpCallback:T,headers:E||{},success:function(e){(e="function"==typeof D&&D.call(s,e)||e)[S.statusName]!=S.statusCode?(p[X]="error",v.html('<i class="layui-icon layui-icon-refresh"></i>')):b(e[S.dataName])},error:function(e,t){p[X]="error","function"==typeof s.error&&s.error(e,t)}})),m;p[$]=!0,x.length&&(!s.initSort||s.url&&!s.autoSort||((g=s.initSort).type?layui.sort(x,g.field,"desc"===g.type,!0):layui.sort(x,F.config.indexName,null,!0)),c.initData(p[u.children],p[W]),N=F.getTrHtml(l,x,null,null,e),L={trs:H(N.trs.join("")),trs_fixed:H(N.trs_fixed.join("")),trs_fixed_r:H(N.trs_fixed_r.join(""))},A=(e.split("-").length-1||0)+1,layui.each(x,function(e,t){L.trs.eq(e).attr({"data-index":t[W],"lay-data-index":t[W],"data-level":A}).data("index",t[W]),L.trs_fixed.eq(e).attr({"data-index":t[W],"lay-data-index":t[W],"data-level":A}).data("index",t[W]),L.trs_fixed_r.eq(e).attr({"data-index":t[W],"lay-data-index":t[W],"data-level":A}).data("index",t[W])}),r.find(q).find('tbody tr[lay-data-index="'+e+'"]').after(L.trs),r.find(B).find('tbody tr[lay-data-index="'+e+'"]').after(L.trs_fixed),r.find(z).find('tbody tr[lay-data-index="'+e+'"]').after(L.trs_fixed_r),c.renderTreeTable(L.trs,A),i)&&!h&&layui.each(x,function(e,t){_({dataIndex:t[W],trElem:r.find('tr[lay-data-index="'+t[W]+'"]').first(),tableViewElem:r,tableId:l,options:s},n,i,a,o)})}else c.isExpandAll=!1,(i&&!h?(layui.each(x,function(e,t){_({dataIndex:t[W],trElem:r.find('tr[lay-data-index="'+t[W]+'"]').first(),tableViewElem:r,tableId:l,options:s},n,i,a,o)}),r.find(x.map(function(e,t,n){return'tr[lay-data-index="'+e[W]+'"]'}).join(","))):(I=c.treeToFlat(x,p[u.id],e),r.find(I.map(function(e,t,n){return'tr[lay-data-index="'+e[W]+'"]'}).join(",")))).addClass(P);j("resize-"+l,function(){O.resize(l)},0)(),o&&"loading"!==p[X]&&(k=d.callback.onExpand,"function"===layui.type(k))&&k(l,p,y)}return m}function i(i){var t=i.config.id,a=M(t),n=i.data=O.getNodeDataByIndex(t,i.index),o=n[W],r=(i.dataIndex=o,i.update);i.update=function(){var e=arguments,t=(H.extend(a.getNodeDataByIndex(o),e[0]),r.apply(this,e)),n=i.config.tree.customName.name;return n in e[0]&&i.tr.find('td[data-field="'+n+'"]').children("div.layui-table-cell").removeClass("layui-table-tree-item"),a.renderTreeTable(i.tr,i.tr.attr("data-level"),!1),t},i.del=function(){O.removeNode(t,n)},i.setRowChecked=function(e){O.setRowChecked(t,{index:n,checked:e})}}var o,H=layui.$,y=layui.form,F=layui.table,p=layui.hint(),O={config:{},on:F.on,eachCols:F.eachCols,index:F.index,set:function(e){return this.config=H.extend({},this.config,e),this},resize:F.resize,getOptions:F.getOptions,hideCol:F.hideCol,renderData:F.renderData},r=function(){var n=this,e=n.config,i=e.id||e.index;return{config:e,reload:function(e,t){n.reload.call(n,e,t)},reloadData:function(e,t){O.reloadData(i,e,t)}}},R="lay-table-id",P="layui-hide",u=".layui-table-body",q=".layui-table-main",B=".layui-table-fixed-l",z=".layui-table-fixed-r",l="layui-table-checked",m="layui-table-tree",W="LAY_DATA_INDEX",g="LAY_DATA_INDEX_HISTORY",h="LAY_PARENT_INDEX",v="LAY_CHECKBOX_HALF",Y="LAY_EXPAND",$="LAY_HAS_EXPANDED",X="LAY_ASYNC_STATUS",s=["all","parent","children","none"],n=/<[^>]+?>/,f=["flexIconClose","flexIconOpen","iconClose","iconOpen","iconLeaf","icon"],x=function(i,a,e){var o=F.cache[i];layui.each(e||o,function(e,t){var n=t[W]||"";-1!==n.indexOf("-")&&(o[n]=t),t[a]&&x(i,a,t[a])})},o=(t.prototype.init=function(){var e=this.config,t=e.tree.data.cascade;-1===s.indexOf(t)&&(e.tree.data.cascade="all");var n=(t=F.render(H.extend({},e,{data:[],url:"",done:null}))).config.id;(r.that[n]=this).tableIns=t,a(n,e)},t.prototype.config={tree:{customName:{children:"children",isParent:"isParent",name:"name",id:"id",pid:"parentId",icon:"icon"},view:{indent:14,flexIconClose:'<i class="layui-icon layui-icon-triangle-r"></i>',flexIconOpen:'<i class="layui-icon layui-icon-triangle-d"></i>',showIcon:!0,icon:"",iconClose:'<i class="layui-icon layui-icon-folder"></i>',iconOpen:'<i class="layui-icon layui-icon-folder-open"></i>',iconLeaf:'<i class="layui-icon layui-icon-leaf"></i>',showFlexIconIfNotParent:!1,dblClickExpand:!0,expandAllDefault:!1},data:{isSimpleData:!1,rootPid:null,cascade:"all"},async:{enable:!1,url:"",type:null,contentType:null,headers:null,where:null,autoParam:[]},callback:{beforeExpand:null,onExpand:null}}},t.prototype.normalizedIcon=function(e){return e?n.test(e)?e:'<i class="'+e+'"></i>':""},t.prototype.getOptions=function(){return this.tableIns?F.getOptions(this.tableIns.config.id):this.config},t.prototype.flatToTree=function(e){var i,a,o,r,l,s,c,d,t=(u=this.getOptions()).tree,n=t.customName,u=u.id;return u=e=e||F.cache[u],i=n.id,a=n.pid,o=n.children,r=t.data.rootPid,i=i||"id",a=a||"parentId",o=o||"children",c={},d=[],layui.each(u,function(e,t){l=i+t[i],s=i+t[a],c[l]||(c[l]={},c[l][o]=[]);var n={};n[o]=c[l][o],c[l]=H.extend({},t,n),((r?c[l][a]!==r:c[l][a])?(c[s]||(c[s]={},c[s][o]=[]),c[s][o]):d).push(c[l])}),d},t.prototype.treeToFlat=function(e,i,a){var o=this,r=o.getOptions().tree.customName,l=r.children,s=r.pid,c=[];return layui.each(e,function(e,t){var e=(a?a+"-":"")+e,n=H.extend({},t);n[s]=t[s]||i,c.push(n),c=c.concat(o.treeToFlat(t[l],t[r.id],e))}),c},t.prototype.getTreeNode=function(e){var t,n,i=this;return e?(n=(t=i.getOptions()).tree,t.id,n.customName,{data:e,dataIndex:e[W],getParentNode:function(){return i.getNodeByIndex(e[h])}}):p.error("找不到节点数据")},t.prototype.getNodeByIndex=function(t){var n,e,i=this,a=i.getNodeDataByIndex(t);return a?((e=i.getOptions()).tree.customName.parent,n=e.id,(e={data:a,dataIndex:a[W],getParentNode:function(){return i.getNodeByIndex(a[h])},update:function(e){return O.updateNode(n,t,e)},remove:function(){return O.removeNode(n,t)},expand:function(e){return O.expandNode(n,H.extend({},e,{index:t}))},setChecked:function(e){return O.setRowChecked(n,H.extend({},e,{index:t}))}}).dataIndex=t,e):p.error("找不到节点数据")},t.prototype.getNodeById=function(n){var i=(e=this.getOptions()).tree.customName.id,a="",e=O.getData(e.id,!0);if(layui.each(e,function(e,t){if(t[i]===n)return a=t[W],!0}),a)return this.getNodeByIndex(a)},t.prototype.getNodeDataByIndex=function(e,t,n){var i=(a=this.getOptions()).tree,a=a.id,o=(a=F.cache[a])[e];if("delete"!==n&&o)return H.extend(o,n),t?H.extend({},o):o;for(var r=a,l=String(e).split("-"),s=0,c=i.customName.children;s<l.length;s++){if(n&&s===l.length-1){if("delete"===n)return(s?r[c]:r).splice(l[s],1)[0];H.extend((s?r[c]:r)[l[s]],n)}r=(s?r[c]:r)[l[s]]}return t?H.extend({},r):r},O.getNodeDataByIndex=function(e,t){if(e=M(e))return e.getNodeDataByIndex(t,!0)},t.prototype.initData=function(e,t){var n=(i=this.getOptions()).tree,i=i.id;e=e||this.getTableData();var a=(n=n.customName).isParent,o=n.children,r=function(e,n){layui.each(e,function(e,t){a in t||(t[a]=!(!t[o]||!t[o].length)),t[g]=t[W],t[h]=n=n||"",e=t[W]=(n?n+"-":"")+e,r(t[o]||[],e)})};return r(e,t),x(i,o,e),e},{}),b=(O.expandNode=function(e,t){var n,i,a;if(e=M(e))return n=(t=t||{}).index,i=t.expandFlag,a=t.inherit,t=t.callbackFlag,e=e.getOptions().elem.next(),_({trElem:e.find('tr[lay-data-index="'+n+'"]').first()},i,a,null,t)},O.expandAll=function(n,e){if("boolean"!==layui.type(e))return p.error("expandAll 的展开状态参数只接收true/false");var t=M(n);if(t){t.isExpandAll=e;var i=(u=t.getOptions()).tree,a=u.elem.next(),o=i.customName.isParent,r=i.customName.id,l=i.view.showFlexIconIfNotParent;if(e){if(e=O.getData(n,!0),i.async.enable){var s=!0;if(layui.each(e,function(e,t){if(t[o]&&!t[X])return!(s=!1)}),!s)return void layui.each(O.getData(n),function(e,t){O.expandNode(n,{index:t[W],expandFlag:!0,inherit:!0})})}var c=!0;if(layui.each(e,function(e,t){if(t[o]&&!t[$])return!(c=!1)}),c)t.updateStatus(null,function(e){(e[o]||l)&&(e[Y]=!0,void 0!==e[r])&&(t.status.expand[e[r]]=!0)}),a.find('tbody tr[data-level!="0"]').removeClass(P),a.find(".layui-table-tree-flexIcon").html(i.view.flexIconOpen),i.view.showIcon&&a.find(".layui-table-tree-nodeIcon:not(.layui-table-tree-iconCustom,.layui-table-tree-iconLeaf)").html(i.view.iconOpen);else{if(t.updateStatus(null,function(e){(e[o]||l)&&(e[Y]=!0,e[$]=!0,void 0!==e[r])&&(t.status.expand[e[r]]=!0)}),u.initSort&&u.initSort.type&&u.autoSort)return O.sort(n);var d,u=F.getTrHtml(n,e),f={trs:H(u.trs.join("")),trs_fixed:H(u.trs_fixed.join("")),trs_fixed_r:H(u.trs_fixed_r.join(""))};layui.each(e,function(e,t){var n=t[W].split("-").length-1;d={"data-index":t[W],"lay-data-index":t[W],"data-level":n},f.trs.eq(e).attr(d).data("index",t[W]),f.trs_fixed.eq(e).attr(d).data("index",t[W]),f.trs_fixed_r.eq(e).attr(d).data("index",t[W])}),layui.each(["main","fixed-l","fixed-r"],function(e,t){a.find(".layui-table-"+t+" tbody").html(f[["trs","trs_fixed","trs_fixed_r"][e]])}),t.renderTreeTable(a,0,!1)}}else t.updateStatus(null,function(e){(e[o]||l)&&(e[Y]=!1,void 0!==e[r])&&(t.status.expand[e[r]]=!1)}),a.find('.layui-table-box tbody tr[data-level!="0"]').addClass(P),a.find(".layui-table-tree-flexIcon").html(i.view.flexIconClose),i.view.showIcon&&a.find(".layui-table-tree-nodeIcon:not(.layui-table-tree-iconCustom,.layui-table-tree-iconLeaf)").html(i.view.iconClose);O.resize(n)}},t.prototype.updateNodeIcon=function(e){var t=this.getOptions().tree||{},n=e.scopeEl,i=e.isExpand,e=e.isParent;n.find(".layui-table-tree-flexIcon").css("visibility",e||t.view.showFlexIconIfNotParent?"visible":"hidden").html(i?t.view.flexIconOpen:t.view.flexIconClose),t.view.showIcon&&(n=n.find(".layui-table-tree-nodeIcon:not(.layui-table-tree-iconCustom)"),i=e?i?t.view.iconOpen:t.view.iconClose:t.view.iconLeaf,n.toggleClass("layui-table-tree-iconLeaf",!e).html(i))},t.prototype.renderTreeTable=function(e,t,n){var o=this,i=o.getOptions(),r=i.elem.next(),a=(r.hasClass(m)||r.addClass(m),i.id),l=i.tree||{},s=(l.data,l.view||{}),c=l.customName||{},d=c.isParent,u=(r.attr("lay-filter"),o),f=((t=t||0)||(r.find(".layui-table-body tr:not([data-level])").attr("data-level",t),layui.each(F.cache[a],function(e,t){r.find('.layui-table-main tbody tr[data-level="0"]:eq('+e+")").attr("lay-data-index",t[W]),r.find('.layui-table-fixed-l tbody tr[data-level="0"]:eq('+e+")").attr("lay-data-index",t[W]),r.find('.layui-table-fixed-r tbody tr[data-level="0"]:eq('+e+")").attr("lay-data-index",t[W])})),null),p=c.name,h=s.indent||14;if(layui.each(e.find('td[data-field="'+p+'"]'),function(e,t){var n,i,a=(t=H(t)).closest("tr");(t=t.children(".layui-table-cell")).hasClass("layui-table-tree-item")||(i=a.attr("lay-data-index"))&&(a=r.find('tr[lay-data-index="'+i+'"]'),(n=u.getNodeDataByIndex(i))[Y]&&n[d]&&((f=f||{})[i]=!0),n[v]&&a.find('input[type="checkbox"][name="layTableCheckbox"]').prop("indeterminate",!0),i=t.html(),(t=a.find('td[data-field="'+p+'"]>div.layui-table-cell')).addClass("layui-table-tree-item"),t.html(['<div class="layui-inline layui-table-tree-flexIcon" ','style="',"margin-left: "+h*a.attr("data-level")+"px;",n[d]||s.showFlexIconIfNotParent?"":" visibility: hidden;",'">',n[Y]?s.flexIconOpen:s.flexIconClose,"</div>",s.showIcon?'<div class="layui-inline layui-table-tree-nodeIcon'+(n[c.icon]||s.icon?" layui-table-tree-iconCustom":"")+(n[d]?"":" layui-table-tree-iconLeaf")+'">'+(o.normalizedIcon(n[c.icon])||s.icon||(n[d]?n[Y]?s.iconOpen:s.iconClose:s.iconLeaf)||"")+"</div>":"",i].join("")).find(".layui-table-tree-flexIcon").on("click",function(e){layui.stope(e),_({trElem:a},null,null,null,!0)}))}),!t&&l.view.expandAllDefault&&void 0===o.isExpandAll)return O.expandAll(a,!0);(!1!==n&&f?(layui.each(f,function(e,t){(e=r.find('tr[lay-data-index="'+e+'"]')).find(".layui-table-tree-flexIcon").html(s.flexIconOpen),_({trElem:e.first()},!0)}),j("renderTreeTable2-"+a,function(){y.render(H(".layui-table-tree["+R+'="'+a+'"]'))},0)):j("renderTreeTable-"+a,function(){i.hasNumberCol&&b(o),y.render(H(".layui-table-tree["+R+'="'+a+'"]'))},0))()},function(n){var e=n.getOptions(),t=e.elem.next(),i=0,a=t.find(".layui-table-main tbody tr"),o=t.find(".layui-table-fixed-l tbody tr"),r=t.find(".layui-table-fixed-r tbody tr");layui.each(n.treeToFlat(F.cache[e.id]),function(e,t){t.LAY_HIDE||(n.getNodeDataByIndex(t[W]).LAY_NUM=++i,a.eq(e).find(".laytable-cell-numbers").html(i),o.eq(e).find(".laytable-cell-numbers").html(i),r.eq(e).find(".laytable-cell-numbers").html(i))})}),w=(t.prototype.render=function(e){this.tableIns=F["reloadData"===e?"reloadData":"reload"](this.tableIns.config.id,H.extend(!0,{},this.config)),this.config=this.tableIns.config},t.prototype.reload=function(e,t,n){var i=this;e=e||{},delete i.haveInit,layui.each(e,function(e,t){"array"===layui.type(t)&&delete i.config[e]}),a(i.getOptions().id,e,n||!0),i.config=H.extend(t,{},i.config,e),i.render(n)},O.reloadData=function(){var e=H.extend(!0,[],arguments);return e[3]="reloadData",O.reload.apply(null,e)},function(e,n,i,a){var o=[];return layui.each(e,function(e,t){"function"===layui.type(n)?n(t):H.extend(t,n),o.push(H.extend({},t)),a||(o=o.concat(w(t[i],n,i,a)))}),o}),d=(t.prototype.updateStatus=function(e,t,n){var i=this.getOptions(),a=i.tree;return e=e||F.cache[i.id],w(e,t,a.customName.children,n)},t.prototype.getTableData=function(){var e=this.getOptions();return F.cache[e.id]},O.updateStatus=function(e,t,n){var i=(e=M(e)).getOptions();return n=n||(i.url?F.cache[i.id]:i.data),e.updateStatus(n,t)},O.sort=function(e){var t,n,a,o,i,r=M(e);r&&(i=(t=r.getOptions()).tree,n=O.getData(e),a=i.customName.children,o=function(e,n,i){layui.sort(e,n,i,!0),layui.each(e,function(e,t){o(t[a]||[],n,i)})},t.autoSort)&&((i=t.initSort).type?o(n,i.field,"desc"===i.type):o(n,F.config.indexName,null),F.cache[e]=n,r.initData(n),O.renderData(e))},O.updateNode=function(e,n,t){var i,a,o,r,l,s=M(e);s&&((r=s.getOptions()).tree,r=(i=r.elem.next()).find('tr[lay-data-index="'+n+'"]'),a=r.attr("data-index"),o=r.attr("data-level"),t)&&(r=s.getNodeDataByIndex(n,!1,t),l=F.getTrHtml(e,[r]),layui.each(["main","fixed-l","fixed-r"],function(e,t){i.find(".layui-table-"+t+' tbody tr[lay-data-index="'+n+'"]').replaceWith(H(l[["trs","trs_fixed","trs_fixed_r"][e]].join("")).attr({"data-index":a,"lay-data-index":n,"data-level":o}).data("index",a))}),s.renderTreeTable(i.find('tr[lay-data-index="'+n+'"]'),o))},O.removeNode=function(e,t,n){var i=M(e);if(i){var a,o=i.getOptions(),r=(f=o.tree).customName.isParent,l=f.customName.children,s=o.elem.next(),c=[],d=F.cache[e],t=i.getNodeDataByIndex("string"===layui.type(t)?t:t[W],!1,"delete"),u=i.getNodeDataByIndex(t[h]),f=(i.updateCheckStatus(u),i.treeToFlat([t],t[f.customName.pid],t[h])),t=(layui.each(f,function(e,t){t=t[W],c.push('tr[lay-data-index="'+t+'"]'),-1!==t.indexOf("-")&&delete d[t]}),s.find(c.join(",")).remove(),i.initData());for(a in d)-1!==a.indexOf("-")&&a!==d[a][W]&&delete d[a];layui.each(i.treeToFlat(t),function(e,t){t[g]&&t[g]!==t[W]&&s.find('tr[lay-data-index="'+t[g]+'"]').attr({"data-index":t[W],"lay-data-index":t[W]}).data("index",t[W])}),layui.each(d,function(e,t){s.find('tr[data-level="0"][lay-data-index="'+t[W]+'"]').attr("data-index",e).data("index",e)}),o.hasNumberCol&&b(i),u&&(f=s.find('tr[lay-data-index="'+u[W]+'"]'),n||(u[r]=!(!u[l]||!u[l].length)),i.updateNodeIcon({scopeEl:f,isExpand:u[Y],isParent:u[r]})),O.resize(e)}},O.addNodes=function(e,t){var n=M(e);if(n){var i=(m=n.getOptions()).tree,a=m.elem.next(),o=F.config.checkName,r=(t=t||{}).parentIndex,l=t.index,s=t.data,t=t.focus,c=(r="number"===layui.type(r)?r.toString():r)?n.getNodeDataByIndex(r):null,l="number"===layui.type(l)?l:-1,s=H.extend(!0,[],layui.isArray(s)?s:[s]);if(layui.each(s,function(e,t){o in t||!c||(t[o]=c[o])}),n.getTableData(),c){var d=i.customName.isParent,u=i.customName.children,f=(c[d]=!0,(f=c[u])?(p=f.splice(-1===l?f.length:l),c[u]=f.concat(s,p)):c[u]=s),u=(n.updateStatus(f,function(e){(e[d]||i.view.showFlexIconIfNotParent)&&(e[$]=!1)}),n.treeToFlat(f));a.find(u.map(function(e){return'tr[lay-data-index="'+e[W]+'"]'}).join(",")).remove(),n.initData(),c[$]=!1,c[X]="local",_({trElem:a.find('tr[lay-data-index="'+r+'"]')},!0)}else{var p=F.cache[e].splice(-1===l?F.cache[e].length:l);if(F.cache[e]=F.cache[e].concat(s,p),m.url||(m.page?(f=m.page,m.data.splice.apply(m.data,[f.limit*(f.curr-1),f.limit].concat(F.cache[e]))):m.data=F.cache[e]),n.initData(),a.find(".layui-none").length)return F.renderData(e),s;var h,u=F.getTrHtml(e,s),y={trs:H(u.trs.join("")),trs_fixed:H(u.trs_fixed.join("")),trs_fixed_r:H(u.trs_fixed_r.join(""))},r=(layui.each(s,function(e,t){h={"data-index":t[W],"lay-data-index":t[W],"data-level":"0"},y.trs.eq(e).attr(h).data("index",t[W]),y.trs_fixed.eq(e).attr(h).data("index",t[W]),y.trs_fixed_r.eq(e).attr(h).data("index",t[W])}),parseInt(s[0][W])-1),f=a.find(q),m=a.find(B),u=a.find(z);-1==r?f.find('tr[data-level="0"][data-index="0"]')[0]?(f.find('tr[data-level="0"][data-index="0"]').before(y.trs),m.find('tr[data-level="0"][data-index="0"]').before(y.trs_fixed),u.find('tr[data-level="0"][data-index="0"]').before(y.trs_fixed_r)):(f.find("tbody").prepend(y.trs),m.find("tbody").prepend(y.trs_fixed),u.find("tbody").prepend(y.trs_fixed_r)):-1===l?(f.find("tbody").append(y.trs),m.find("tbody").append(y.trs_fixed),u.find("tbody").append(y.trs_fixed_r)):(r=p[0][g],f.find('tr[data-level="0"][data-index="'+r+'"]').before(y.trs),m.find('tr[data-level="0"][data-index="'+r+'"]').before(y.trs_fixed),u.find('tr[data-level="0"][data-index="'+r+'"]').before(y.trs_fixed_r)),layui.each(F.cache[e],function(e,t){a.find('tr[data-level="0"][lay-data-index="'+t[W]+'"]').attr("data-index",e).data("index",e)}),n.renderTreeTable(a.find(s.map(function(e,t,n){return'tr[lay-data-index="'+e[W]+'"]'}).join(",")))}return n.updateCheckStatus(c),c&&(l=a.find('tr[lay-data-index="'+c[W]+'"]'),n.updateNodeIcon({scopeEl:l,isExpand:c[Y],isParent:c[d]})),O.resize(e),t&&a.find(q).find('tr[lay-data-index="'+s[0][W]+'"]').get(0).scrollIntoViewIfNeeded(),s}},O.checkStatus=function(e,i){var a,t,n,o=M(e);if(o)return o=o.getOptions().tree,a=F.config.checkName,t=O.getData(e,!0).filter(function(e,t,n){return e[a]||i&&e[v]}),n=!0,layui.each("all"===o.data.cascade?F.cache[e]:O.getData(e,!0),function(e,t){if(!t[a])return!(n=!1)}),{data:t,isAll:n}},O.on("sort",function(e){var t=(e=e.config).elem.next(),e=e.id;t.hasClass(m)&&O.sort(e)}),O.on("row",function(e){e.config.elem.next().hasClass(m)&&i(e)}),O.on("rowDouble",function(e){var t=e.config,n=t.elem.next();t.id,n.hasClass(m)&&(i(e),(t.tree||{}).view.dblClickExpand)&&_({trElem:e.tr.first()},null,null,null,!0)}),O.on("rowContextmenu",function(e){var t=e.config,n=t.elem.next();t.id,n.hasClass(m)&&i(e)}),O.on("tool",function(e){var t=e.config,n=t.elem.next();t.id,n.hasClass(m)&&i(e)}),O.on("edit",function(e){var t=e.config,n=t.elem.next();t.id,n.hasClass(m)&&(i(e),e.field===t.tree.customName.name)&&((n={})[e.field]=e.value,e.update(n))}),O.on("radio",function(e){var t=(n=e.config).elem.next(),n=n.id;t.hasClass(m)&&(t=M(n),i(e),d.call(t,e.tr,e.checked))}),t.prototype.setRowCheckedClass=function(e,t){var n=this.getOptions(),i=(e.data("index"),n.elem.next());e[t?"addClass":"removeClass"](l),e.each(function(){var e=H(this).data("index");i.find('.layui-table-fixed-r tbody tr[data-index="'+e+'"]')[t?"addClass":"removeClass"](l)})},t.prototype.updateCheckStatus=function(e,t){var n,i,a,o,r,l,s,c=this,d=c.getOptions();return!!d.hasChecboxCol&&(n=d.tree,i=d.id,a=d.elem.next(),o=F.config.checkName,"all"!==(r=n.data.cascade)&&"parent"!==r||!e||(r=c.updateParentCheckStatus(e,"boolean"===layui.type(t)?t:null),layui.each(r,function(e,t){var n=a.find('tr[lay-data-index="'+t[W]+'"]  input[name="layTableCheckbox"]:not(:disabled)'),i=t[o];c.setRowCheckedClass(n.closest("tr"),i),y.render(n.prop({checked:i,indeterminate:t[v]}))})),s=!(l=!0),0<(e=(e="all"===n.data.cascade?F.cache[i]:O.getData(i,!0)).filter(function(e){return!e[d.disabledName]})).length?layui.each(e,function(e,t){if((t[o]||t[v])&&(s=!0),t[o]||(l=!1),s&&!l)return!0}):l=!1,s=s&&!l,y.render(a.find('input[name="layTableCheckbox"][lay-filter="layTableAllChoose"]').prop({checked:l,indeterminate:s})),l)},t.prototype.updateParentCheckStatus=function(n,i){var a,e=(t=this.getOptions()).tree,t=t.id,o=F.config.checkName,e=e.customName.children,r=[];return!(n[v]=!1)===i?n[e].length?layui.each(n[e],function(e,t){if(!t[o])return i=!1,n[v]=!0}):i=!1:!1===i?layui.each(n[e],function(e,t){if(t[o]||t[v])return n[v]=!0}):(i=!1,a=0,layui.each(n[e],function(e,t){t[o]&&a++}),i=n[e].length?n[e].length===a:n[o],n[v]=!i&&0<a),n[o]=i,r.push(H.extend({},n)),n[h]?r.concat(this.updateParentCheckStatus(F.cache[t][n[h]],i)):r},function(e,t,n){var i=this,a=(s=i.getOptions()).tree,o=s.id,r=s.elem.next(),l=(e.length?e:r).find(".laytable-cell-radio, .laytable-cell-checkbox").children("input").last(),s="radio"===l.attr("type");if(n)n=function(){function e(e){layui.stope(e)}l.parent().on("click",e),l.next().click(),l.parent().off("click",e)},s?t&&!l.prop("checked")&&n():"boolean"===layui.type(t)&&l.prop("checked")===t||n();else{var c,n=i.getNodeDataByIndex(e.attr("data-index")),d=F.config.checkName;if(!s)return t="boolean"===layui.type(t)?t:!n[d],s=i.updateStatus(n?[n]:F.cache[o],function(e){e[F.config.disabledName]||(e[d]=t,e[v]=!1)},n&&-1!==["parent","none"].indexOf(a.data.cascade)),o=r.find(s.map(function(e){return'tr[lay-data-index="'+e[W]+'"] input[name="layTableCheckbox"]:not(:disabled)'}).join(",")),i.setRowCheckedClass(o.closest("tr"),t),y.render(o.prop({checked:t,indeterminate:!1})),n&&n[h]&&(c=i.getNodeDataByIndex(n[h])),i.updateCheckStatus(c,t);n&&(i.updateStatus(null,function(e){var t;e[d]&&(t=r.find('tr[lay-data-index="'+e[W]+'"] input[type="radio"][lay-type="layTableRadio"]'),e[d]=!1,i.setRowCheckedClass(t.closest("tr"),!1),y.render(t.prop("checked",!1)))}),n[d]=t,i.setRowCheckedClass(e,t),i.setRowCheckedClass(e.siblings(),!1),y.render(e.find('input[type="radio"][lay-type="layTableRadio"]').prop("checked",t)))}});O.on("checkbox",function(e){var t=(n=e.config).elem.next(),n=n.id;t.hasClass(m)&&(t=M(n),n=e.checked,i(e),e.isAll=d.call(t,e.tr,n))}),O.setRowChecked=function(n,e){var t,i,a,o,r,l,s,c=M(n);c&&(t=c.getOptions().elem.next(),a=(e=e||{}).index,i=e.checked,e=e.callbackFlag,a="string"===layui.type(a)?a:a[W],l=c.getNodeDataByIndex(a))&&(o=function(e){s.push(e),t.find('tr[lay-data-index="'+e+'"]').length||(e=c.getNodeDataByIndex(e)[h])&&o(e)},(r=t.find('tr[lay-data-index="'+a+'"]')).length||(l=l[h],s=[],o(l),layui.each(s.reverse(),function(e,t){O.expandNode(n,{index:t,expandFlag:!0})}),r=t.find('tr[lay-data-index="'+a+'"]')),d.call(c,r,i,e))},O.checkAllNodes=function(e,t){var n;(e=M(e))&&(n=e.getOptions().elem.next(),d.call(e,n.find('tr[data-index="NONE"]'),!!t))},O.getData=function(e,t){var n,i=M(e);if(i)return n=[],layui.each(H.extend(!0,[],F.cache[e]||[]),function(e,t){n.push(t)}),t?i.treeToFlat(n):n},O.reloadAsyncNode=function(n,e){var t,i,a=M(n);a&&(t=a.getOptions().tree).async&&t.async.enable&&(i=a.getNodeDataByIndex(e))&&(i[$]=!1,i[Y]=!1,i[X]=!1,layui.each(a.treeToFlat(i[t.customName.children]).reverse(),function(e,t){O.removeNode(n,t[W],!0)}),O.expandNode(n,{index:e,expandFlag:!0,callbackFlag:!0}))},O.getNodeById=function(e,t){if(e=M(e))return e.getNodeById(t)},O.getNodesByFilter=function(e,t,n){var i,a,o,r=M(e);if(r)return a=r.getOptions(),i=(n=n||{}).isSingle,n=(n=n.parentNode)&&n.data,a=r.treeToFlat(n?n[a.tree.customName.children]||[]:F.cache[e]).filter(t),o=[],layui.each(a,function(e,t){if(o.push(r.getNodeByIndex(t[W])),i)return!0}),o},r.that={},O.reload=function(e,t,n,i){if(e=M(e))return e.reload(t,n,i),r.call(e)},O.render=function(e){return e=new t(e),r.call(e)},e("treeTable",O)}),layui.define(["form","util"],function(e){"use strict";function i(){var t=this,e=t.config,n=e.id||t.index;return i.that[n]=t,{config:i.config[n]=e,reload:function(e){t.reload.call(t,e)},getChecked:function(){return t.getChecked.call(t)},setChecked:function(e){return t.setChecked.call(t,e)}}}function t(e){this.index=++o.index,this.config=p.extend({},this.config,o.config,e),this.render()}var p=layui.$,n=layui.form,h=layui.layer,y=layui.util,a="tree",o={config:{customName:{id:"id",title:"title",children:"children"}},index:layui.tree?layui.tree.index+1e4:0,set:function(e){return this.config=p.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,a,e,t)}},m="layui-hide",u="layui-disabled",g="layui-tree-set",v="layui-tree-iconClick",x="layui-icon-addition",b="layui-icon-subtraction",w="layui-tree-entry",k="layui-tree-main",C="layui-tree-txt",T="layui-tree-pack",E="layui-tree-spread",D="layui-tree-setLineShort",S="layui-tree-showLine",N="layui-tree-lineExtend";t.prototype.config={data:[],showCheckbox:!1,showLine:!0,accordion:!1,onlyIconControl:!1,isJump:!1,edit:!1,text:{defaultNodeName:"未命名",none:"无数据"}},t.prototype.reload=function(e){var n=this;layui.each(e,function(e,t){"array"===layui.type(t)&&delete n.config[e]}),n.config=p.extend(!0,{},n.config,e),n.render()},t.prototype.render=function(){var e=this,t=e.config,n=(t.customName=p.extend({},o.config.customName,t.customName),e.checkids=[],p('<div class="layui-tree layui-border-box'+(t.showCheckbox?" layui-form":"")+(t.showLine?" layui-tree-line":"")+'" lay-filter="LAY-tree-'+e.index+'"></div>')),i=(e.tree(n),t.elem=p(t.elem));if(i[0]){if(e.key=t.id||e.index,e.elem=n,e.elemNone=p('<div class="layui-tree-emptyText">'+t.text.none+"</div>"),i.html(e.elem),0==e.elem.find(".layui-tree-set").length)return e.elem.append(e.elemNone);t.showCheckbox&&e.renderForm("checkbox"),e.elem.find(".layui-tree-set").each(function(){var e=p(this);e.parent(".layui-tree-pack")[0]||e.addClass("layui-tree-setHide"),!e.next()[0]&&e.parents(".layui-tree-pack").eq(1).hasClass("layui-tree-lineExtend")&&e.addClass(D),e.next()[0]||e.parents(".layui-tree-set").eq(0).next()[0]||e.addClass(D)}),e.events()}},t.prototype.renderForm=function(e){n.render(e,"LAY-tree-"+this.index)},t.prototype.tree=function(l,e){var s=this,c=s.config,d=c.customName,e=e||c.data;layui.each(e,function(e,t){var n,i,a=t[d.children]&&0<t[d.children].length,o=p('<div class="layui-tree-pack" '+(t.spread?'style="display: block;"':"")+"></div>"),r=p(['<div data-id="'+t[d.id]+'" class="layui-tree-set'+(t.spread?" layui-tree-spread":"")+(t.checked?" layui-tree-checkedFirst":"")+'">','<div class="layui-tree-entry">','<div class="layui-tree-main">',c.showLine?a?'<span class="layui-tree-iconClick layui-tree-icon"><i class="layui-icon '+(t.spread?"layui-icon-subtraction":"layui-icon-addition")+'"></i></span>':'<span class="layui-tree-iconClick"><i class="layui-icon layui-icon-file"></i></span>':'<span class="layui-tree-iconClick"><i class="layui-tree-iconArrow '+(a?"":m)+'"></i></span>',c.showCheckbox?'<input type="checkbox" name="'+(t.field||"layuiTreeCheck_"+t[d.id])+'" same="layuiTreeCheck" lay-skin="primary" '+(t.disabled?"disabled":"")+' value="'+t[d.id]+'">':"",c.isJump&&t.href?'<a href="'+t.href+'" target="_blank" class="'+C+'">'+(t[d.title]||t.label||c.text.defaultNodeName)+"</a>":'<span class="'+C+(t.disabled?" "+u:"")+'">'+(t[d.title]||t.label||c.text.defaultNodeName)+"</span>","</div>",c.edit?(n={add:'<i class="layui-icon layui-icon-add-1"  data-type="add"></i>',update:'<i class="layui-icon layui-icon-edit" data-type="update"></i>',del:'<i class="layui-icon layui-icon-delete" data-type="del"></i>'},i=['<div class="layui-btn-group layui-tree-btnGroup">'],!0===c.edit&&(c.edit=["update","del"]),"object"==typeof c.edit?(layui.each(c.edit,function(e,t){i.push(n[t]||"")}),i.join("")+"</div>"):void 0):"","</div></div>"].join(""));a&&(r.append(o),s.tree(o,t[d.children])),l.append(r),r.prev("."+g)[0]&&r.prev().children(".layui-tree-pack").addClass("layui-tree-showLine"),a||r.parent(".layui-tree-pack").addClass("layui-tree-lineExtend"),s.spread(r,t),c.showCheckbox&&(t.checked&&s.checkids.push(t[d.id]),s.checkClick(r,t)),c.edit&&s.operate(r,t)})},t.prototype.spread=function(i,a){var o=this,r=o.config,e=(l=i.children("."+w)).children("."+k),t=e.find('input[same="layuiTreeCheck"]'),n=l.find("."+v),l=l.find("."+C),s=r.onlyIconControl?n:e,c="";s.on("click",function(e){var t=i.children("."+T),n=(s.children(".layui-icon")[0]?s:s.find(".layui-tree-icon")).children(".layui-icon");t[0]?i.hasClass(E)?(i.removeClass(E),t.slideUp(200),n.removeClass(b).addClass(x),o.updateFieldValue(a,"spread",!1)):(i.addClass(E),t.slideDown(200),n.addClass(b).removeClass(x),o.updateFieldValue(a,"spread",!0),r.accordion&&((t=i.siblings("."+g)).removeClass(E),t.children("."+T).slideUp(200),t.find(".layui-tree-icon").children(".layui-icon").removeClass(b).addClass(x))):c="normal"}),l.on("click",function(){p(this).hasClass(u)||(c=i.hasClass(E)?r.onlyIconControl?"open":"close":r.onlyIconControl?"close":"open",t[0]&&o.updateFieldValue(a,"checked",t.prop("checked")),r.click&&r.click({elem:i,state:c,data:a}))})},t.prototype.updateFieldValue=function(e,t,n){t in e&&(e[t]=n)},t.prototype.setCheckbox=function(e,t,n){var a,i=this,o=i.config.customName,r=n.prop("checked");n.prop("disabled")||("object"!=typeof t[o.children]&&!e.find("."+T)[0]||e.find("."+T).find('input[same="layuiTreeCheck"]').each(function(e){this.disabled||((e=t[o.children][e])&&i.updateFieldValue(e,"checked",r),i.updateFieldValue(this,"checked",r))}),(a=function(e){var t,n,i;e.parents("."+g)[0]&&(n=(e=e.parent("."+T)).parent(),i=e.prev().find('input[same="layuiTreeCheck"]'),r?i.prop("checked",r):(e.find('input[same="layuiTreeCheck"]').each(function(){this.checked&&(t=!0)}),t||i.prop("checked",!1)),a(n))})(e),i.renderForm("checkbox"))},t.prototype.checkClick=function(n,i){var a=this,o=a.config;n.children("."+w).children("."+k).on("click",'input[same="layuiTreeCheck"]+',function(e){layui.stope(e);var t=(e=p(this).prev()).prop("checked");e.prop("disabled")||(a.setCheckbox(n,i,e),a.updateFieldValue(i,"checked",t),o.oncheck&&o.oncheck({elem:n,checked:t,data:i}))})},t.prototype.operate=function(s,o){var c=this,d=c.config,u=d.customName,e=s.children("."+w),f=e.children("."+k);e.children(".layui-tree-btnGroup").on("click",".layui-icon",function(e){layui.stope(e);var t,n,i,a,e=p(this).data("type"),r=s.children("."+T),l={data:o,type:e,elem:s};"add"==e?(r[0]||(d.showLine?(f.find("."+v).addClass("layui-tree-icon"),f.find("."+v).children(".layui-icon").addClass(x).removeClass("layui-icon-file")):f.find(".layui-tree-iconArrow").removeClass(m),s.append('<div class="layui-tree-pack"></div>')),i=d.operate&&d.operate(l),(a={})[u.title]=d.text.defaultNodeName,a[u.id]=i,c.tree(s.children("."+T),[a]),d.showLine&&(r[0]?(r.hasClass(N)||r.addClass(N),s.find("."+T).each(function(){p(this).children("."+g).last().addClass(D)}),(r.children("."+g).last().prev().hasClass(D)?r.children("."+g).last().prev():r.children("."+g).last()).removeClass(D),!s.parent("."+T)[0]&&s.next()[0]&&r.children("."+g).last().removeClass(D)):(i=s.siblings("."+g),n=1,a=s.parent("."+T),layui.each(i,function(e,t){p(t).children("."+T)[0]||(n=0)}),(1==n?(i.children("."+T).addClass(S),i.children("."+T).children("."+g).removeClass(D),s.children("."+T).addClass(S),a.removeClass(N),a.children("."+g).last().children("."+T).children("."+g).last()):s.children("."+T).children("."+g)).addClass(D))),d.showCheckbox&&(f.find('input[same="layuiTreeCheck"]')[0].checked&&(s.children("."+T).children("."+g).last().find('input[same="layuiTreeCheck"]')[0].checked=!0),c.renderForm("checkbox"))):"update"==e?(i=f.children("."+C).html(),f.children("."+C).html(""),f.append('<input type="text" class="layui-tree-editInput">'),f.children(".layui-tree-editInput").val(y.unescape(i)).focus(),t=function(e){var t=y.escape(e.val().trim())||d.text.defaultNodeName;e.remove(),f.children("."+C).html(t),l.data[u.title]=t,d.operate&&d.operate(l)},f.children(".layui-tree-editInput").blur(function(){t(p(this))}),f.children(".layui-tree-editInput").on("keydown",function(e){13===e.keyCode&&(e.preventDefault(),t(p(this)))})):h.confirm('确认删除该节点 "<span style="color: #999;">'+(o[u.title]||"")+'</span>" 吗？',function(e){var o,n,t;d.operate&&d.operate(l),l.status="remove",h.close(e),s.prev("."+g)[0]||s.next("."+g)[0]||s.parent("."+T)[0]?(s.siblings("."+g).children("."+w)[0]?(d.showCheckbox&&(o=function(e){var t,n,i,a;e.parents("."+g)[0]&&(t=e.siblings("."+g).children("."+w),n=(e=e.parent("."+T).prev()).find('input[same="layuiTreeCheck"]')[0],i=1,(a=0)==n.checked)&&(t.each(function(e,t){0!=(t=p(t).find('input[same="layuiTreeCheck"]')[0]).checked||t.disabled||(i=0),t.disabled||(a=1)}),1==i)&&1==a&&(n.checked=!0,c.renderForm("checkbox"),o(e.parent("."+g)))})(s),d.showLine&&(e=s.siblings("."+g),n=1,t=s.parent("."+T),layui.each(e,function(e,t){p(t).children("."+T)[0]||(n=0)}),1==n?(r[0]||(t.removeClass(N),e.children("."+T).addClass(S),e.children("."+T).children("."+g).removeClass(D)),(s.next()[0]?t.children("."+g).last():s.prev()).children("."+T).children("."+g).last().addClass(D),s.next()[0]||s.parents("."+g)[1]||s.parents("."+g).eq(0).next()[0]||s.prev("."+g).addClass(D)):!s.next()[0]&&s.hasClass(D)&&s.prev().addClass(D))):(e=s.parent("."+T).prev(),d.showLine?(e.find("."+v).removeClass("layui-tree-icon"),e.find("."+v).children(".layui-icon").removeClass(b).addClass("layui-icon-file"),(t=e.parents("."+T).eq(0)).addClass(N),t.children("."+g).each(function(){p(this).children("."+T).children("."+g).last().addClass(D)})):e.find(".layui-tree-iconArrow").addClass(m),s.parents("."+g).eq(0).removeClass(E),s.parent("."+T).remove()),s.remove()):(s.remove(),c.elem.append(c.elemNone))})})},t.prototype.events=function(){var t=this,a=t.config;t.elem.find(".layui-tree-checkedFirst"),t.setChecked(t.checkids),t.elem.find(".layui-tree-search").on("keyup",function(){var n=(e=p(this)).val(),e=e.nextAll(),i=[];e.find("."+C).each(function(){var t,e=p(this).parents("."+w);-1!=p(this).html().indexOf(n)&&(i.push(p(this).parent()),(t=function(e){e.addClass("layui-tree-searchShow"),e.parent("."+T)[0]&&t(e.parent("."+T).parent("."+g))})(e.parent("."+g)))}),e.find("."+w).each(function(){var e=p(this).parent("."+g);e.hasClass("layui-tree-searchShow")||e.addClass(m)}),0==e.find(".layui-tree-searchShow").length&&t.elem.append(t.elemNone),a.onsearch&&a.onsearch({elem:i})}),t.elem.find(".layui-tree-search").on("keydown",function(){p(this).nextAll().find("."+w).each(function(){p(this).parent("."+g).removeClass("layui-tree-searchShow "+m)}),p(".layui-tree-emptyText")[0]&&p(".layui-tree-emptyText").remove()})},t.prototype.getChecked=function(){var a=this,e=a.config,o=e.customName,t=[],n=[],r=(a.elem.find(".layui-form-checked").each(function(){t.push(p(this).prev()[0].value)}),function(e,i){layui.each(e,function(e,n){layui.each(t,function(e,t){if(n[o.id]==t)return a.updateFieldValue(n,"checked",!0),delete(t=p.extend({},n))[o.children],i.push(t),n[o.children]&&(t[o.children]=[],r(n[o.children],t[o.children])),!0})})});return r(p.extend({},e.data),n),n},t.prototype.setChecked=function(o){this.config,this.elem.find("."+g).each(function(e,t){var n=p(this).data("id"),i=p(t).children("."+w).find('input[same="layuiTreeCheck"]'),a=i.next();if("number"==typeof o){if(n.toString()==o.toString())return i[0].checked||a.click(),!1}else"object"==typeof o&&layui.each(o,function(e,t){if(t.toString()==n.toString()&&!i[0].checked)return a.click(),!0})})},i.that={},i.config={},o.reload=function(e,t){return(e=i.that[e]).reload(t),i.call(e)},o.getChecked=function(e){return i.that[e].getChecked()},o.setChecked=function(e,t){return i.that[e].setChecked(t)},o.render=function(e){return e=new t(e),i.call(e)},e(a,o)}),layui.define(["laytpl","form"],function(e){"use strict";function i(){var t=this,e=t.config,n=e.id||t.index;return i.that[n]=t,{config:i.config[n]=e,reload:function(e){t.reload.call(t,e)},getData:function(){return t.getData.call(t)}}}function t(e){return['<div class="layui-transfer-box" data-index="'+(e=e||{}).index+'">','<div class="layui-transfer-header">','<input type="checkbox" name="'+e.checkAllName+'" lay-filter="layTransferCheckbox" lay-type="all" lay-skin="primary" title="{{= d.data.title['+e.index+"] || 'list"+(e.index+1)+"' }}\">","</div>","{{# if(d.data.showSearch){ }}",'<div class="layui-transfer-search">','<i class="layui-icon layui-icon-search"></i>','<input type="text" class="layui-input" placeholder="关键词搜索">',"</div>","{{# } }}",'<ul class="layui-transfer-data"></ul>',"</div>"].join("")}function n(e){this.index=++l.index,this.config=c.extend({},this.config,l.config,e),this.render()}var c=layui.$,a=layui.laytpl,o=layui.form,r="transfer",l={config:{},index:layui[r]?layui[r].index+1e4:0,set:function(e){return this.config=c.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,r,e,t)}},d="layui-hide",u="layui-btn-disabled",s="layui-none",f="layui-transfer-box",p="layui-transfer-header",h="layui-transfer-search",y="layui-transfer-data",m=['<div class="layui-transfer layui-form layui-border-box" lay-filter="LAY-transfer-{{= d.index }}">',t({index:0,checkAllName:"layTransferLeftCheckAll"}),'<div class="layui-transfer-active">','<button type="button" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0">','<i class="layui-icon layui-icon-next"></i>',"</button>",'<button type="button" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1">','<i class="layui-icon layui-icon-prev"></i>',"</button>","</div>",t({index:1,checkAllName:"layTransferRightCheckAll"}),"</div>"].join("");n.prototype.config={title:["列表一","列表二"],width:200,height:360,data:[],value:[],showSearch:!1,id:"",text:{none:"无数据",searchNone:"无匹配数据"}},n.prototype.reload=function(e){this.config=c.extend({},this.config,e),this.render()},n.prototype.render=function(){var e=this,t=e.config,n=e.elem=c(a(m,{open:"{{",close:"}}"}).render({data:t,index:e.index})),i=t.elem=c(t.elem);i[0]&&(t.data=t.data||[],t.value=t.value||[],t.id="id"in t?t.id:elem.attr("id")||e.index,e.key=t.id,i.html(e.elem),e.layBox=e.elem.find("."+f),e.layHeader=e.elem.find("."+p),e.laySearch=e.elem.find("."+h),e.layData=n.find("."+y),e.layBtn=n.find(".layui-transfer-active .layui-btn"),e.layBox.css({width:t.width,height:t.height}),e.layData.css({height:(i=t.height-e.layHeader.outerHeight(),t.showSearch&&(i-=e.laySearch.outerHeight()),i-2)}),e.renderData(),e.events())},n.prototype.renderData=function(){var e=this.config,o=[{checkName:"layTransferLeftCheck",views:[]},{checkName:"layTransferRightCheck",views:[]}];this.parseData(function(n){var i=n.selected?1:0,a=["<li>",'<input type="checkbox" name="'+o[i].checkName+'" lay-skin="primary" lay-filter="layTransferCheckbox" title="'+n.title+'"'+(n.disabled?" disabled":"")+(n.checked?" checked":"")+' value="'+n.value+'">',"</li>"].join("");i?layui.each(e.value,function(e,t){t==n.value&&n.selected&&(o[i].views[e]=a)}):o[i].views.push(a),delete n.selected}),this.layData.eq(0).html(o[0].views.join("")),this.layData.eq(1).html(o[1].views.join("")),this.renderCheckBtn()},n.prototype.renderForm=function(e){o.render(e,"LAY-transfer-"+this.index)},n.prototype.renderCheckBtn=function(r){var l=this,s=l.config;r=r||{},l.layBox.each(function(e){var t=(n=c(this)).find("."+y),n=n.find("."+p).find('input[type="checkbox"]'),i=t.find('input[type="checkbox"]'),a=0,o=!1;i.each(function(){var e=c(this).data("hide");(this.checked||this.disabled||e)&&a++,this.checked&&!e&&(o=!0)}),n.prop("checked",o&&a===i.length),l.layBtn.eq(e)[o?"removeClass":"addClass"](u),r.stopNone||(i=t.children("li:not(."+d+")").length,l.noneView(t,i?"":s.text.none))}),l.renderForm("checkbox")},n.prototype.noneView=function(e,t){var n=c('<p class="layui-none">'+(t||"")+"</p>");e.find("."+s)[0]&&e.find("."+s).remove(),t.replace(/\s/g,"")&&e.append(n)},n.prototype.setValue=function(){var e=this.config,t=[];return this.layBox.eq(1).find("."+y+' input[type="checkbox"]').each(function(){c(this).data("hide")||t.push(this.value)}),e.value=t,this},n.prototype.parseData=function(t){var i=this.config,a=[];return layui.each(i.data,function(e,n){n=("function"==typeof i.parseData?i.parseData(n):n)||n,a.push(n=c.extend({},n)),layui.each(i.value,function(e,t){t==n.value&&(n.selected=!0)}),t&&t(n)}),i.data=a,this},n.prototype.getData=function(e){var t=this.config,i=[];return this.setValue(),layui.each(e||t.value,function(e,n){layui.each(t.data,function(e,t){delete t.selected,n==t.value&&i.push(t)})}),i},n.prototype.transfer=function(e,t){var n,i=this,a=i.config,o=i.layBox.eq(e),r=[];t?((n=t.find('input[type="checkbox"]'))[0].checked=!1,o.siblings("."+f).find("."+y).append(t.clone()),t.remove(),r.push(n[0].value),i.setValue()):o.each(function(e){c(this).find("."+y).children("li").each(function(){var e=c(this),t=e.find('input[type="checkbox"]'),n=t.data("hide");t[0].checked&&!n&&(t[0].checked=!1,o.siblings("."+f).find("."+y).append(e.clone()),e.remove(),r.push(t[0].value)),i.setValue()})}),i.renderCheckBtn(),""!==(t=o.siblings("."+f).find("."+h+" input")).val()&&t.trigger("keyup"),a.onchange&&a.onchange(i.getData(r),e)},n.prototype.events=function(){var a=this,o=a.config;a.elem.on("click",'input[lay-filter="layTransferCheckbox"]+',function(){var e=c(this).prev(),t=e[0].checked,n=e.parents("."+f).eq(0).find("."+y);e[0].disabled||("all"===e.attr("lay-type")&&n.find('input[type="checkbox"]').each(function(){this.disabled||(this.checked=t)}),setTimeout(function(){a.renderCheckBtn({stopNone:!0})},0))}),a.elem.on("dblclick","."+y+">li",function(e){var t=c(this),n=t.children('input[type="checkbox"]'),i=t.parent().parent().data("index");n[0].disabled||!1!==("function"==typeof o.dblclick?o.dblclick({elem:t,data:a.getData([n[0].value])[0],index:i}):null)&&a.transfer(i,t)}),a.layBtn.on("click",function(){var e=c(this),t=e.data("index");e.hasClass(u)||a.transfer(t)}),a.laySearch.find("input").on("keyup",function(){var i=this.value,e=c(this).parents("."+h).eq(0).siblings("."+y),t=((t=e.children("li")).each(function(){var e=c(this),t=e.find('input[type="checkbox"]'),n=t[0].title;"cs"!==o.showSearch&&(n=n.toLowerCase(),i=i.toLowerCase()),e[(n=-1!==n.indexOf(i))?"removeClass":"addClass"](d),t.data("hide",!n)}),a.renderCheckBtn(),t.length===e.children("li."+d).length);a.noneView(e,t?o.text.searchNone:"")})},i.that={},i.config={},l.reload=function(e,t){return(e=i.that[e]).reload(t),i.call(e)},l.getData=function(e){return i.that[e].getData()},l.render=function(e){return e=new n(e),i.call(e)},e(r,l)}),layui.define(["jquery","lay"],function(e){"use strict";function t(e){this.config=o.extend({},this.config,i.config,e),this.render()}var o=layui.$,r=layui.lay,i=(layui.hint(),layui.device(),{config:{},set:function(e){return this.config=o.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,s,e,t)}}),s="carousel",c="layui-this",d="layui-carousel-left",u="layui-carousel-right",f="layui-carousel-prev",p="layui-carousel-next",a="layui-carousel-arrow",l="layui-carousel-ind";t.prototype.config={width:"600px",height:"280px",full:!1,arrow:"hover",indicator:"inside",autoplay:!0,interval:3e3,anim:"",trigger:"click",index:0},t.prototype.render=function(){var e=this,t=e.config,n=o(t.elem);if(1<n.length)return layui.each(n,function(){i.render(o.extend({},t,{elem:this}))}),e;o.extend(t,r.options(n[0])),t.elem=o(t.elem),t.elem[0]&&(e.elemItem=t.elem.find(">*[carousel-item]>*"),t.index<0&&(t.index=0),t.index>=e.elemItem.length&&(t.index=e.elemItem.length-1),t.interval<800&&(t.interval=800),t.full?t.elem.css({position:"fixed",width:"100%",height:"100%",zIndex:9999}):t.elem.css({width:t.width,height:t.height}),t.elem.attr("lay-anim",t.anim),e.elemItem.eq(t.index).addClass(c),e.indicator(),e.arrow(),e.autoplay(),1<e.elemItem.length)&&e.events()},t.prototype.reload=function(e){clearInterval(this.timer),this.config=o.extend({},this.config,e),this.render()},t.prototype.prevIndex=function(){var e=this.config.index-1;return e<0?this.elemItem.length-1:e},t.prototype.nextIndex=function(){var e=this.config.index+1;return e>=this.elemItem.length?0:e},t.prototype.addIndex=function(e){var t=this.config;t.index=t.index+(e=e||1),t.index>=this.elemItem.length&&(t.index=0)},t.prototype.subIndex=function(e){var t=this.config;t.index=t.index-(e=e||1),t.index<0&&(t.index=this.elemItem.length-1)},t.prototype.autoplay=function(){var e=this,t=e.config,n=e.elemItem.length;t.autoplay&&(clearInterval(e.timer),1<n)&&(e.timer=setInterval(function(){e.slide()},t.interval))},t.prototype.arrow=function(){var t=this,e=t.config,n=t.elemItem.length,i=o(['<button type="button" class="layui-icon '+("updown"===e.anim?"layui-icon-up":"layui-icon-left")+" "+a+'" lay-type="sub"></button>','<button type="button" class="layui-icon '+("updown"===e.anim?"layui-icon-down":"layui-icon-right")+" "+a+'" lay-type="add"></button>'].join(""));e.elem.attr("lay-arrow",e.arrow),e.elem.find("."+a)[0]&&e.elem.find("."+a).remove(),1<n?e.elem.append(i):i.remove(),i.on("click",function(){var e=o(this).attr("lay-type");t.slide(e)})},t.prototype.goto=function(e){var t=this.config;e>t.index?this.slide("add",e-t.index):e<t.index&&this.slide("sub",t.index-e)},t.prototype.indicator=function(){var t,e=this,n=e.config,i=e.elemItem.length,a=e.elemInd=o(['<div class="'+l+'"><ul>',(t=[],layui.each(e.elemItem,function(e){t.push("<li"+(n.index===e?' class="layui-this"':"")+"></li>")}),t.join("")),"</ul></div>"].join(""));n.elem.attr("lay-indicator",n.indicator),n.elem.find("."+l)[0]&&n.elem.find("."+l).remove(),1<i?n.elem.append(a):a.remove(),"updown"===n.anim&&a.css("margin-top",-a.height()/2),a.find("li").on("hover"===n.trigger?"mouseover":n.trigger,function(){e.goto(o(this).index())})},t.prototype.slide=function(e,t){var n=this,i=n.elemItem,a=i.length,o=n.config,r=o.index,l=o.elem.attr("lay-filter");n.haveSlide||a<=1||("sub"===e?(n.subIndex(t),i.eq(o.index).addClass(f),setTimeout(function(){i.eq(r).addClass(u),i.eq(o.index).addClass(u)},50)):(n.addIndex(t),i.eq(o.index).addClass(p),setTimeout(function(){i.eq(r).addClass(d),i.eq(o.index).addClass(d)},50)),setTimeout(function(){i.removeClass(c+" "+f+" "+p+" "+d+" "+u),i.eq(o.index).addClass(c),n.haveSlide=!1},350),n.elemInd.find("li").eq(o.index).addClass(c).siblings().removeClass(c),n.haveSlide=!0,a={index:o.index,prevIndex:r,item:i.eq(o.index)},"function"==typeof o.change&&o.change(a),layui.event.call(this,s,"change("+l+")",a))},t.prototype.events=function(){var i,a,o=this,e=o.config;e.elem.data("haveEvents")||(e.elem.on("mouseenter touchstart",function(){"always"!==o.config.autoplay&&clearInterval(o.timer)}).on("mouseleave touchend",function(){"always"!==o.config.autoplay&&o.autoplay()}),i=e.elem,a="updown"===e.anim,r.touchSwipe(i,{onTouchEnd:function(e,t){var n=Date.now()-t.timeStart,t=a?t.distanceY:t.distanceX;(.25<Math.abs(t/n)||Math.abs(t)>i[a?"height":"width"]()/3)&&o.slide(0<t?"":"sub")}}),e.elem.data("haveEvents",!0))},i.render=function(e){return new t(e)},e(s,i)}),layui.define(["jquery","lay"],function(e){"use strict";function t(e){this.index=++c.index,this.config=l.extend({},this.config,c.config,e),this.render()}var l=layui.jquery,s=layui.lay,c={config:{},index:layui.rate?layui.rate.index+1e4:0,set:function(e){return this.config=l.extend({},this.config,e),this},on:function(e,t){return layui.onevent.call(this,n,e,t)}},n="rate",d="layui-icon-rate",u="layui-icon-rate-solid",r="layui-icon-rate-half",f="layui-icon-rate-solid layui-icon-rate-half",p="layui-icon-rate layui-icon-rate-half";t.prototype.config={length:5,text:!1,readonly:!1,half:!1,value:0,theme:""},t.prototype.render=function(){var e=this.config,t=l(e.elem);if(1<t.length)return layui.each(t,function(){c.render(l.extend({},e,{elem:this}))}),this;l.extend(e,s.options(t[0]));for(var n=e.theme?'style="color: '+e.theme+';"':"",i=(e.elem=l(e.elem),e.value>e.length&&(e.value=e.length),parseInt(e.value)===e.value||e.half||(e.value=Math.ceil(e.value)-e.value<.5?Math.ceil(e.value):Math.floor(e.value)),'<ul class="layui-rate" '+(e.readonly?"readonly":"")+">"),a=1;a<=e.length;a++){var o='<li class="layui-inline"><i class="layui-icon '+(a>Math.floor(e.value)?d:u)+'" '+n+"></i></li>";e.half&&parseInt(e.value)!==e.value&&a==Math.ceil(e.value)?i=i+'<li><i class="layui-icon layui-icon-rate-half" '+n+"></i></li>":i+=o}i+="</ul>"+(e.text?'<span class="layui-inline">'+e.value+"星":"")+"</span>";var r=(t=e.elem).next(".layui-rate");r[0]&&r.remove(),this.elemTemp=l(i),e.span=this.elemTemp.next("span"),e.setText&&e.setText(e.value),t.html(this.elemTemp),t.addClass("layui-inline"),e.readonly||this.action()},t.prototype.setvalue=function(e){this.config.value=e,this.render()},t.prototype.action=function(){var o=this.config,i=this.elemTemp,a=i.find("i").width(),n=i.children("li");n.each(function(e){var t=e+1,n=l(this);n.on("click",function(e){o.value=t,o.half&&e.pageX-l(this).offset().left<=a/2&&(o.value=o.value-.5),o.text&&i.next("span").text(o.value+"星"),o.choose&&o.choose(o.value),o.setText&&o.setText(o.value)}),n.on("mousemove",function(e){i.find("i").each(function(){l(this).addClass(d).removeClass(f)}),i.find("i:lt("+t+")").each(function(){l(this).addClass(u).removeClass(p)}),o.half&&e.pageX-l(this).offset().left<=a/2&&n.children("i").addClass(r).removeClass(u)}),n.on("mouseleave",function(){i.find("i").each(function(){l(this).addClass(d).removeClass(f)}),i.find("i:lt("+Math.floor(o.value)+")").each(function(){l(this).addClass(u).removeClass(p)}),o.half&&parseInt(o.value)!==o.value&&i.children("li:eq("+Math.floor(o.value)+")").children("i").addClass(r).removeClass("layui-icon-rate-solid layui-icon-rate")})}),s.touchSwipe(i,{onTouchMove:function(e,t){var a;Date.now()-t.timeStart<=200||(t=e.touches[0].pageX,e=i.width()/o.length,t=(t-i.offset().left)/e,(a=(a=(e=t%1)<=.5&&o.half?t-e+.5:Math.ceil(t))>o.length?o.length:a)<0&&(a=0),n.each(function(e){var t=l(this).children("i"),n=Math.ceil(a)-e==1,i=Math.ceil(a)>e,e=a-e==.5;i?(t.addClass(u).removeClass(p),o.half&&e&&t.addClass(r).removeClass(u)):t.addClass(d).removeClass(f),t.toggleClass("layui-rate-hover",n)}),o.value=a,o.text&&i.next("span").text(o.value+"星"),o.setText&&o.setText(o.value))},onTouchEnd:function(e,t){Date.now()-t.timeStart<=200||(i.find("i").removeClass("layui-rate-hover"),o.choose&&o.choose(o.value),o.setText&&o.setText(o.value))}})},t.prototype.events=function(){},c.render=function(e){return e=new t(e),function(){var t=this;return{setvalue:function(e){t.setvalue.call(t,e)},config:t.config}}.call(e)},e(n,c)}),layui.define("jquery",function(e){"use strict";function t(e){}var v=layui.$;t.prototype.load=function(e){var a,o,r,t,l,s,n,i,c,d,u,f,p,h,y,m=0,g=v((e=e||{}).elem);if(g[0])return l=v(e.scrollElem||document),s="mb"in e?e.mb:50,n=!("isAuto"in e)||e.isAuto,i=e.moreText||"加载更多",c=e.end||"没有更多了",d="top"===(e.direction||"bottom"),this._cleanup(g,l),u=e.scrollElem&&e.scrollElem!==document,p=v('<div class="layui-flow-more"><a href="javascript:;">'+(f="<cite>"+i+"</cite>")+"</a></div>"),g.find(".layui-flow-more")[0]||g[d?"prepend":"append"](p),h=function(e,t){var n=u?l.prop("scrollHeight"):document.documentElement.scrollHeight,i=l.scrollTop();e=v(e),p[d?"after":"before"](e),(t=0==t||null)?p.html(c):p.find("a").html(f),o=t,a=null,r&&r(),d&&(e=u?l.prop("scrollHeight"):document.documentElement.scrollHeight,1===m?l.scrollTop(e):1<m&&l.scrollTop(i+(e-n)))},(y=function(){a=!0,p.find("a").html('<i class="layui-anim layui-anim-rotate layui-anim-loop layui-icon ">&#xe63e;</i>'),"function"==typeof e.done&&e.done(++m,h)})(),p.find("a").on("click.flow",function(){v(this),o||a||y()}),e.isLazyimg&&(r=this.lazyimg({elem:e.elem+" img",scrollElem:e.scrollElem,direction:e.direction})),n&&l.on("scroll.flow",function(){var n=v(this),i=n.scrollTop();t&&clearTimeout(t),!o&&g.width()&&(t=setTimeout(function(){var e=(u?n:v(window)).height(),t=u?n.prop("scrollHeight"):document.documentElement.scrollHeight;(d?i<=s:t-i-e<=s)&&!a&&y()},100))}),this},t.prototype.lazyimg=function(e){var t,s=this,c=0,d=v((e=e||{}).scrollElem||document),u=e.elem||"img",o="top"===(e.direction||"bottom"),f=e.scrollElem&&e.scrollElem!==document,p=function(t,e){var n,i=d.scrollTop(),e=i+e,a=f?t.offset().top-d.offset().top+i:t.offset().top;(o?a+t.height():a)>=i&&a<=e&&t.attr("lay-src")&&(n=t.attr("lay-src"),layui.img(n,function(){var e=s.lazyimg.elem.eq(c);t.attr("src",n).removeAttr("lay-src"),e[0]&&r(e),c++},function(){s.lazyimg.elem.eq(c),t.removeAttr("lay-src")}))},r=function(e,t){var n=(f?t||d:v(window)).height(),i=d.scrollTop(),a=i+n;if(s.lazyimg.elem=v(u),e)p(e,n);else for(var o=0;o<s.lazyimg.elem.length;o++){var r=s.lazyimg.elem.eq(o),l=f?r.offset().top-d.offset().top+i:r.offset().top;if(p(r,n),c=o,a<l)break}};return r(),d.on("scroll.lazyimg",function(){var e=v(this);t&&clearTimeout(t),t=setTimeout(function(){r(null,e)},50)}),r},t.prototype._cleanup=function(e,t){t.off("scroll.flow").off("scroll.lazyimg"),e.find(".layui-flow-more").find("a").off("click.flow")},e("flow",new t)}),layui.define(["lay","util","element","form"],function(e){"use strict";function N(e){return String(e).replace(/\s+$/,"").replace(/^\n|\n$/,"")}var L=layui.$,A=layui.util,I=layui.element,M=layui.form,j=layui.layer,_=(layui.hint(),{ELEM_VIEW:"layui-code-view",ELEM_TAB:"layui-tab",ELEM_HEADER:"layui-code-header",ELEM_FULL:"layui-code-full",ELEM_PREVIEW:"layui-code-preview",ELEM_ITEM:"layui-code-item",ELEM_SHOW:"layui-show",ELEM_LINE:"layui-code-line",ELEM_LINE_NUM:"layui-code-line-number",ELEM_LN_MODE:"layui-code-ln-mode",CDDE_DATA_CLASS:"LayuiCodeDataClass",LINE_RAW_WIDTH:45}),H={elem:"",about:"",ln:!0,header:!1,encode:!0,copy:!0,text:{code:A.escape("</>"),preview:"Preview"},wordWrap:!0,lang:"text",highlighter:!1,langMarker:!1},F=layui.code?layui.code.index+1e4:0;e("code",function(i,e){var a,o,t,n,r,l,s,c,d,u,f,p,h,y,m,g,v,x,b,w,k,C,T,E,D={config:i=L.extend(!0,{},H,i),reload:function(e){layui.code(this.updateOptions(e))},updateOptions:function(e){return delete(e=e||{}).elem,L.extend(!0,i,e)},reloadCode:function(e){layui.code(this.updateOptions(e),"reloadCode")}},S=L(i.elem);return 1<S.length?layui.each(i.obverse?S:S.get().reverse(),function(){layui.code(L.extend({},i,{elem:this}),e)}):(a=i.elem=L(i.elem))[0]&&(L.extend(!0,i,lay.options(a[0]),(o={},layui.each(["title","height","encode","skin","about"],function(e,t){var n=a.attr("lay-"+t);"string"==typeof n&&(o[t]=n)}),o)),i.encode=(i.encode||i.preview)&&!i.codeRender,i.code=i.code||(t=[],a.children("textarea").each(function(){t.push(N(this.value))}),0===t.length&&t.push(N(a.html())),t.join("")),S=function(e){"function"==typeof i.codeRender&&(e=i.codeRender(String(e),i));var t=String(e).split(/\r?\n/g);return{lines:t,html:e=L.map(t,function(e,t){return['<div class="'+_.ELEM_LINE+'">',i.ln?['<div class="'+_.ELEM_LINE_NUM+'">',A.digit(t+1)+".","</div>"].join(""):"",'<div class="layui-code-line-content">',e||" ","</div>","</div>"].join("")})}},n=i.code,r=function(e){return"function"==typeof i.codeParse?i.codeParse(e,i):e},"reloadCode"===e?a.children(".layui-code-wrap").html(S(r(n)).html):(l=layui.code.index=++F,a.attr("lay-code-index",l),(w=_.CDDE_DATA_CLASS in a.data())&&a.attr("class",a.data(_.CDDE_DATA_CLASS)||""),w||a.data(_.CDDE_DATA_CLASS,a.attr("class")),s={copy:{className:"file-b",title:["复制代码"],event:function(e){var t=A.unescape(r(i.code));lay.clipboard.writeText({text:t,done:function(){j.msg("已复制",{icon:1})},error:function(){j.msg("复制失败",{icon:2})}}),"function"==typeof i.onCopy&&i.onCopy(t)}}},C=a.parent("."+_.ELEM_PREVIEW),T=C.children("."+_.ELEM_TAB),E=C.children("."+_.ELEM_ITEM+"-preview"),T.remove(),E.remove(),C[0]&&a.unwrap(),i.preview&&(w="LAY-CODE-DF-"+l,y=i.layout||["code","preview"],c="iframe"===i.preview,h=L('<div class="'+_.ELEM_PREVIEW+'">'),k=L('<div class="layui-tab layui-tab-brief">'),d=L('<div class="layui-tab-title">'),b=L('<div class="'+[_.ELEM_ITEM,_.ELEM_ITEM+"-preview","layui-border"].join(" ")+'">'),u=L('<div class="layui-code-tools"></div>'),i.id&&h.attr("id",i.id),h.addClass(i.className),k.attr("lay-filter",w),layui.each(y,function(e,t){var n=L('<li lay-id="'+t+'">');0===e&&n.addClass("layui-this"),n.html(i.text[t]),d.append(n)}),L.extend(s,{full:{className:"screen-full",title:["最大化显示","还原显示"],event:function(e){var t=(e=e.elem).closest("."+_.ELEM_PREVIEW),n="layui-icon-"+this.className,i="layui-icon-screen-restore",a=this.title,o=L("html,body"),r="layui-scrollbar-hide";e.hasClass(n)?(t.addClass(_.ELEM_FULL),e.removeClass(n).addClass(i),e.attr("title",a[1]),o.addClass(r)):(t.removeClass(_.ELEM_FULL),e.removeClass(i).addClass(n),e.attr("title",a[0]),o.removeClass(r))}},window:{className:"release",title:["在新窗口预览"],event:function(e){A.openWin({content:r(i.code)})}}}),i.copy&&("array"===layui.type(i.tools)?-1===i.tools.indexOf("copy")&&i.tools.unshift("copy"):i.tools=["copy"]),u.on("click",">i",function(){var e=(t=L(this)).data("type"),t={elem:t,type:e,options:i,rawCode:i.code,finalCode:A.unescape(r(i.code))};s[e]&&"function"==typeof s[e].event&&s[e].event(t),"function"==typeof i.toolsEvent&&i.toolsEvent(t)}),i.addTools&&i.tools&&(i.tools=[].concat(i.tools,i.addTools)),layui.each(i.tools,function(e,t){var n="object"==typeof t,i=n?t:s[t]||{className:t,title:[t]},a=i.className||i.type,o=i.title||[""];(n=n?i.type||a:t)&&(s[n]||((t={})[n]=i,L.extend(s,t)),u.append('<i class="layui-icon layui-icon-'+a+'" data-type="'+n+'" title="'+o[0]+'"></i>'))}),a.addClass(_.ELEM_ITEM).wrap(h),k.append(d),i.tools&&k.append(u),a.before(k),c&&b.html('<iframe allowtransparency="true" frameborder="0"></iframe>'),f=function(e){var t=e.children("iframe")[0];c&&t?t.srcdoc=r(i.code):e.html(i.code),setTimeout(function(){"function"==typeof i.done&&i.done({container:e,options:i,render:function(){M.render(e.find(".layui-form")),I.render()}})},3)},"preview"===y[0]?(b.addClass(_.ELEM_SHOW),a.before(b),f(b)):a.addClass(_.ELEM_SHOW).after(b),i.previewStyle=[i.style,i.previewStyle].join(""),b.attr("style",i.previewStyle),I.on("tab("+w+")",function(e){var t=L(this),n=L(e.elem).closest("."+_.ELEM_PREVIEW).find("."+_.ELEM_ITEM),e=n.eq(e.index);n.removeClass(_.ELEM_SHOW),e.addClass(_.ELEM_SHOW),"preview"===t.attr("lay-id")&&f(e),x()})),p=L('<code class="layui-code-wrap"></code>'),a.addClass((h=["layui-code-view layui-border-box"],i.wordWrap||h.push("layui-code-nowrap"),h.join(" "))),(k=i.theme||i.skin)&&(a.removeClass("layui-code-theme-dark layui-code-theme-light"),a.addClass("layui-code-theme-"+k)),i.highlighter&&a.addClass([i.highlighter,"language-"+i.lang,"layui-code-hl"].join(" ")),y=S(i.encode?A.escape(r(n)):n),m=y.lines,a.html(p.html(y.html)),i.ln&&a.append('<div class="layui-code-ln-side"></div>'),i.height&&p.css("max-height",i.height),i.codeStyle=[i.style,i.codeStyle].join(""),i.codeStyle&&p.attr("style",function(e,t){return(t||"")+i.codeStyle}),g=[{selector:">.layui-code-wrap>.layui-code-line{}",setValue:function(e,t){e.style["padding-left"]=t+"px"}},{selector:">.layui-code-wrap>.layui-code-line>.layui-code-line-number{}",setValue:function(e,t){e.style.width=t+"px"}},{selector:">.layui-code-ln-side{}",setValue:function(e,t){e.style.width=t+"px"}}],v=lay.style({target:a[0],id:"DF-code-"+l,text:L.map(L.map(g,function(e){return e.selector}),function(e,t){return['.layui-code-view[lay-code-index="'+l+'"]',e].join(" ")}).join("")}),x=function e(){var t,n;return i.ln&&(t=Math.floor(m.length/100),n=p.children("."+_.ELEM_LINE).last().children("."+_.ELEM_LINE_NUM).outerWidth(),a.addClass(_.ELEM_LN_MODE),t)&&_.LINE_RAW_WIDTH<n&&lay.getStyleRules(v,function(e,t){try{g[t].setValue(e,n)}catch(e){}}),e}(),i.header&&((b=L('<div class="'+_.ELEM_HEADER+'"></div>')).html(i.title||i.text.code),a.prepend(b)),w=L('<div class="layui-code-fixbar"></div>'),i.copy&&!i.preview&&((k=L(['<span class="layui-code-copy">','<i class="layui-icon layui-icon-file-b" title="复制"></i>',"</span>"].join(""))).on("click",function(){s.copy.event()}),w.append(k)),i.langMarker&&w.append('<span class="layui-code-lang-marker">'+i.lang+"</span>"),i.about&&w.append(i.about),a.append(w),i.preview||setTimeout(function(){"function"==typeof i.done&&i.done({})},3),i.elem.length===1+l&&"function"==typeof i.allDone&&i.allDone())),D})}),layui["layui.all"]||layui.addcss("modules/code.css?v=6","skincodecss");