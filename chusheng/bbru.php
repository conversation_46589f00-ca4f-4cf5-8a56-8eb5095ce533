<?php
include('../confing/common.php');
$redis=new Redis();
$redis->connect("127.0.0.1","6379");
$redis->select(8);

echo "连通redis： " . $redis->ping() . "\r\n";
    $lenth=$redis->LLEN('oidsjxz');
    if($lenth<=500){
        $i=0;
        $a=$DB->query("select * from qingka_wangke_order where dockstatus!=4 order by oid desc limit 1000");
        foreach($a as $b){
            $redis->lPush("oidsjxz",$b['oid']);
            $i++;
        }
        echo "入队成功！本次入队订单共计：".$i."条\r\n";
    }else {
        echo("入队失败！队列池还有：".$redis->LLEN('oidsjxz')."条订单正在执行\r\n");
        $list = $redis->lRange('oidsjxz', 0, -1); // 获取列表中的所有元素
    foreach($list as $id){
        echo $id . "\r\n";
    }
    }
?>