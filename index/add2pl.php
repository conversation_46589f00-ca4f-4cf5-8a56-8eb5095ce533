<?php
$id =$_GET['id'];
$title='新版查询';
require_once('head.php');
$addsalt=md5(mt_rand(0,999).time());
$_SESSION['addsalt']=$addsalt;

// 初始化选中的分类ID
$selectedCategoryId = isset($_GET['category']) ? $_GET['category'] : '';

// 弹窗内容配置
$recommendContent = [
    'title' => '推荐下单项目',
    'subtitle' => '为您精选的热门平台',
    'items' => [
        
        [
            'name' => '智慧树',
            'description' => '奶昔 TSL 龙龙 黑白。'
        ],
        [
            'name' => '学习通',
            'description' => '专业课：龙龙 至强 呱呱 嘿嘿。
选修课：星云 坤坤'
        ],
        [
            'name' => '智慧职教',
            'description' => '毛豆 欲梦。'
        ],
       
        [
            'name' => '英语系列',
            'description' => 'pup 茉莉 奶昔。'
        ],
         [
            'name' => '青书学堂，学起',
            'description' => '小青 欲梦 一流。'
         ],
          [
            'name' => '冷门项目',
            'description' => '龙龙 十优 欲梦。'
        ],

    ]
];
?>
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>FreeDom</title>
    <meta name="keywords" content="FreeDom" />
    <meta name="description" content="FreeDom" />
    
    <link rel="icon" href="assets/LightYear/favicon.ico" type="image/ico" />
    <link rel="stylesheet" href="assets/css/apps.css" type="text/css" />
    <link rel="stylesheet" href="assets/css/app.css" type="text/css" />
    <link rel="stylesheet" href="assets/layui/css/layui.css" type="text/css" />
    <link rel="stylesheet" href="assets/LightYear/js/bootstrap-multitabs/multitabs.min.css" />
    <link rel="stylesheet" href="assets/LightYear/css/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/LightYear/css/style.min.css" />
    <link rel="stylesheet" href="assets/LightYear/css/materialdesignicons.min.css" />
    <link rel="stylesheet" href="assets/js/font-awesome.min.css" />
    <link rel="stylesheet" href="assets/css/element.css" />
    <link rel="stylesheet" href="../assets/css/bootstrap.css" type="text/css" />
    <link rel="stylesheet" href="../assets/css/app.css" type="text/css" />
    <link rel="stylesheet" href="../assets/layui/css/layui.css" type="text/css" />
    <link rel="stylesheet" href="assets/css/index.css" />

    <script src="layer/3.1.1/layer.js"></script>
    <script src="../assets/js/jquery.min.js"></script>
    <script src="assets/index.js"></script>

    <script>
        $(document).ready(function(){
            // 初始化 Collapse 插件
            $('[data-toggle="collapse"]').collapse();
            
            // 显示推荐弹窗
            $('#recommendModal').show();
            
            // 初始化弹窗拖拽功能
            initDrag();
        });
        
        // 弹窗拖拽功能
        function initDrag() {
            var modal = $(".modal-container");
            var header = $(".modal-header");
            
            header.on("mousedown", function(e) {
                var isDragging = true;
                var startX = e.clientX;
                var startY = e.clientY;
                var origX = modal.offset().left;
                var origY = modal.offset().top;
                
                $(document).on("mousemove.drag", function(e) {
                    if (!isDragging) return;
                    
                    var newX = origX + (e.clientX - startX);
                    var newY = origY + (e.clientY - startY);
                    
                    // 限制在窗口范围内
                    newX = Math.max(0, Math.min(window.innerWidth - modal.outerWidth(), newX));
                    newY = Math.max(0, Math.min(window.innerHeight - modal.outerHeight(), newY));
                    
                    modal.css({
                        left: newX + "px",
                        top: newY + "px",
                        transform: "none"
                    });
                });
                
                $(document).on("mouseup", function() {
                    isDragging = false;
                    $(document).off("mousemove.drag");
                });
            });
        }
        
        // 重置弹窗位置到屏幕中心
        function resetModalPosition() {
            var modal = $(".modal-container");
            var modalWidth = modal.outerWidth();
            var modalHeight = modal.outerHeight();
            
            var left = (window.innerWidth - modalWidth) / 2;
            var top = (window.innerHeight - modalHeight) / 3; // 1/3位置显示
            
            modal.css({
                left: left + "px",
                top: top + "px",
                transform: "none"
            });
        }
    </script>
    <style type="text/css">
    /* 更新后的推荐弹窗样式（去除模糊效果） */
    #recommendModal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5); /* 半透明背景，无模糊 */
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 99999;
        /* 移除 backdrop-filter 属性 */
    }
        .modal-container {
            background: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translate(-50%, -20%);
            cursor: move;
            border: 1px solid #ebebeb;
        }
        .modal-header {
            background: #fff;
            color: #333;
            padding: 15px 20px;
            text-align: center;
            position: relative;
            cursor: move;
            border-bottom: 1px solid #ebebeb;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #0e78eF;
        }
        .modal-subtitle {
            font-size: 14px;
            margin-top: 5px;
            color: #666;
        }
        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            color: #999;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s;
            z-index: 10;
        }
        .close-btn:hover {
            color: #0e78eF;
            transform: rotate(90deg);
        }
        .modal-body {
            padding: 15px;
            max-height: 50vh;
            overflow-y: auto;
            background: #fff;
        }
        .recommend-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            background: #fff;
        }
        .recommend-item:last-child {
            border-bottom: none;
        }
        .item-name {
            font-weight: bold;
            color: #0e78eF;
            margin-bottom: 5px;
        }
        .item-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            white-space: pre-wrap;
        }
        .modal-footer {
            text-align: center;
            padding: 15px;
            border-top: 1px solid #eee;
            background: #fff;
        }
        .later-btn {
            background: #0e78eF;
            color: white;
            border: none;
            padding: 8px 25px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .later-btn:hover {
            background: #0c6ac8;
        }
        
        /* 更新后的查看推荐按钮样式 */
        .view-recommend-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: transparent;
            color: #666;
            border: 1px solid #ddd;
            padding: 4px 10px;
            border-radius: 4px;
            font-weight: normal;
            cursor: pointer;
            z-index: 100;
            font-size: 12px;
            transition: all 0.3s;
        }
        .view-recommend-btn:hover {
            color: #0e78eF;
            border-color: #0e78eF;
            background: transparent;
        }

        /* 原有样式保持不变 */
        .susuicon {
            position: absolute;
            left: 21px;
            top: 14px;
            width: 1.3em; 
            height: 1.3em;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
        }
        .frosss{    
            height: 38px;
            border-radius: 8px !important; 
            border: 2px solid rgb(236, 236, 236);
            border-color: #ebebeb;
            -webkit-border-radius: 2px;
            border-radius: 2px;
            padding: 5px 12px;
            line-height: inherit;
            -webkit-transition: 0.2s linear;
            transition: 0.2s linear;
            -webkit-box-shadow: none;
            box-shadow: none;
        }
        .frosss2{ 
            border-radius: 8px !important; 
            border: 2px solid rgb(236, 236, 236);
            display: block;
            width: 100%;
            height: 38px;
            border-color: #ebebeb;
            -webkit-border-radius: 2px;
            border-radius: 2px;
            padding: 5px 12px;
            line-height: inherit;
            -webkit-transition: 0.2s linear;
            transition: 0.2s linear;
            -webkit-box-shadow: none;
            box-shadow: none;
        }
        .category-container {
            display: flex;
            flex-wrap: wrap;
        }
        .category-container label {
            margin-right: 10px;
            margin-bottom: 5px;
            cursor: pointer;
        }
        .category-container input[type="radio"] {
            display: none;
        }
        .category-container input[type="radio"] + span {
            display: inline-block;
            padding: 8px 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }
        .category-container input[type="radio"]:checked + span {
            border-color: #0e78eF;
            color: #333;
        }
        .two-column-layout {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .main-panel {
            flex: 1;
            min-width: 300px;
            margin-right: 15px;
            position: relative;
        }
        .notice-panel {
            width: auto;
            max-width: 400px;
            flex-grow: 1;
        }
        @media (max-width: 768px) {
            .two-column-layout {
                flex-direction: column;
            }
            .main-panel, .notice-panel {
                margin-right: 0;
                margin-bottom: 15px;
                max-width: 100%;
            }
            .modal-container {
                width: 95%;
            }
        }
        .panel-equal-height {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .result-panel {
            height: 100%;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- 简化后的推荐项目弹窗 -->
    <div id="recommendModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title"><?= $recommendContent['title'] ?></h3>
                <div class="modal-subtitle"><?= $recommendContent['subtitle'] ?></div>
                <button class="close-btn" onclick="closeRecommendModal()">×</button>
            </div>
            <div class="modal-body">
                <?php foreach ($recommendContent['items'] as $item): ?>
                <div class="recommend-item">
                    <div class="item-name"><?= $item['name'] ?></div>
                    <div class="item-desc"><?= $item['description'] ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="modal-footer">
                <button class="later-btn" onclick="closeRecommendModal()">稍后查看</button>
            </div>
        </div>
    </div>
    
    <div class="app-content-body ">
        <div class="wrapper-md control" id="add">
            <!-- 双列布局开始 -->
            <div class="two-column-layout">
                <!-- 主面板（批量下单） -->
                <div class="main-panel">
                    <div class="panel panel-default panel-equal-height" style="box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius:8px; height:100%;">
                        <!-- 更新后的查看推荐按钮 -->
                        <button class="view-recommend-btn" onclick="showRecommendModal()">
                            <i class="glyphicon glyphicon-thumbs-up"></i> 推荐项目
                        </button>
                        
                        <div class="panel-heading font-bold " style="border-top-left-radius: 8px; border-top-right-radius: 8px;background-color:#fff;">
                            <div style="float:right;margin-right:20px"><el-link type="info"></el-link></div>
                            批量下单
                        </div>
                        <div class="panel-body" style="flex-grow:1;">
                            <form class="form-horizontal devform">
                                <?php if ($conf['flkg']=="1" && $conf['fllx']=="1") {?>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目分类</label>
                                        <div class="col-sm-9">
                                            <div class="category-container">
                                                <!-- 添加全部分类选项 -->
                                                <label>
                                                    <input type="radio" name="category" value="" @click="fenlei2('')" <?=($selectedCategoryId === '') ? 'checked' : ''?>>
                                                    <span class="category-name">全部分类</span>
                                                </label>
                                                <?php 
                                                $a = $DB->query("SELECT * FROM qingka_wangke_fenlei WHERE status=1 OR status=2  ORDER BY `sort` ASC");
                                                while ($rs = $DB->fetch($a)) {
                                                ?>
                                                <label>
                                                    <input type="radio" name="category" value="<?=$rs['id']?>" @click="fenlei2('<?=$rs['id']?>')" <?=($selectedCategoryId === $rs['id']) ? 'checked' : ''?>>
                                                    <span class="category-name"><?=$rs['name']?></span>
                                                </label>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php } else if ($conf['flkg']=="1" && $conf['fllx']=="2022") {?>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目分类</label>
                                        <div class="col-sm-9">
                                            <div class="category-container">
                                                <!-- 添加全部分类选项 -->
                                                <label>
                                                    <input type="radio" name="category" value="" @click="fenlei('')" <?=($selectedCategoryId === '') ? 'checked' : ''?>>
                                                    <span class="category-name">全部分类</span>
                                                </label>
                                                <?php 
                                                $a = $DB->query("SELECT * FROM qingka_wangke_fenlei WHERE status=1 OR status=2  ORDER BY `sort` ASC");
                                                while ($rs = $DB->fetch($a)) {
                                                ?>
                                                <label>
                                                    <input type="radio" name="category" value="<?=$rs['id']?>" @click="fenlei('<?=$rs['id']?>')" <?=($selectedCategoryId === $rs['id']) ? 'checked' : ''?>>
                                                    <span class="category-name"><?=$rs['name']?></span>
                                                </label>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php }?>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">选择平台</label>
                                    <div class="col-sm-9">
                                        <el-select 
                                            id="select" 
                                            v-model="cid" 
                                            @change="tips(cid)" 
                                            popper-class="lioverhide" 
                                            :popper-append-to-body="false" 
                                            filterable 
                                            :remote="selectedCategoryId === ''"
                                            :remote-method="selectedCategoryId === '' ? remoteSearch : null"
                                            :loading="loading"
                                            :placeholder="selectedCategoryId === '' ? '请输入关键词搜索平台' : '点击选择下单平台'" 
                                            style="background: url('../index/arrow.png') no-repeat scroll 99%;width:100%">
                                            <el-option
                                                v-for="class2 in class1"
                                                :key="class2.cid"
                                                :label="class2.name+'('+class2.price+'币)'"
                                                :value="class2.cid">
                                            </el-option>
                                        </el-select>                    
                                    </div>
                                </div>
                                
                                <div class="form-group" v-if="activems==true">
                                    <label class="col-sm-2 control-label" for="checkbox1">是否秒刷</label>
                                    <div class="col-sm-9">
                                        <div class="checkbox checkbox-success" @change="tips2">
                                            <input style="margin-left: 0px;" type="checkbox" v-model="miaoshua">
                                            <label for="checkbox1" id="miaoshua"></label>
                                        </div>
                                    </div>                            
                                </div>
                                <!--单学时备注定位点  在下面的v-if里面按格式添加cid即可-->
                                <div class="form-group " v-if="nochake==1">
                                    <div class="col-sm-2 col-xs-3 control-label" style="margin-top:9px">份数</div>
                                    <div class="col-sm-9 col-xs-8">
                                        <el-input-number v-model="shu" :min="1" :max="100"></el-input-number>
                                    </div>
                                    <div class="col-sm-2 col-xs-3 control-label" style="margin-top:9px">课程名称</div>
                                    <div class="col-sm-9 col-xs-8">
                                        <el-input v-model="bei" placeholder="请输入要刷的课程" style="margin-top:9px"></el-input>
                                    </div>
                                </div>
                                <!--单学时备注定位点-->
                                
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">信息填写</label>
                                    <div class="col-sm-9">
                                        <textarea rows="5" class="frosss2" style="height:120px;" v-model="userinfo"></textarea>    
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">网课说明</label>
                                    <div class="col-sm-9">
                                        <el-input placeholder="请选择商品查看说明" type="textarea" :autosize="{ minRows: 2, maxRows: 8 }" v-model="content">
                                        </el-input>
                                    </div>
                                </div>
                
                                <div class="col-sm-offset-2" v-if="nochake==1">
                                    <button style="margin-left: 6px; font-size: 13px;" type="button" @click="add" value="立即提交" class="el-button el-button--primary is-plain"><i class="glyphicon glyphicon-ok"></i>  立即提交</button>
                                </div>
                                <div class="col-sm-offset-2" v-else>
                                    <button style="margin-left: 6px; font-size: 13px;" type="button" @click="get" value="立即查询" class="el-button el-button--primary is-round"><i class="glyphicon glyphicon-zoom-in"></i>  立即查询</button>
                                    <button style="margin-left: 6px; font-size: 13px;" type="button" @click="add" value="立即提交" class="el-button el-button--primary is-round"><i class="glyphicon glyphicon-ok"></i>  立即提交</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 结果面板（有查询结果时显示结果，无结果时显示注意事项） -->
                <div class="notice-panel">
                    <!-- 有查询结果时显示查询结果 -->
                    <div v-if="row.length > 0" class="panel panel-default panel-equal-height result-panel" style="box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius:8px; height:100%;">
                        <div class="panel-heading font-bold " style="border-top-left-radius: 8px; border-top-right-radius: 8px;background-color:#fff;">
                            查询结果 &nbsp;
                            <a class="el-button el-button--primary is-plain el-button--mini" style="padding: 4px 10px;" @click="selectAll()">全选</a>&nbsp;
                            已勾选课程数量：{{ selectedCount }} &nbsp;
                        </div>
                        <div class="panel-body" style="overflow-y: auto;">
                            <form class="form-horizontal devform">        
                                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                    <div v-for="(rs,key) in row">
                                        <div class="panel panel-default">
                                            <div class="panel-heading" role="tab" id="headingOne">
                                                <h4 class="panel-title">                             
                                                    <a role="button" data-toggle="collapse" data-parent="#accordion" :href="'#'+key" aria-expanded="true">
                                                        <b>{{rs.userName}}</b> {{rs.userinfo}} <span v-if="rs.msg=='查询成功'"><b style="color: green;">{{rs.msg}}</b></span><span v-else-if="rs.msg!='查询成功'"><b style="color: red;">{{rs.msg}}</b></span>
                                                    </a>
                                                </h4>
                                            </div>
                                            
                                            <div :id="key" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">
                                                <div class="panel-body">
                                                    <div v-for="(res,key) in rs.data">
                                                        <label class="layui-table lyear-checkbox checkbox-inline checkbox-success">
                                                            <li>
                                                                <input style="margin-left: 0px;" :checked="checked" name="checkbox" type="checkbox" :value="res.name" @click="checkResources(rs.userinfo,rs.userName,rs.data,res.id,res.name)">
                                                                <span v-if="!!res.remark" style="color: red;">[{{res.remark}}]</span>
                                                                <span v-if="res.state=='已结束或者未开始' || res.state=='已结课或者未开始'" style="color: red;">【{{res.state}}】</span>
                                                                <span v-if="!!res.sort">【{{res.sort}}】</span>
                                                                <span>{{res.name}}</span>
                                                                <span v-if="res.credit !== null && res.credit !== undefined && res.credit !== ''"> 【{{res.credit}}】</span>
                                                                <span v-if="res.id !== null && res.id !== undefined && res.id !== ''">  ◼  [课程ID: {{res.id}}] </span>
                                                            </li>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>            
                            </form>
                        </div>
                    </div>
                    
                    <!-- 无查询结果时显示注意事项 -->
                    <div v-else class="panel panel-default panel-equal-height" style="box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius:8px; height:100%;">
                        <div class="panel-heading font-bold bg-white" style="border-radius: 10px;">下单前请确保已知晓下列注意事项</div>
                        <div class="panel-body" style="flex-grow:1;">
                            <ul class="layui-timeline">
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <p>请务必查看项目下单须知和说明，防止出现错误！</p>
                                    </div>
                                </li>
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <p>多账号下单必须换行，务必保证一行一条信息！</p>
                                    </div>
                                     </li>
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <p>客户自助查询地址https://yimengwk.top/get1</p>
                                    </div>
                                </li>
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <p>同商品重复下单，请修改密码后再下！</p>
                                    </div>
                                </li>
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <p>默认下单格式为学校、账号、密码(空格分开)！</p>
                                    </div>
                                </li>
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <p>本站所有项目均来源于互联网，非平台自营，下单后概不退款！</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 双列布局结束 -->
        </div>
    </div>
    
    <script>
        // 弹窗控制函数
        function showRecommendModal() {
            $('#recommendModal').show();
            resetModalPosition(); // 每次显示时重置位置
        }
        
        function closeRecommendModal() {
            $('#recommendModal').hide();
        }
        
        $(document).ready(function(){
            $("#btn4").click(function(){ 
                $("input[name='checkbox']").each(function(){ 
                    if($(this).attr("checked")) { 
                        $(this).removeAttr("checked"); 
                    } else { 
                        $(this).attr("checked","true"); 
                    } 
                }) 
            }); 
        }); 
    </script>

    <script type="text/javascript" src="assets/LightYear/js/jquery.min.js"></script>
    <script type="text/javascript" src="assets/LightYear/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="assets/LightYear/js/perfect-scrollbar.min.js"></script>
    <script type="text/javascript" src="assets/LightYear/js/main.min.js"></script>
    <script src="assets/js/aes.js"></script>
    <script src="../assets/js/vue.min.js"></script>
    <script src="assets/vue-resource.min.js"></script>
    <script src="assets/axios.min.js"></script>
    <script src="./assets/js/element.js"></script>

    <script>
        var vm = new Vue({
            el:"#add",
            data:{    
                row:[],
                shu:'',
                bei:'',
                nochake:0,
                check_row:[],
                selectedCount: 0,
                userinfo:'',
                cid:'',
                miaoshua:'',
                class1:'',
                class3:'',
                activems:false,
                checked:false,
                content:'',
                loading: false,
                selectedCategoryId: ''
            },
            methods:{
                get:function(salt){
                    if(this.cid=='' || this.userinfo==''){
                        layer.msg("所有项目不能为空");
                        return false;
                    }
                    userinfo=this.userinfo.replace(/\r\n/g, "[br]").replace(/\n/g, "[br]").replace(/\r/g, "[br]");                         
                    userinfo=userinfo.split('[br]');//分割
                    this.row=[];
                    this.check_row=[];
                    this.selectedCount = 0;  // 更新已勾选对象的数量
                    
                    // 显示查询加载动画
                    var loading = layer.load(0);
                    
                    for(var i=0;i<userinfo.length;i++){    
                        info=userinfo[i]
                        if(info==''){continue;}
                        var hash=getENC('<?php echo $addsalt;?>');
                        this.$http.post("/apisub.php?act=get",{cid:this.cid,userinfo:info,hash},{emulateJSON:true}).then(function(data){
                            if(data.body.code==-7){
                                salt=getENC(data.body.msg);
                                vm.get(salt);
                            }else{
                                this.row.push(data.body);
                            }
                            
                            // 关闭查询加载动画
                            layer.close(loading);
                        });
                    }                     
                },
                add:function(){
                    if(this.cid==''){
                        if(this.nochake!=1){
                            layer.msg("请先查课");
                            return false;
                        }
                    }     
                    if(this.check_row.length<1){
                        if(this.nochake!=1){
                            layer.msg("请先选择课程");
                            return false;
                        }
                    }     
                    
                    // 显示提交加载动画
                    var loading = layer.load();
                    
                    this.$http.post("/apisub.php?act=add",{cid:this.cid,data:this.check_row,shu:this.shu,bei:this.bei,userinfo:this.userinfo,nochake:this.nochake},{emulateJSON:true}).then(function(data){
                        // 关闭提交加载动画
                        layer.close(loading);
                        
                        if(data.data.code==1){
                            this.row=[];
                            this.check_row=[]; 
                            this.selectedCount = 0;
                            this.$message({type: 'success', showClose: true,message: data.data.msg});
                        }else{
                            this.$message({type: 'error', showClose: true,message: data.data.msg});
                        }
                    });
                },
                fenlei:function(id){
                    // 保存当前选中的分类ID
                    this.selectedCategoryId = id;
                    
                    if (id === '') {
                        // 全部分类：清空商品列表，等待用户搜索
                        this.class1 = [];
                        this.cid = '';
                        this.content = '';
                        this.activems = false;
                    } else {
                        // 具体分类：正常加载该分类下的所有商品
                        var loading = layer.load(2);
                        this.$http.post("/apisub.php?act=getclassfl",{id:id},{emulateJSON:true}).then(function(data){    
                            layer.close(loading); // 关闭加载动画
                            this.loading = false;
                            
                            if(data.data.code==1){                                     
                                this.class1=data.data.data;                                 
                            }else{
                                this.class1 = [];
                                layer.msg(data.data.msg,{icon:2});
                            }
                        }).catch(function() {
                            layer.close(loading); // 关闭加载动画
                            this.loading = false;
                            this.class1 = [];
                        });
                    }
                },
                fenlei2:function(id){
                    // 保存当前选中的分类ID
                    this.selectedCategoryId = id;
                    
                    if (id === '') {
                        // 全部分类：清空商品列表，等待用户搜索
                        this.class1 = [];
                        this.cid = '';
                        this.content = '';
                        this.activems = false;
                    } else {
                        // 具体分类：正常加载该分类下的所有商品
                        var loading = layer.load(0);
                        this.$http.post("/apisub.php?act=getclassfl",{id:id},{emulateJSON:true}).then(function(data){    
                            layer.close(loading); // 关闭加载动画
                            if(data.data.code==1){                                     
                                this.class1=data.data.data;                                 
                            }else{
                                this.class1 = [];
                                layer.msg(data.data.msg,{icon:2});
                            }
                        }).catch(function() {
                            layer.close(loading);
                            this.class1 = [];
                        });
                    }
                },
                getclass:function(){
                    // 初始加载不显示动画
                    this.$http.post("/apisub.php?act=getclass").then(function(data){    
                        if(data.data.code==1){                                     
                            this.class1=data.body.data;                                 
                        }else{
                            layer.msg(data.data.msg,{icon:2});
                        }
                    });    
                    
                },
                selectAll:function() {
                    if (this.cid === '') {
                        layer.msg("请先查课");
                        return false;
                    }
                
                    this.checked = !this.checked;  // 切换全选状态
                
                    if (this.checked) {
                        // 全选逻辑：遍历所有行，将它们添加到 check_row 中
                        for (let i = 0; i < this.row.length; i++) {
                            const userinfo = this.row[i].userinfo;
                            const userName = this.row[i].userName;
                            const rs = this.row[i].data;
                            this.check_row = [];
                            for (let a = 0; a < rs.length; a++) {
                                const aa = rs[a];
                                const data = { userinfo, userName, data: aa };
                                this.check_row.push(data);
                            }
                        }
                        this.selectedCount = 0;
                        this.selectedCount = this.check_row.length;  // 更新已勾选对象的数量
                    } else {
                        // 取消全选逻辑：清空 check_row 数组
                        this.check_row = [];
                        this.selectedCount = 0;  // 更新已勾选对象的数量
                    }
                
                    console.log(this.check_row);  // 调试输出
                },
                checkResources:function(userinfo, userName, rs, id, name) {
                    let found = false;
                    for (let i = 0; i < rs.length; i++) {
                        if (id === "") {
                            if (rs[i].name === name) {
                                found = rs[i];
                                break;
                            }
                        } else {
                            if (rs[i].id === id && rs[i].name === name) {
                                found = rs[i];
                                break;
                            }
                        }
                    }
                
                    if (found) {
                        const data = { userinfo, userName, data: found };
                        const index = this.check_row.findIndex(item => item.userinfo === userinfo && item.data.name === found.name);
                
                        if (index === -1) {
                            // 如果未找到，添加新元素
                            this.check_row.push(data);
                            this.selectedCount++;
                        } else {
                            // 如果已存在，删除元素
                            this.check_row.splice(index, 1);
                            this.selectedCount--;
                        }
                    }
                },
                getnock:function(cid){
                    this.$http.post("/apisub.php?act=getnock").then(function(data){    
                        if(data.data.code==1){                                     
                            this.nock=data.body.data;    
                            for(i=0;this.nock.length>i;i++){
                                if(cid==this.nock[i].cid){
                                    this.nochake=1;
                                    break;
                                }else{
                                    this.nochake=0;
                                }
                            }
                        }else{
                            layer.msg(data.data.msg,{icon:2});
                        }
                    });    
                    
                },
                tips: function (message) {
                    for(var i=0;this.class1.length>i;i++){
                        if(this.class1[i].cid==message){
                            this.show = true;
                            this.content = this.class1[i].price+'币 '+'说明：'+this.class1[i].content;
                            if(this.class1[i].miaoshua==1){
                                this.activems=true;
                            }else{
                                this.activems=false;
                            }
                            return false;
                        }
                    }
                },
                tips2: function () {
                    layer.tips('开启秒刷将额外收0.05的费用', '#miaoshua');            
                },
                remoteSearch: function(query) {
                    // 只有在全部分类时才启用远程搜索
                    if (this.selectedCategoryId !== '') {
                        return;
                    }
                    
                    // 如果搜索关键词为空，清空选项
                    if (query === '') {
                        this.class1 = [];
                        return;
                    }
                    
                    // 显示加载状态
                    this.loading = true;
                    
                    // 发送搜索请求
                    this.$http.post("/apisub.php?act=searchclass", {
                        keyword: query,
                        category: ''
                    }, {emulateJSON: true}).then(function(data) {
                        this.loading = false;
                        
                        if (data.data.code == 1) {
                            this.class1 = data.data.data;
                        } else {
                            this.class1 = [];
                            layer.msg(data.data.msg, {icon: 2});
                        }
                    }).catch(function() {
                        this.loading = false;
                        this.class1 = [];
                    });
                }
            },
            mounted(){
                // 移除初始加载，改为搜索时加载
                // this.getclass();       
            }
        });
    </script>
</body>
</html>
