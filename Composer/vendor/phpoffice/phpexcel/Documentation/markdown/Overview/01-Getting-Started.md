# PHPExcel Developer Documentation


## Prerequisites, Installation, FAQ and Links

### Software requirements

The following software is required to develop using PHPExcel:

 - PHP version 5.2.0 or newer
 - PHP extension php_zip enabled [^phpzip_footnote]
 - PHP extension php_xml enabled
 - PHP extension php_gd2 enabled (if not compiled in)


### Installation instructions

Installation is quite easy: copy the contents of the Classes folder to any location within your application source directories.

*Example:*

If your web root folder is /var/www/ you may want to create a subfolder called /var/www/Classes/ and copy the files into that folder so you end up with files:

    /var/www/Classes/PHPExcel.php  
    /var/www/Classes/PHPExcel/Calculation.php  
    /var/www/Classes/PHPExcel/Cell.php  
    ...  


### Getting started

A good way to get started is to run some of the tests included in the download.  
Copy the "Examples" folder next to your "Classes" folder from above so you end up with:

    /var/www/Examples/01simple.php  
    /var/www/Examples/02types.php  
    ...  

Start running the tests by pointing your browser to the test scripts:

http://example.com/Tests/01simple.php  
http://example.com/Tests/02types.php  
...  

**Note:** It may be necessary to modify the include/require statements at the beginning of each of the test scripts if your "Classes" folder from above is named differently.


### Useful links and tools

There are some links and tools which are very useful when developing using PHPExcel. Please refer to the [PHPExcel CodePlex pages][2] for an update version of the list below.

#### OpenXML / SpreadsheetML

 - __File format documentation__  
[http://www.ecma-international.org/news/TC45_current_work/TC45_available_docs.htm][3]
 - __OpenXML Explained e-book__  
[http://openxmldeveloper.org/articles/1970.aspx][4] 
 - __Microsoft Office Compatibility Pack for Word, Excel, and PowerPoint 2007 File Formats__  
[http://www.microsoft.com/downloads/details.aspx?familyid=941b3470-3ae9-4aee-8f43-c6bb74cd1466&displaylang=en][5]
 - __OpenXML Package Explorer__  
[http://www.codeplex.com/PackageExplorer/][6]


### Frequently asked questions

The up-to-date F.A.Q. page for PHPExcel can be found on [http://www.codeplex.com/PHPExcel/Wiki/View.aspx?title=FAQ&referringTitle=Requirements][7].

##### There seems to be a problem with character encoding...

It is necessary to use UTF-8 encoding for all texts in PHPExcel. If the script uses different encoding then you can convert those texts with PHP's iconv() or mb_convert_encoding() functions.

##### PHP complains about ZipArchive not being found

Make sure you meet all requirements, especially php_zip extension should be enabled.

The ZipArchive class is only required when reading or writing formats that use Zip compression (Excel2007 and OOCalc). Since version 1.7.6 the PCLZip library has been bundled with PHPExcel as an alternative to the ZipArchive class.

This can be enabled by calling:
```php
PHPExcel_Settings::setZipClass(PHPExcel_Settings::PCLZIP);
```
*before* calling the save method of the Excel2007 Writer.

You can revert to using ZipArchive by calling:
```php
PHPExcel_Settings::setZipClass(PHPExcel_Settings::ZIPARCHIVE);
```
At present, this only allows you to write Excel2007 files without the need for ZipArchive (not to read Excel2007 or OOCalc)

##### Excel 2007 cannot open the file generated by PHPExcel_Writer_2007 on Windows

"Excel found unreadable content in '*.xlsx'. Do you want to recover the contents of this workbook? If you trust the source of this workbook, click Yes."

Some older versions of the 5.2.x php_zip extension on Windows contain an error when creating ZIP files. The version that can be found on [http://snaps.php.net/win32/php5.2-win32-latest.zip][8] should work at all times.

Alternatively, upgrading to at least PHP 5.2.9 should solve the problem.

If you can't locate a clean copy of ZipArchive, then you can use the PCLZip library as an alternative when writing Excel2007 files, as described above.

##### Fatal error: Allowed memory size of xxx bytes exhausted (tried to allocate yyy bytes) in zzz on line aaa

PHPExcel holds an "in memory" representation of a spreadsheet, so it is susceptible to PHP's memory limitations. The memory made available to PHP can be increased by editing the value of the memory_limit directive in your php.ini file, or by using ini_set('memory_limit', '128M') within your code (ISP permitting).

Some Readers and Writers are faster than others, and they also use differing amounts of memory. You can find some indication of the relative performance and memory usage for the different Readers and Writers, over the different versions of PHPExcel, on the [discussion board][9].

If you've already increased memory to a maximum, or can't change your memory limit, then [this discussion][10] on the board describes some of the methods that can be applied to reduce the memory usage of your scripts using PHPExcel.

##### Protection on my worksheet is not working?

When you make use of any of the worksheet protection features (e.g. cell range protection, prohibiting deleting rows, ...), make sure you enable worksheet security. This can for example be done like this:
```php
$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
```

##### Feature X is not working with PHPExcel_Reader_Y / PHPExcel_Writer_Z

Not all features of PHPExcel are implemented in all of the Reader / Writer classes. This is mostly due to underlying libraries not supporting a specific feature or not having implemented a specific feature.

For example autofilter is not implemented in PEAR Spreadsheet_Excel_writer, which is the base of our Excel5 writer.

We are slowly building up a list of features, together with the different readers and writers that support them, in the "Functionality Cross-Reference.xls" file in the /Documentation folder.

##### Formulas don't seem to be calculated in Excel2003 using compatibility pack?

This is normal behaviour of the compatibility pack, Excel2007 displays this correctly. Use PHPExcel_Writer_Excel5 if you really need calculated values, or force recalculation in Excel2003.

##### Setting column width is not 100% accurate

Trying to set column width, I experience one problem. When I open the file in Excel, the actual width is 0.71 less than it should be.

The short answer is that PHPExcel uses a measure where padding is included. See section: "Setting a column's width" for more details.

##### How do I use PHPExcel with my framework

 - There are some instructions for using PHPExcel with Joomla on the [Joomla message board][11]
 - A page of advice on using [PHPExcel in the Yii framework][12]
 - [The Bakery][13] has some helper classes for reading and writing with PHPExcel within CakePHP
 - Integrating [PHPExcel into Kohana 3][14] and [?????????? PHPExcel ? Kohana Framework][15]
 - Using [PHPExcel with Typo3][16]

##### Joomla Autoloader interferes with PHPExcel Autoloader

Thanks to peterrlynch for the following advice on resolving issues between the [PHPExcel autoloader and Joomla Autoloader][17]


#### Tutorials

 - __English PHPExcel tutorial__  
   [http://openxmldeveloper.org][18]
 - __French PHPExcel tutorial__  
   [http://g-ernaelsten.developpez.com/tutoriels/excel2007/][19]
 - __A Japanese-language introduction to PHPExcel__  
  [http://journal.mycom.co.jp/articles/2009/03/06/phpexcel/index.html][21]


  [2]: http://www.codeplex.com/PHPExcel/Wiki/View.aspx?title=Documents&referringTitle=Home
  [3]: http://www.ecma-international.org/news/TC45_current_work/TC45_available_docs.htm
  [4]: http://openxmldeveloper.org/articles/1970.aspx
  [5]: http://www.microsoft.com/downloads/details.aspx?familyid=941b3470-3ae9-4aee-8f43-c6bb74cd1466&displaylang=en
  [6]: http://www.codeplex.com/PackageExplorer/
  [7]: http://www.codeplex.com/PHPExcel/Wiki/View.aspx?title=FAQ&referringTitle=Requirements
  [8]: http://snaps.php.net/win32/php5.2-win32-latest.zip
  [9]: http://phpexcel.codeplex.com/Thread/View.aspx?ThreadId=234150
  [10]: http://phpexcel.codeplex.com/Thread/View.aspx?ThreadId=242712
  [11]: http://http:/forum.joomla.org/viewtopic.php?f=304&t=433060
  [12]: http://www.yiiframework.com/wiki/101/how-to-use-phpexcel-external-library-with-yii/
  [13]: http://bakery.cakephp.org/articles/melgior/2010/01/26/simple-excel-spreadsheet-helper
  [14]: http://www.flynsarmy.com/2010/07/phpexcel-module-for-kohana-3/
  [15]: http://szpargalki.blogspot.com/2011/02/phpexcel-kohana-framework.html
  [16]: http://typo3.org/documentation/document-library/extension-manuals/phpexcel_library/1.1.1/view/toc/0/
  [17]: http://phpexcel.codeplex.com/discussions/211925
  [18]: http://openxmldeveloper.org
  [19]: http://g-ernaelsten.developpez.com/tutoriels/excel2007/
  [20]: http://www.web-junior.net/sozdanie-excel-fajjlov-s-pomoshhyu-phpexcel/
  [21]: http://journal.mycom.co.jp/articles/2009/03/06/phpexcel/index.html


[^phpzip_footnote]: __php_zip__ is only needed by __PHPExcel_Reader_Excel2007__, __PHPExcel_Writer_Excel2007__ and __PHPExcel_Reader_OOCalc__. In other words, if you need PHPExcel to handle .xlsx or .ods files you will need the zip extension, but otherwise not.<br />You can remove this dependency for writing Excel2007 files (though not yet for reading) by using the PCLZip library that is bundled with PHPExcel. See the FAQ section of this document for details about this. PCLZip does have a dependency on PHP's zlib extension being enabled.

