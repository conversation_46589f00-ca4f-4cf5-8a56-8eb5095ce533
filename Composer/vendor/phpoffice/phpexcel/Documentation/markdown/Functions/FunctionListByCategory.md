## CATEGORY_CUBE

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	CU<PERSON>KPIMEMBER       | **\*\*\*  Not yet Implemented**
	CUBEMEMBER          | **\*\*\*  Not yet Implemented**
	CUBEMEMBERPROPERTY  | **\*\*\*  Not yet Implemented**
	CUBERANKEDMEMBER    | **\*\*\*  Not yet Implemented**
	CUBESET             | **\*\*\*  Not yet Implemented**
	CUBESETCOUNT        | **\*\*\*  Not yet Implemented**
	CUBEVALUE           | **\*\*\*  Not yet Implemented**

## CATEGORY_DATABASE

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	DAVERAGE            | PHPExcel_Calculation_Database::DAVERAGE
	DCOUNT              | PHPExcel_Calculation_Database::DCOUNT
	DCOUNTA             | PHPExcel_Calculation_Database::DCOUNTA
	DGET                | PHPExcel_Calculation_Database::DGET
	DMAX                | PHPExcel_Calculation_Database::DMAX
	DMIN                | PHPExcel_Calculation_Database::DMIN
	DPRODUCT            | PHPExcel_Calculation_Database::DPRODUCT
	DSTDEV              | PHPExcel_Calculation_Database::DSTDEV
	DSTDEVP             | PHPExcel_Calculation_Database::DSTDEVP
	DSUM                | PHPExcel_Calculation_Database::DSUM
	DVAR                | PHPExcel_Calculation_Database::DVAR
	DVARP               | PHPExcel_Calculation_Database::DVARP

## CATEGORY_DATE_AND_TIME

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	DATE                | PHPExcel_Calculation_DateTime::DATE
	DATEDIF             | PHPExcel_Calculation_DateTime::DATEDIF
	DATEVALUE           | PHPExcel_Calculation_DateTime::DATEVALUE
	DAY                 | PHPExcel_Calculation_DateTime::DAYOFMONTH
	DAYS360             | PHPExcel_Calculation_DateTime::DAYS360
	EDATE               | PHPExcel_Calculation_DateTime::EDATE
	EOMONTH             | PHPExcel_Calculation_DateTime::EOMONTH
	HOUR                | PHPExcel_Calculation_DateTime::HOUROFDAY
	MINUTE              | PHPExcel_Calculation_DateTime::MINUTEOFHOUR
	MONTH               | PHPExcel_Calculation_DateTime::MONTHOFYEAR
	NETWORKDAYS         | PHPExcel_Calculation_DateTime::NETWORKDAYS
	NOW                 | PHPExcel_Calculation_DateTime::DATETIMENOW
	SECOND              | PHPExcel_Calculation_DateTime::SECONDOFMINUTE
	TIME                | PHPExcel_Calculation_DateTime::TIME
	TIMEVALUE           | PHPExcel_Calculation_DateTime::TIMEVALUE
	TODAY               | PHPExcel_Calculation_DateTime::DATENOW
	WEEKDAY             | PHPExcel_Calculation_DateTime::DAYOFWEEK
	WEEKNUM             | PHPExcel_Calculation_DateTime::WEEKOFYEAR
	WORKDAY             | PHPExcel_Calculation_DateTime::WORKDAY
	YEAR                | PHPExcel_Calculation_DateTime::YEAR
	YEARFRAC            | PHPExcel_Calculation_DateTime::YEARFRAC

## CATEGORY_ENGINEERING

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	BESSELI             | PHPExcel_Calculation_Engineering::BESSELI
	BESSELJ             | PHPExcel_Calculation_Engineering::BESSELJ
	BESSELK             | PHPExcel_Calculation_Engineering::BESSELK
	BESSELY             | PHPExcel_Calculation_Engineering::BESSELY
	BIN2DEC             | PHPExcel_Calculation_Engineering::BINTODEC
	BIN2HEX             | PHPExcel_Calculation_Engineering::BINTOHEX
	BIN2OCT             | PHPExcel_Calculation_Engineering::BINTOOCT
	COMPLEX             | PHPExcel_Calculation_Engineering::COMPLEX
	CONVERT             | PHPExcel_Calculation_Engineering::CONVERTUOM
	DEC2BIN             | PHPExcel_Calculation_Engineering::DECTOBIN
	DEC2HEX             | PHPExcel_Calculation_Engineering::DECTOHEX
	DEC2OCT             | PHPExcel_Calculation_Engineering::DECTOOCT
	DELTA               | PHPExcel_Calculation_Engineering::DELTA
	ERF                 | PHPExcel_Calculation_Engineering::ERF
	ERFC                | PHPExcel_Calculation_Engineering::ERFC
	GESTEP              | PHPExcel_Calculation_Engineering::GESTEP
	HEX2BIN             | PHPExcel_Calculation_Engineering::HEXTOBIN
	HEX2DEC             | PHPExcel_Calculation_Engineering::HEXTODEC
	HEX2OCT             | PHPExcel_Calculation_Engineering::HEXTOOCT
	IMABS               | PHPExcel_Calculation_Engineering::IMABS
	IMAGINARY           | PHPExcel_Calculation_Engineering::IMAGINARY
	IMARGUMENT          | PHPExcel_Calculation_Engineering::IMARGUMENT
	IMCONJUGATE         | PHPExcel_Calculation_Engineering::IMCONJUGATE
	IMCOS               | PHPExcel_Calculation_Engineering::IMCOS
	IMDIV               | PHPExcel_Calculation_Engineering::IMDIV
	IMEXP               | PHPExcel_Calculation_Engineering::IMEXP
	IMLN                | PHPExcel_Calculation_Engineering::IMLN
	IMLOG10             | PHPExcel_Calculation_Engineering::IMLOG10
	IMLOG2              | PHPExcel_Calculation_Engineering::IMLOG2
	IMPOWER             | PHPExcel_Calculation_Engineering::IMPOWER
	IMPRODUCT           | PHPExcel_Calculation_Engineering::IMPRODUCT
	IMREAL              | PHPExcel_Calculation_Engineering::IMREAL
	IMSIN               | PHPExcel_Calculation_Engineering::IMSIN
	IMSQRT              | PHPExcel_Calculation_Engineering::IMSQRT
	IMSUB               | PHPExcel_Calculation_Engineering::IMSUB
	IMSUM               | PHPExcel_Calculation_Engineering::IMSUM
	OCT2BIN             | PHPExcel_Calculation_Engineering::OCTTOBIN
	OCT2DEC             | PHPExcel_Calculation_Engineering::OCTTODEC
	OCT2HEX             | PHPExcel_Calculation_Engineering::OCTTOHEX

## CATEGORY_FINANCIAL

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	ACCRINT             | PHPExcel_Calculation_Financial::ACCRINT
	ACCRINTM            | PHPExcel_Calculation_Financial::ACCRINTM
	AMORDEGRC           | PHPExcel_Calculation_Financial::AMORDEGRC
	AMORLINC            | PHPExcel_Calculation_Financial::AMORLINC
	COUPDAYBS           | PHPExcel_Calculation_Financial::COUPDAYBS
	COUPDAYS            | PHPExcel_Calculation_Financial::COUPDAYS
	COUPDAYSNC          | PHPExcel_Calculation_Financial::COUPDAYSNC
	COUPNCD             | PHPExcel_Calculation_Financial::COUPNCD
	COUPNUM             | PHPExcel_Calculation_Financial::COUPNUM
	COUPPCD             | PHPExcel_Calculation_Financial::COUPPCD
	CUMIPMT             | PHPExcel_Calculation_Financial::CUMIPMT
	CUMPRINC            | PHPExcel_Calculation_Financial::CUMPRINC
	DB                  | PHPExcel_Calculation_Financial::DB
	DDB                 | PHPExcel_Calculation_Financial::DDB
	DISC                | PHPExcel_Calculation_Financial::DISC
	DOLLARDE            | PHPExcel_Calculation_Financial::DOLLARDE
	DOLLARFR            | PHPExcel_Calculation_Financial::DOLLARFR
	DURATION            | **\*\*\*  Not yet Implemented**
	EFFECT              | PHPExcel_Calculation_Financial::EFFECT
	FV                  | PHPExcel_Calculation_Financial::FV
	FVSCHEDULE          | PHPExcel_Calculation_Financial::FVSCHEDULE
	INTRATE             | PHPExcel_Calculation_Financial::INTRATE
	IPMT                | PHPExcel_Calculation_Financial::IPMT
	IRR                 | PHPExcel_Calculation_Financial::IRR
	ISPMT               | PHPExcel_Calculation_Financial::ISPMT
	MDURATION           | **\*\*\*  Not yet Implemented**
	MIRR                | PHPExcel_Calculation_Financial::MIRR
	NOMINAL             | PHPExcel_Calculation_Financial::NOMINAL
	NPER                | PHPExcel_Calculation_Financial::NPER
	NPV                 | PHPExcel_Calculation_Financial::NPV
	ODDFPRICE           | **\*\*\*  Not yet Implemented**
	ODDFYIELD           | **\*\*\*  Not yet Implemented**
	ODDLPRICE           | **\*\*\*  Not yet Implemented**
	ODDLYIELD           | **\*\*\*  Not yet Implemented**
	PMT                 | PHPExcel_Calculation_Financial::PMT
	PPMT                | PHPExcel_Calculation_Financial::PPMT
	PRICE               | PHPExcel_Calculation_Financial::PRICE
	PRICEDISC           | PHPExcel_Calculation_Financial::PRICEDISC
	PRICEMAT            | PHPExcel_Calculation_Financial::PRICEMAT
	PV                  | PHPExcel_Calculation_Financial::PV
	RATE                | PHPExcel_Calculation_Financial::RATE
	RECEIVED            | PHPExcel_Calculation_Financial::RECEIVED
	SLN                 | PHPExcel_Calculation_Financial::SLN
	SYD                 | PHPExcel_Calculation_Financial::SYD
	TBILLEQ             | PHPExcel_Calculation_Financial::TBILLEQ
	TBILLPRICE          | PHPExcel_Calculation_Financial::TBILLPRICE
	TBILLYIELD          | PHPExcel_Calculation_Financial::TBILLYIELD
	USDOLLAR            | **\*\*\*  Not yet Implemented**
	VDB                 | **\*\*\*  Not yet Implemented**
	XIRR                | PHPExcel_Calculation_Financial::XIRR
	XNPV                | PHPExcel_Calculation_Financial::XNPV
	YIELD               | **\*\*\*  Not yet Implemented**
	YIELDDISC           | PHPExcel_Calculation_Financial::YIELDDISC
	YIELDMAT            | PHPExcel_Calculation_Financial::YIELDMAT

## CATEGORY_INFORMATION

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	CELL                | **\*\*\*  Not yet Implemented**
	ERROR.TYPE          | PHPExcel_Calculation_Functions::ERROR_TYPE
	INFO                | **\*\*\*  Not yet Implemented**
	ISBLANK             | PHPExcel_Calculation_Functions::IS_BLANK
	ISERR               | PHPExcel_Calculation_Functions::IS_ERR
	ISERROR             | PHPExcel_Calculation_Functions::IS_ERROR
	ISEVEN              | PHPExcel_Calculation_Functions::IS_EVEN
	ISLOGICAL           | PHPExcel_Calculation_Functions::IS_LOGICAL
	ISNA                | PHPExcel_Calculation_Functions::IS_NA
	ISNONTEXT           | PHPExcel_Calculation_Functions::IS_NONTEXT
	ISNUMBER            | PHPExcel_Calculation_Functions::IS_NUMBER
	ISODD               | PHPExcel_Calculation_Functions::IS_ODD
	ISREF               | **\*\*\*  Not yet Implemented**
	ISTEXT              | PHPExcel_Calculation_Functions::IS_TEXT
	N                   | PHPExcel_Calculation_Functions::N
	NA                  | PHPExcel_Calculation_Functions::NA
	TYPE                | PHPExcel_Calculation_Functions::TYPE
	VERSION             | PHPExcel_Calculation_Functions::VERSION

## CATEGORY_LOGICAL

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	AND                 | PHPExcel_Calculation_Logical::LOGICAL_AND
	FALSE               | PHPExcel_Calculation_Logical::FALSE
	IF                  | PHPExcel_Calculation_Logical::STATEMENT_IF
	IFERROR             | PHPExcel_Calculation_Logical::IFERROR
	NOT                 | PHPExcel_Calculation_Logical::NOT
	OR                  | PHPExcel_Calculation_Logical::LOGICAL_OR
	TRUE                | PHPExcel_Calculation_Logical::TRUE

## CATEGORY_LOOKUP_AND_REFERENCE

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	ADDRESS             | PHPExcel_Calculation_LookupRef::CELL_ADDRESS
	AREAS               | **\*\*\*  Not yet Implemented**
	CHOOSE              | PHPExcel_Calculation_LookupRef::CHOOSE
	COLUMN              | PHPExcel_Calculation_LookupRef::COLUMN
	COLUMNS             | PHPExcel_Calculation_LookupRef::COLUMNS
	GETPIVOTDATA        | **\*\*\*  Not yet Implemented**
	HLOOKUP             | **\*\*\*  Not yet Implemented**
	HYPERLINK           | PHPExcel_Calculation_LookupRef::HYPERLINK
	INDEX               | PHPExcel_Calculation_LookupRef::INDEX
	INDIRECT            | PHPExcel_Calculation_LookupRef::INDIRECT
	LOOKUP              | PHPExcel_Calculation_LookupRef::LOOKUP
	MATCH               | PHPExcel_Calculation_LookupRef::MATCH
	OFFSET              | PHPExcel_Calculation_LookupRef::OFFSET
	ROW                 | PHPExcel_Calculation_LookupRef::ROW
	ROWS                | PHPExcel_Calculation_LookupRef::ROWS
	RTD                 | **\*\*\*  Not yet Implemented**
	TRANSPOSE           | PHPExcel_Calculation_LookupRef::TRANSPOSE
	VLOOKUP             | PHPExcel_Calculation_LookupRef::VLOOKUP

## CATEGORY_MATH_AND_TRIG

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	ABS                 | abs
	ACOS                | acos
	ACOSH               | acosh
	ASIN                | asin
	ASINH               | asinh
	ATAN                | atan
	ATAN2               | PHPExcel_Calculation_MathTrig::REVERSE_ATAN2
	ATANH               | atanh
	CEILING             | PHPExcel_Calculation_MathTrig::CEILING
	COMBIN              | PHPExcel_Calculation_MathTrig::COMBIN
	COS                 | cos
	COSH                | cosh
	DEGREES             | rad2deg
	EVEN                | PHPExcel_Calculation_MathTrig::EVEN
	EXP                 | exp
	FACT                | PHPExcel_Calculation_MathTrig::FACT
	FACTDOUBLE          | PHPExcel_Calculation_MathTrig::FACTDOUBLE
	FLOOR               | PHPExcel_Calculation_MathTrig::FLOOR
	GCD                 | PHPExcel_Calculation_MathTrig::GCD
	INT                 | PHPExcel_Calculation_MathTrig::INT
	LCM                 | PHPExcel_Calculation_MathTrig::LCM
	LN                  | log
	LOG                 | PHPExcel_Calculation_MathTrig::LOG_BASE
	LOG10               | log10
	MDETERM             | PHPExcel_Calculation_MathTrig::MDETERM
	MINVERSE            | PHPExcel_Calculation_MathTrig::MINVERSE
	MMULT               | PHPExcel_Calculation_MathTrig::MMULT
	MOD                 | PHPExcel_Calculation_MathTrig::MOD
	MROUND              | PHPExcel_Calculation_MathTrig::MROUND
	MULTINOMIAL         | PHPExcel_Calculation_MathTrig::MULTINOMIAL
	ODD                 | PHPExcel_Calculation_MathTrig::ODD
	PI                  | pi
	POWER               | PHPExcel_Calculation_MathTrig::POWER
	PRODUCT             | PHPExcel_Calculation_MathTrig::PRODUCT
	QUOTIENT            | PHPExcel_Calculation_MathTrig::QUOTIENT
	RADIANS             | deg2rad
	RAND                | PHPExcel_Calculation_MathTrig::RAND
	RANDBETWEEN         | PHPExcel_Calculation_MathTrig::RAND
	ROMAN               | PHPExcel_Calculation_MathTrig::ROMAN
	ROUND               | round
	ROUNDDOWN           | PHPExcel_Calculation_MathTrig::ROUNDDOWN
	ROUNDUP             | PHPExcel_Calculation_MathTrig::ROUNDUP
	SERIESSUM           | PHPExcel_Calculation_MathTrig::SERIESSUM
	SIGN                | PHPExcel_Calculation_MathTrig::SIGN
	SIN                 | sin
	SINH                | sinh
	SQRT                | sqrt
	SQRTPI              | PHPExcel_Calculation_MathTrig::SQRTPI
	SUBTOTAL            | PHPExcel_Calculation_MathTrig::SUBTOTAL
	SUM                 | PHPExcel_Calculation_MathTrig::SUM
	SUMIF               | PHPExcel_Calculation_MathTrig::SUMIF
	SUMIFS              | **\*\*\*  Not yet Implemented**
	SUMPRODUCT          | PHPExcel_Calculation_MathTrig::SUMPRODUCT
	SUMSQ               | PHPExcel_Calculation_MathTrig::SUMSQ
	SUMX2MY2            | PHPExcel_Calculation_MathTrig::SUMX2MY2
	SUMX2PY2            | PHPExcel_Calculation_MathTrig::SUMX2PY2
	SUMXMY2             | PHPExcel_Calculation_MathTrig::SUMXMY2
	TAN                 | tan
	TANH                | tanh
	TRUNC               | PHPExcel_Calculation_MathTrig::TRUNC

## CATEGORY_STATISTICAL

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	AVEDEV              | PHPExcel_Calculation_Statistical::AVEDEV
	AVERAGE             | PHPExcel_Calculation_Statistical::AVERAGE
	AVERAGEA            | PHPExcel_Calculation_Statistical::AVERAGEA
	AVERAGEIF           | PHPExcel_Calculation_Statistical::AVERAGEIF
	AVERAGEIFS          | **\*\*\*  Not yet Implemented**
	BETADIST            | PHPExcel_Calculation_Statistical::BETADIST
	BETAINV             | PHPExcel_Calculation_Statistical::BETAINV
	BINOMDIST           | PHPExcel_Calculation_Statistical::BINOMDIST
	CHIDIST             | PHPExcel_Calculation_Statistical::CHIDIST
	CHIINV              | PHPExcel_Calculation_Statistical::CHIINV
	CHITEST             | **\*\*\*  Not yet Implemented**
	CONFIDENCE          | PHPExcel_Calculation_Statistical::CONFIDENCE
	CORREL              | PHPExcel_Calculation_Statistical::CORREL
	COUNT               | PHPExcel_Calculation_Statistical::COUNT
	COUNTA              | PHPExcel_Calculation_Statistical::COUNTA
	COUNTBLANK          | PHPExcel_Calculation_Statistical::COUNTBLANK
	COUNTIF             | PHPExcel_Calculation_Statistical::COUNTIF
	COUNTIFS            | **\*\*\*  Not yet Implemented**
	COVAR               | PHPExcel_Calculation_Statistical::COVAR
	CRITBINOM           | PHPExcel_Calculation_Statistical::CRITBINOM
	DEVSQ               | PHPExcel_Calculation_Statistical::DEVSQ
	EXPONDIST           | PHPExcel_Calculation_Statistical::EXPONDIST
	FDIST               | **\*\*\*  Not yet Implemented**
	FINV                | **\*\*\*  Not yet Implemented**
	FISHER              | PHPExcel_Calculation_Statistical::FISHER
	FISHERINV           | PHPExcel_Calculation_Statistical::FISHERINV
	FORECAST            | PHPExcel_Calculation_Statistical::FORECAST
	FREQUENCY           | **\*\*\*  Not yet Implemented**
	FTEST               | **\*\*\*  Not yet Implemented**
	GAMMADIST           | PHPExcel_Calculation_Statistical::GAMMADIST
	GAMMAINV            | PHPExcel_Calculation_Statistical::GAMMAINV
	GAMMALN             | PHPExcel_Calculation_Statistical::GAMMALN
	GEOMEAN             | PHPExcel_Calculation_Statistical::GEOMEAN
	GROWTH              | PHPExcel_Calculation_Statistical::GROWTH
	HARMEAN             | PHPExcel_Calculation_Statistical::HARMEAN
	HYPGEOMDIST         | PHPExcel_Calculation_Statistical::HYPGEOMDIST
	INTERCEPT           | PHPExcel_Calculation_Statistical::INTERCEPT
	KURT                | PHPExcel_Calculation_Statistical::KURT
	LARGE               | PHPExcel_Calculation_Statistical::LARGE
	LINEST              | PHPExcel_Calculation_Statistical::LINEST
	LOGEST              | PHPExcel_Calculation_Statistical::LOGEST
	LOGINV              | PHPExcel_Calculation_Statistical::LOGINV
	LOGNORMDIST         | PHPExcel_Calculation_Statistical::LOGNORMDIST
	MAX                 | PHPExcel_Calculation_Statistical::MAX
	MAXA                | PHPExcel_Calculation_Statistical::MAXA
	MAXIF               | PHPExcel_Calculation_Statistical::MAXIF
	MEDIAN              | PHPExcel_Calculation_Statistical::MEDIAN
	MEDIANIF            | **\*\*\*  Not yet Implemented**
	MIN                 | PHPExcel_Calculation_Statistical::MIN
	MINA                | PHPExcel_Calculation_Statistical::MINA
	MINIF               | PHPExcel_Calculation_Statistical::MINIF
	MODE                | PHPExcel_Calculation_Statistical::MODE
	NEGBINOMDIST        | PHPExcel_Calculation_Statistical::NEGBINOMDIST
	NORMDIST            | PHPExcel_Calculation_Statistical::NORMDIST
	NORMINV             | PHPExcel_Calculation_Statistical::NORMINV
	NORMSDIST           | PHPExcel_Calculation_Statistical::NORMSDIST
	NORMSINV            | PHPExcel_Calculation_Statistical::NORMSINV
	PEARSON             | PHPExcel_Calculation_Statistical::CORREL
	PERCENTILE          | PHPExcel_Calculation_Statistical::PERCENTILE
	PERCENTRANK         | PHPExcel_Calculation_Statistical::PERCENTRANK
	PERMUT              | PHPExcel_Calculation_Statistical::PERMUT
	POISSON             | PHPExcel_Calculation_Statistical::POISSON
	PROB                | **\*\*\*  Not yet Implemented**
	QUARTILE            | PHPExcel_Calculation_Statistical::QUARTILE
	RANK                | PHPExcel_Calculation_Statistical::RANK
	RSQ                 | PHPExcel_Calculation_Statistical::RSQ
	SKEW                | PHPExcel_Calculation_Statistical::SKEW
	SLOPE               | PHPExcel_Calculation_Statistical::SLOPE
	SMALL               | PHPExcel_Calculation_Statistical::SMALL
	STANDARDIZE         | PHPExcel_Calculation_Statistical::STANDARDIZE
	STDEV               | PHPExcel_Calculation_Statistical::STDEV
	STDEVA              | PHPExcel_Calculation_Statistical::STDEVA
	STDEVP              | PHPExcel_Calculation_Statistical::STDEVP
	STDEVPA             | PHPExcel_Calculation_Statistical::STDEVPA
	STEYX               | PHPExcel_Calculation_Statistical::STEYX
	TDIST               | PHPExcel_Calculation_Statistical::TDIST
	TINV                | PHPExcel_Calculation_Statistical::TINV
	TREND               | PHPExcel_Calculation_Statistical::TREND
	TRIMMEAN            | PHPExcel_Calculation_Statistical::TRIMMEAN
	TTEST               | **\*\*\*  Not yet Implemented**
	VAR                 | PHPExcel_Calculation_Statistical::VARFunc
	VARA                | PHPExcel_Calculation_Statistical::VARA
	VARP                | PHPExcel_Calculation_Statistical::VARP
	VARPA               | PHPExcel_Calculation_Statistical::VARPA
	WEIBULL             | PHPExcel_Calculation_Statistical::WEIBULL
	ZTEST               | PHPExcel_Calculation_Statistical::ZTEST

## CATEGORY_TEXT_AND_DATA

    Excel Function      | PHPExcel Function
    --------------------|-------------------------------------------
	ASC                 | **\*\*\*  Not yet Implemented**
	BAHTTEXT            | **\*\*\*  Not yet Implemented**
	CHAR                | PHPExcel_Calculation_TextData::CHARACTER
	CLEAN               | PHPExcel_Calculation_TextData::TRIMNONPRINTABLE
	CODE                | PHPExcel_Calculation_TextData::ASCIICODE
	CONCATENATE         | PHPExcel_Calculation_TextData::CONCATENATE
	DOLLAR              | PHPExcel_Calculation_TextData::DOLLAR
	EXACT               | **\*\*\*  Not yet Implemented**
	FIND                | PHPExcel_Calculation_TextData::SEARCHSENSITIVE
	FINDB               | PHPExcel_Calculation_TextData::SEARCHSENSITIVE
	FIXED               | PHPExcel_Calculation_TextData::FIXEDFORMAT
	JIS                 | **\*\*\*  Not yet Implemented**
	LEFT                | PHPExcel_Calculation_TextData::LEFT
	LEFTB               | PHPExcel_Calculation_TextData::LEFT
	LEN                 | PHPExcel_Calculation_TextData::STRINGLENGTH
	LENB                | PHPExcel_Calculation_TextData::STRINGLENGTH
	LOWER               | PHPExcel_Calculation_TextData::LOWERCASE
	MID                 | PHPExcel_Calculation_TextData::MID
	MIDB                | PHPExcel_Calculation_TextData::MID
	PHONETIC            | **\*\*\*  Not yet Implemented**
	PROPER              | PHPExcel_Calculation_TextData::PROPERCASE
	REPLACE             | PHPExcel_Calculation_TextData::REPLACE
	REPLACEB            | PHPExcel_Calculation_TextData::REPLACE
	REPT                | str_repeat
	RIGHT               | PHPExcel_Calculation_TextData::RIGHT
	RIGHTB              | PHPExcel_Calculation_TextData::RIGHT
	SEARCH              | PHPExcel_Calculation_TextData::SEARCHINSENSITIVE
	SEARCHB             | PHPExcel_Calculation_TextData::SEARCHINSENSITIVE
	SUBSTITUTE          | PHPExcel_Calculation_TextData::SUBSTITUTE
	T                   | PHPExcel_Calculation_TextData::RETURNSTRING
	TEXT                | PHPExcel_Calculation_TextData::TEXTFORMAT
	TRIM                | PHPExcel_Calculation_TextData::TRIMSPACES
	UPPER               | PHPExcel_Calculation_TextData::UPPERCASE
	VALUE               | **\*\*\*  Not yet Implemented**

