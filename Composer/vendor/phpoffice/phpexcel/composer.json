{"name": "phpoffice/phpexcel", "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "keywords": ["PHP", "Excel", "OpenXML", "xlsx", "xls", "spreadsheet"], "homepage": "https://github.com/PHPOffice/PHPExcel", "type": "library", "license": "LGPL-2.1", "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "http://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rootslabs.net"}, {"name": "<PERSON>"}], "require": {"php": "^5.2|^7.0", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*"}, "require-dev": {"squizlabs/php_codesniffer": "2.*"}, "recommend": {"ext-zip": "*", "ext-gd": "*"}, "autoload": {"psr-0": {"PHPExcel": "Classes/"}}}