<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit340c5de09bddf7888e5fb4070f5ebd87
{
    public static $prefixesPsr0 = array (
        'P' => 
        array (
            'PHPExcel' => 
            array (
                0 => __DIR__ . '/..' . '/phpoffice/phpexcel/Classes',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixesPsr0 = ComposerStaticInit340c5de09bddf7888e5fb4070f5ebd87::$prefixesPsr0;
            $loader->classMap = ComposerStaticInit340c5de09bddf7888e5fb4070f5ebd87::$classMap;

        }, null, ClassLoader::class);
    }
}
